/**
 * Simplified Auth Client
 * Uses Supabase Auth for reliable authentication
 */

import { createClient } from '@supabase/supabase-js';

export interface User {
  id: string;
  email: string;
  name?: string;
  emailVerified: boolean;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Session {
  id: string;
  userId: string;
  expiresAt: Date;
  token: string;
  user: User;
}

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase configuration. Please check your environment variables.');
}

const supabase = createClient(supabaseUrl || '', supabaseAnonKey || '');

export const authClient = {
  signIn: async (email: string, password: string) => {
    const response = await fetch(`${API_BASE}/auth?action=signin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({ email, password }),
    });
    
    const data = await response.json();
    if (data.success && data.token) {
      localStorage.setItem('auth_token', data.token);
      localStorage.setItem('user_data', JSON.stringify(data.user));
    }
    return data;
  },

  signUp: async (email: string, password: string, name: string) => {
    const response = await fetch(`${API_BASE}/auth?action=signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({ email, password, name }),
    });
    
    const data = await response.json();
    if (data.success && data.token) {
      localStorage.setItem('auth_token', data.token);
      localStorage.setItem('user_data', JSON.stringify(data.user));
    }
    return data;
  },

  signOut: async () => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      try {
        await fetch(`${API_BASE}/auth?action=signout`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ token }),
        });
      } catch (error) {
        console.error('Signout error:', error);
      }
    }
    
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    return { success: true };
  },

  getSession: async () => {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      return { success: false, error: 'No token found' };
    }

    try {
      const response = await fetch(`${API_BASE}/auth?action=session&token=${encodeURIComponent(token)}`, {
        credentials: 'include'
      });
      const data = await response.json();
      
      if (data.success && data.user) {
        localStorage.setItem('user_data', JSON.stringify(data.user));
        return data;
      } else {
        // Token is invalid, clear it
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        return { success: false, error: 'Invalid session' };
      }
    } catch (error) {
      console.error('Session validation error:', error);
      return { success: false, error: 'Session validation failed' };
    }
  },

  updateUser: async (data: Partial<User>) => {
    console.log('updateUser not implemented yet');
    return { data: { user: { ...data } }, error: null };
  },
  changePassword: async (data: { currentPassword: string; newPassword: string }) => {
    console.log('changePassword not implemented yet');
    return { data: null, error: null };
  },
  resetPassword: async (data: { email: string }) => {
    console.log('resetPassword not implemented yet');
    return { data: null, error: null };
  },
  sendVerificationEmail: async (data: { email: string }) => {
    console.log('sendVerificationEmail not implemented yet');
    return { data: null, error: null };
  },
  verifyEmail: async (data: { token: string }) => {
    console.log('verifyEmail not implemented yet');
    return { data: null, error: null };
  }
};

// Export individual methods for easier use
export const signIn = authClient.signIn.email;
export const signUp = authClient.signUp.email;
export const signOut = authClient.signOut;
export const getSession = authClient.getSession;
export const updateUser = authClient.updateUser;
export const changePassword = authClient.changePassword;
export const resetPassword = authClient.resetPassword;
export const sendVerificationEmail = authClient.sendVerificationEmail;
export const verifyEmail = authClient.verifyEmail;