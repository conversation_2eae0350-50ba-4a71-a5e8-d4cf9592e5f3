import{r as e,j as t}from"./react-core-B9nwsbCA.js";import{c as r,p as a}from"./state-4gBW_4Nx.js";import{u as i}from"./main-1loL2DJY.js";import{I as s}from"./utils-D7twGpBa.js";const n=r()(a(((e,t)=>({watchHistory:{},likedVideos:[],watchLater:[],viewedCategories:{},isLoading:!1,error:null,updateWatchProgress:(t,r,a)=>{const i=Math.min(Math.round(r/a*100),100),s=i>=90;e((e=>({watchHistory:{...e.watchHistory,[t]:{currentTime:r,duration:a,percent:i,lastWatched:(new Date).toISOString(),completed:s}}})));(async()=>{const{data:{user:e}}=await supabase.auth.getUser();if(e){const{error:n}=await supabase.from("watch_history").upsert({user_id:e.id,video_id:t,playback_position:r,duration:a,percent:i,last_watched:(new Date).toISOString(),completed:s},{onConflict:"user_id,video_id"})}})()},getWatchProgress:e=>t().watchHistory[e]||null,getContinueWatchingVideos:async()=>{e({isLoading:!0,error:null});try{const{data:{user:r}}=await supabase.auth.getUser();if(!r){const r=t().watchHistory,a=new Date;a.setDate(a.getDate()-30);const i=Object.entries(r).filter((([e,t])=>{const r=new Date(t.lastWatched);return!t.completed&&r>a})).sort((([e,t],[r,a])=>new Date(a.lastWatched).getTime()-new Date(t.lastWatched).getTime())).map((([e,t])=>e));return e({isLoading:!1}),i}const{data:a,error:i}=await supabase.from("watch_history").select("video_id, last_watched, completed, percent").eq("user_id",r.id).eq("completed",!1).gt("percent",5).lt("percent",90).order("last_watched",{ascending:!1}).limit(10);if(i)throw new Error(i.message);const s={...t().watchHistory};return a.forEach((e=>{s[e.video_id]={currentTime:0,duration:0,percent:e.percent,lastWatched:e.last_watched,completed:e.completed}})),e({watchHistory:s,isLoading:!1}),a.map((e=>e.video_id))}catch(r){return e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1}),[]}},likeVideo:async t=>{e({isLoading:!0,error:null});try{const{data:{user:r}}=await supabase.auth.getUser();if(!r)return e((e=>({likedVideos:[...e.likedVideos,t],isLoading:!1}))),!0;const{error:a}=await supabase.from("video_likes").insert({user_id:r.id,video_id:t,liked_at:(new Date).toISOString()});if(a)throw new Error(a.message);return await supabase.rpc("increment_video_likes",{video_id:t}),e((e=>({likedVideos:[...e.likedVideos,t],isLoading:!1}))),!0}catch(r){return e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1}),!1}},unlikeVideo:async t=>{e({isLoading:!0,error:null});try{const{data:{user:r}}=await supabase.auth.getUser();if(!r)return e((e=>({likedVideos:e.likedVideos.filter((e=>e!==t)),isLoading:!1}))),!0;const{error:a}=await supabase.from("video_likes").delete().eq("user_id",r.id).eq("video_id",t);if(a)throw new Error(a.message);return await supabase.rpc("decrement_video_likes",{video_id:t}),e((e=>({likedVideos:e.likedVideos.filter((e=>e!==t)),isLoading:!1}))),!0}catch(r){return e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1}),!1}},isVideoLiked:e=>t().likedVideos.includes(e),addToWatchLater:t=>{e((e=>({watchLater:[...e.watchLater,t]})));(async()=>{const{data:{user:e}}=await supabase.auth.getUser();if(e){const{error:r}=await supabase.from("watch_later").insert({user_id:e.id,video_id:t,added_at:(new Date).toISOString()})}})()},removeFromWatchLater:t=>{e((e=>({watchLater:e.watchLater.filter((e=>e!==t))})));(async()=>{const{data:{user:e}}=await supabase.auth.getUser();if(e){const{error:r}=await supabase.from("watch_later").delete().eq("user_id",e.id).eq("video_id",t)}})()},isInWatchLater:e=>t().watchLater.includes(e),trackCategoryView:t=>{t&&e((e=>{const r={...e.viewedCategories};return r[t]=(r[t]||0)+1,{viewedCategories:r}}))},getMostViewedCategories:(e=5)=>{const r=t().viewedCategories;return Object.entries(r).sort((([e,t],[r,a])=>a-t)).slice(0,e).map((([e,t])=>e))}})),{name:"user-preferences-storage",partialize:e=>({watchHistory:e.watchHistory,likedVideos:e.likedVideos,watchLater:e.watchLater,viewedCategories:e.viewedCategories})})),o=e.memo((({video:r,onBack:a})=>{const[o,d]=e.useState(!1),[c,l]=e.useState(0),[u,m]=e.useState(r.duration||0),[w,v]=e.useState(s()),[h,g]=e.useState(!1),[p,f]=e.useState(!1),L=e.useRef(null),k=e.useRef(null),b=e.useRef(!1),{updateWatchProgress:E,getWatchProgress:_}=n(),{incrementVideoViews:y}=i();return e.useEffect((()=>{const e=navigator.userAgent||navigator.vendor||window.opera;f(/iPad|iPhone|iPod/.test(e)&&!window.MSStream),g(/Android/i.test(e))}),[]),e.useEffect((()=>{const e=()=>{v(s())};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[]),e.useEffect((()=>{const e=()=>{L.current&&!isInFullscreen()&&L.current.paused};return document.addEventListener("fullscreenchange",e),document.addEventListener("webkitfullscreenchange",e),document.addEventListener("mozfullscreenchange",e),document.addEventListener("MSFullscreenChange",e),()=>{document.removeEventListener("fullscreenchange",e),document.removeEventListener("webkitfullscreenchange",e),document.removeEventListener("mozfullscreenchange",e),document.removeEventListener("MSFullscreenChange",e)}}),[]),e.useEffect((()=>{if(!L.current)return;const e=()=>{if(L.current){const e=L.current.currentTime,t=L.current.duration,a=e/t*100;l(e),m(t),Math.floor(e)%5==0&&E(r.id,e,t),a>=10&&!b.current&&(y(r.id),b.current=!0)}},t=()=>d(!0),a=()=>d(!1),i=()=>{d(!1),u>0&&E(r.id,u,u)},s=()=>{if(L.current){m(L.current.duration);const e=_(r.id);e&&!e.completed&&(L.current.currentTime=e.currentTime,l(e.currentTime))}},n=()=>{h&&L.current&&(isInFullscreen()?exitFullscreen():requestFullscreen(L.current))},o=L.current;return o.addEventListener("timeupdate",e),o.addEventListener("play",t),o.addEventListener("pause",a),o.addEventListener("ended",i),o.addEventListener("loadedmetadata",s),h&&o.addEventListener("dblclick",n),p&&(o.addEventListener("webkitbeginfullscreen",(()=>{})),o.addEventListener("webkitendfullscreen",(()=>{}))),()=>{o.removeEventListener("timeupdate",e),o.removeEventListener("play",t),o.removeEventListener("pause",a),o.removeEventListener("ended",i),o.removeEventListener("loadedmetadata",s),h&&o.removeEventListener("dblclick",n),p&&(o.removeEventListener("webkitbeginfullscreen",(()=>{})),o.removeEventListener("webkitendfullscreen",(()=>{}))),u>0&&E(r.id,c,u)}}),[r.id,E,y,_,c,u,p,h]),t.jsxs("div",{className:"bg-black video-player-container relative z-10",children:[t.jsx("div",{ref:k,className:"relative aspect-video max-h-[70vh] bg-black video-container",children:t.jsx("video",{ref:L,className:"w-full h-full object-contain bg-black native-video-player",src:r.videoUrl,poster:r.thumbnailUrl,controls:!0,playsInline:!0,"webkit-playsinline":"true","x-webkit-airplay":"allow",preload:"metadata",autoPlay:!0,controlsList:"nodownload",disablePictureInPicture:w})}),a&&t.jsx("div",{className:"mt-4 flex justify-start",children:t.jsxs("button",{className:"bg-blue-700 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors flex items-center",onClick:a,"aria-label":"Back to previous page",children:[t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-2",children:t.jsx("path",{d:"M19 12H5M12 19l-7-7 7-7"})}),"Back"]})})]})}));export{o as N};
