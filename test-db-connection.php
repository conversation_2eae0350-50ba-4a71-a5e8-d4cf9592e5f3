<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=bluerpcm_bluefilmx', 'bluerpcm_dbuser', 'kingpatrick100');
    echo "Database connection successful\n";
    
    // Check if profiles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'profiles'");
    if ($stmt->rowCount() > 0) {
        echo "Profiles table exists\n";
        
        // Check table structure
        $stmt = $pdo->query("DESCRIBE profiles");
        echo "Profiles table structure:\n";
        while ($row = $stmt->fetch()) {
            echo "- {$row['Field']} ({$row['Type']})\n";
        }
        
        // Count profiles
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM profiles");
        $count = $stmt->fetch()['count'];
        echo "Total profiles: $count\n";
    } else {
        echo "Profiles table does NOT exist\n";
        echo "Available tables:\n";
        $stmt = $pdo->query("SHOW TABLES");
        while ($row = $stmt->fetch()) {
            echo "- " . array_values($row)[0] . "\n";
        }
    }
    
} catch(Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>