<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Domain Consistency Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #2a2a2a;
        }
        .success {
            border-color: #4ade80;
            background-color: #1a2e1a;
        }
        .error {
            border-color: #ef4444;
            background-color: #2e1a1a;
        }
        .loading {
            border-color: #fbbf24;
            background-color: #2e2a1a;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
        pre {
            background-color: #111;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .domain-test {
            border: 1px solid #444;
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🌐 Domain Consistency Test</h1>
    <p>This page tests both www.bluefilmx.com and bluefilmx.com to ensure they serve the same content.</p>

    <div class="test-section">
        <h3>Test 1: Version Comparison</h3>
        <button onclick="testVersions()">Compare Versions</button>
        <div id="version-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: JavaScript File Comparison</h3>
        <button onclick="testJavaScriptFiles()">Compare JS Files</button>
        <div id="js-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Search API Test on Both Domains</h3>
        <button onclick="testSearchOnBothDomains()">Test Search APIs</button>
        <div id="search-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 4: Cache Headers Check</h3>
        <button onclick="testCacheHeaders()">Check Cache Headers</button>
        <div id="cache-result"></div>
    </div>

    <script>
        function setResult(elementId, message, type = 'loading') {
            const element = document.getElementById(elementId);
            element.className = `test-section ${type}`;
            element.innerHTML = message;
        }

        async function testVersions() {
            setResult('version-result', 'Comparing versions...', 'loading');
            
            try {
                const [wwwResponse, nonWwwResponse] = await Promise.all([
                    fetch('https://www.bluefilmx.com/version.json?t=' + Date.now()),
                    fetch('https://bluefilmx.com/version.json?t=' + Date.now())
                ]);

                const wwwVersion = await wwwResponse.json();
                const nonWwwVersion = await nonWwwResponse.json();

                const isConsistent = wwwVersion.buildId === nonWwwVersion.buildId;

                setResult('version-result', `
                    <div class="comparison">
                        <div class="domain-test">
                            <h4>www.bluefilmx.com</h4>
                            <pre>${JSON.stringify(wwwVersion, null, 2)}</pre>
                        </div>
                        <div class="domain-test">
                            <h4>bluefilmx.com</h4>
                            <pre>${JSON.stringify(nonWwwVersion, null, 2)}</pre>
                        </div>
                    </div>
                    <p><strong>${isConsistent ? '✅ Versions Match!' : '❌ Version Mismatch!'}</strong></p>
                `, isConsistent ? 'success' : 'error');

            } catch (error) {
                setResult('version-result', `
                    <strong>❌ Error comparing versions:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        async function testJavaScriptFiles() {
            setResult('js-result', 'Comparing JavaScript files...', 'loading');
            
            try {
                // Get the main JS file from both domains
                const [wwwResponse, nonWwwResponse] = await Promise.all([
                    fetch('https://www.bluefilmx.com/assets/main-CLSQ1n6z.js?t=' + Date.now(), { method: 'HEAD' }),
                    fetch('https://bluefilmx.com/assets/main-CLSQ1n6z.js?t=' + Date.now(), { method: 'HEAD' })
                ]);

                const wwwLastModified = wwwResponse.headers.get('last-modified');
                const nonWwwLastModified = nonWwwResponse.headers.get('last-modified');
                const wwwSize = wwwResponse.headers.get('content-length');
                const nonWwwSize = nonWwwResponse.headers.get('content-length');

                const isConsistent = wwwLastModified === nonWwwLastModified && wwwSize === nonWwwSize;

                setResult('js-result', `
                    <div class="comparison">
                        <div class="domain-test">
                            <h4>www.bluefilmx.com</h4>
                            <p>Status: ${wwwResponse.status}</p>
                            <p>Last Modified: ${wwwLastModified}</p>
                            <p>Size: ${wwwSize} bytes</p>
                        </div>
                        <div class="domain-test">
                            <h4>bluefilmx.com</h4>
                            <p>Status: ${nonWwwResponse.status}</p>
                            <p>Last Modified: ${nonWwwLastModified}</p>
                            <p>Size: ${nonWwwSize} bytes</p>
                        </div>
                    </div>
                    <p><strong>${isConsistent ? '✅ JS Files Match!' : '❌ JS Files Different!'}</strong></p>
                `, isConsistent ? 'success' : 'error');

            } catch (error) {
                setResult('js-result', `
                    <strong>❌ Error comparing JS files:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        async function testSearchOnBothDomains() {
            setResult('search-result', 'Testing search on both domains...', 'loading');
            
            try {
                const searchTerm = 'sugar';
                const [wwwResponse, nonWwwResponse] = await Promise.all([
                    fetch(`https://www.bluefilmx.com/api/videos.php?search=${searchTerm}&limit=3`, {
                        credentials: 'include',
                        headers: { 'Content-Type': 'application/json' }
                    }),
                    fetch(`https://bluefilmx.com/api/videos.php?search=${searchTerm}&limit=3`, {
                        credentials: 'include',
                        headers: { 'Content-Type': 'application/json' }
                    })
                ]);

                const wwwData = await wwwResponse.json();
                const nonWwwData = await nonWwwResponse.json();

                const wwwCount = wwwData.data?.videos?.length || 0;
                const nonWwwCount = nonWwwData.data?.videos?.length || 0;
                const isConsistent = wwwCount === nonWwwCount && wwwResponse.status === nonWwwResponse.status;

                setResult('search-result', `
                    <div class="comparison">
                        <div class="domain-test">
                            <h4>www.bluefilmx.com API</h4>
                            <p>Status: ${wwwResponse.status}</p>
                            <p>Results: ${wwwCount}</p>
                            <p>Success: ${wwwData.success ? 'Yes' : 'No'}</p>
                        </div>
                        <div class="domain-test">
                            <h4>bluefilmx.com API</h4>
                            <p>Status: ${nonWwwResponse.status}</p>
                            <p>Results: ${nonWwwCount}</p>
                            <p>Success: ${nonWwwData.success ? 'Yes' : 'No'}</p>
                        </div>
                    </div>
                    <p><strong>${isConsistent ? '✅ Search APIs Consistent!' : '❌ Search APIs Different!'}</strong></p>
                `, isConsistent ? 'success' : 'error');

            } catch (error) {
                setResult('search-result', `
                    <strong>❌ Error testing search APIs:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        async function testCacheHeaders() {
            setResult('cache-result', 'Checking cache headers...', 'loading');
            
            try {
                const [wwwResponse, nonWwwResponse] = await Promise.all([
                    fetch('https://www.bluefilmx.com/?t=' + Date.now(), { method: 'HEAD' }),
                    fetch('https://bluefilmx.com/?t=' + Date.now(), { method: 'HEAD' })
                ]);

                const wwwCacheControl = wwwResponse.headers.get('cache-control');
                const nonWwwCacheControl = nonWwwResponse.headers.get('cache-control');

                setResult('cache-result', `
                    <div class="comparison">
                        <div class="domain-test">
                            <h4>www.bluefilmx.com</h4>
                            <p>Cache-Control: ${wwwCacheControl || 'Not set'}</p>
                            <p>Pragma: ${wwwResponse.headers.get('pragma') || 'Not set'}</p>
                            <p>Expires: ${wwwResponse.headers.get('expires') || 'Not set'}</p>
                        </div>
                        <div class="domain-test">
                            <h4>bluefilmx.com</h4>
                            <p>Cache-Control: ${nonWwwCacheControl || 'Not set'}</p>
                            <p>Pragma: ${nonWwwResponse.headers.get('pragma') || 'Not set'}</p>
                            <p>Expires: ${nonWwwResponse.headers.get('expires') || 'Not set'}</p>
                        </div>
                    </div>
                    <p><strong>Cache headers checked</strong></p>
                `, 'success');

            } catch (error) {
                setResult('cache-result', `
                    <strong>❌ Error checking cache headers:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        // Auto-run version test on page load
        window.addEventListener('load', () => {
            console.log('🧪 Domain Consistency Test Page Loaded');
            testVersions();
        });
    </script>
</body>
</html>
