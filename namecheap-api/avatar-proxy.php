<?php
/**
 * Avatar Proxy Endpoint
 * Proxies requests to DiceBear API to avoid CORS issues
 */

header('Content-Type: image/svg+xml');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Cache-Control: public, max-age=86400'); // Cache for 24 hours

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get the avatar URL from query parameters
$avatarUrl = $_GET['url'] ?? '';

// Validate the URL
if (empty($avatarUrl)) {
    http_response_code(400);
    echo json_encode(['error' => 'Avatar URL is required']);
    exit;
}

// Ensure the URL is from DiceBear
if (!str_starts_with($avatarUrl, 'https://avatars.dicebear.com/')) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid avatar URL']);
    exit;
}

try {
    // Initialize cURL
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $avatarUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_USERAGENT => 'BlueFilmX Avatar Proxy/1.0',
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_SSL_VERIFYHOST => 2
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    
    if (curl_errno($ch)) {
        throw new Exception('cURL error: ' . curl_error($ch));
    }
    
    curl_close($ch);
    
    // Check if the request was successful
    if ($httpCode !== 200) {
        http_response_code($httpCode);
        echo json_encode(['error' => 'Failed to fetch avatar']);
        exit;
    }
    
    // Ensure we got an SVG
    if (!str_contains($contentType, 'image/svg') && !str_contains($response, '<svg')) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid avatar format']);
        exit;
    }
    
    // Output the SVG content
    echo $response;
    
} catch (Exception $e) {
    error_log('Avatar proxy error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>