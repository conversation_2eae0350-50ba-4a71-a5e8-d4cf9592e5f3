<?php
/**
 * Video Transcoding Service
 * Converts uploaded videos to web-compatible formats using FFmpeg
 */

require_once 'config/database.php';

// Set CORS headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Start session
if (!isset($_SESSION)) {
    session_start();
}

// Helper functions
function requireAuth() {
    $userId = $_SESSION['user_id'] ?? null;
    if (!$userId) {
        http_response_code(401);
        echo json_encode(['success' => false, 'error' => 'Authentication required']);
        exit();
    }
    return $userId;
}

function successResponse($data = null, $message = 'Success') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
}

function errorResponse($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'error' => $message
    ]);
}

/**
 * Check if FFmpeg is available
 */
function checkFFmpegAvailable() {
    $output = [];
    $returnCode = 0;
    exec('ffmpeg -version 2>&1', $output, $returnCode);
    return $returnCode === 0;
}

/**
 * Get video information using FFprobe
 */
function getVideoInfo($filePath) {
    $command = "ffprobe -v quiet -print_format json -show_format -show_streams " . escapeshellarg($filePath);
    $output = shell_exec($command);
    
    if (!$output) {
        return null;
    }
    
    $info = json_decode($output, true);
    if (!$info) {
        return null;
    }
    
    // Extract video stream info
    $videoStream = null;
    foreach ($info['streams'] as $stream) {
        if ($stream['codec_type'] === 'video') {
            $videoStream = $stream;
            break;
        }
    }
    
    return [
        'format' => $info['format'],
        'video_stream' => $videoStream,
        'duration' => floatval($info['format']['duration'] ?? 0),
        'size' => intval($info['format']['size'] ?? 0)
    ];
}

/**
 * Transcode video to web-compatible format
 */
function transcodeVideo($inputPath, $outputPath) {
    // Web-compatible encoding settings
    $command = sprintf(
        'ffmpeg -i %s -c:v libx264 -profile:v baseline -level 3.0 -pix_fmt yuv420p -c:a aac -b:a 128k -movflags +faststart -y %s 2>&1',
        escapeshellarg($inputPath),
        escapeshellarg($outputPath)
    );
    
    $output = [];
    $returnCode = 0;
    exec($command, $output, $returnCode);
    
    return [
        'success' => $returnCode === 0,
        'output' => implode("\n", $output),
        'return_code' => $returnCode
    ];
}

/**
 * Generate thumbnail from video
 */
function generateThumbnail($videoPath, $thumbnailPath, $timeOffset = 1) {
    $command = sprintf(
        'ffmpeg -i %s -ss %d -vframes 1 -q:v 2 -y %s 2>&1',
        escapeshellarg($videoPath),
        $timeOffset,
        escapeshellarg($thumbnailPath)
    );
    
    $output = [];
    $returnCode = 0;
    exec($command, $output, $returnCode);
    
    return [
        'success' => $returnCode === 0,
        'output' => implode("\n", $output),
        'return_code' => $returnCode
    ];
}

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    // Check transcoding service status
    $ffmpegAvailable = checkFFmpegAvailable();
    
    successResponse([
        'ffmpeg_available' => $ffmpegAvailable,
        'service_status' => $ffmpegAvailable ? 'ready' : 'ffmpeg_not_available',
        'supported_formats' => ['mp4', 'mov', 'avi', 'mkv', 'webm', 'flv', 'wmv', '3gp']
    ], 'Transcoding service status');
    
} elseif ($method === 'POST') {
    // Transcode video
    $userId = requireAuth();
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['video_path'])) {
        errorResponse('Video path is required');
        exit();
    }
    
    $videoPath = $input['video_path'];
    $generateThumb = $input['generate_thumbnail'] ?? true;
    
    // Validate input file exists
    if (!file_exists($videoPath)) {
        errorResponse('Video file not found');
        exit();
    }
    
    // Check if FFmpeg is available
    if (!checkFFmpegAvailable()) {
        errorResponse('Video transcoding service not available (FFmpeg not installed)');
        exit();
    }
    
    try {
        // Get video info
        $videoInfo = getVideoInfo($videoPath);
        if (!$videoInfo) {
            errorResponse('Could not analyze video file');
            exit();
        }
        
        // Generate output paths
        $pathInfo = pathinfo($videoPath);
        $outputPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_transcoded.mp4';
        $thumbnailPath = null;
        
        if ($generateThumb) {
            $thumbnailPath = str_replace('/videos/', '/thumbnails/', $pathInfo['dirname']) . '/' . $pathInfo['filename'] . '_thumb.jpg';
            
            // Ensure thumbnails directory exists
            $thumbnailDir = dirname($thumbnailPath);
            if (!is_dir($thumbnailDir)) {
                mkdir($thumbnailDir, 0755, true);
            }
        }
        
        // Check if video needs transcoding
        $needsTranscoding = false;
        $videoStream = $videoInfo['video_stream'];
        
        if ($videoStream) {
            // Check codec compatibility
            $codec = $videoStream['codec_name'] ?? '';
            $profile = $videoStream['profile'] ?? '';
            $pixelFormat = $videoStream['pix_fmt'] ?? '';
            
            // Needs transcoding if:
            // - Not H.264 codec
            // - Not baseline/main/high profile
            // - Not yuv420p pixel format
            // - Has compatibility issues
            if ($codec !== 'h264' || 
                !in_array($profile, ['Baseline', 'Main', 'High']) ||
                $pixelFormat !== 'yuv420p') {
                $needsTranscoding = true;
            }
        } else {
            $needsTranscoding = true; // No video stream found
        }
        
        $transcodeResult = null;
        $finalVideoPath = $videoPath;
        
        if ($needsTranscoding) {
            // Transcode video
            $transcodeResult = transcodeVideo($videoPath, $outputPath);
            
            if ($transcodeResult['success']) {
                // Replace original with transcoded version
                if (file_exists($outputPath)) {
                    unlink($videoPath); // Delete original
                    rename($outputPath, $videoPath); // Rename transcoded to original name
                    $finalVideoPath = $videoPath;
                }
            } else {
                errorResponse('Video transcoding failed: ' . $transcodeResult['output']);
                exit();
            }
        }
        
        // Generate thumbnail
        $thumbnailResult = null;
        if ($generateThumb && $thumbnailPath) {
            $thumbnailResult = generateThumbnail($finalVideoPath, $thumbnailPath);
        }
        
        // Get final video info
        $finalVideoInfo = getVideoInfo($finalVideoPath);
        
        successResponse([
            'video_path' => $finalVideoPath,
            'thumbnail_path' => $thumbnailPath && file_exists($thumbnailPath) ? $thumbnailPath : null,
            'transcoded' => $needsTranscoding,
            'transcode_result' => $transcodeResult,
            'thumbnail_result' => $thumbnailResult,
            'original_info' => $videoInfo,
            'final_info' => $finalVideoInfo,
            'duration' => $finalVideoInfo['duration'] ?? 0,
            'file_size' => filesize($finalVideoPath)
        ], 'Video processing completed');
        
    } catch (Exception $e) {
        error_log("Video transcoding error: " . $e->getMessage());
        errorResponse('Video processing failed: ' . $e->getMessage(), 500);
    }
    
} else {
    errorResponse('Method not allowed', 405);
}
?>
