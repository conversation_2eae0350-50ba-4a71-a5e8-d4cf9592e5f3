# Enhanced Authentication System

A comprehensive, secure authentication system built for modern web applications with advanced security features, JWT tokens, email verification, and role-based access control.

## Features

### 🔐 Security Features
- **JWT Token Authentication** - Stateless authentication with secure token management
- **Password Hashing** - Argon2ID hashing algorithm for maximum security
- **Rate Limiting** - Prevents brute force attacks and API abuse
- **Email Verification** - Secure email verification with expiring tokens
- **Password Reset** - Secure password reset flow with time-limited tokens
- **CSRF Protection** - Cross-site request forgery protection
- **XSS Prevention** - Input sanitization and output encoding
- **SQL Injection Protection** - Prepared statements and input validation
- **Security Headers** - Comprehensive security headers implementation

### 👤 User Management
- **User Registration** - Comprehensive user registration with validation
- **User Profiles** - Detailed user profiles with customizable settings
- **Role-Based Access Control** - Flexible permission system
- **Account Management** - Profile updates, password changes, account deletion
- **Session Management** - Secure session handling with automatic cleanup

### 📧 Email System
- **Email Verification** - Automated email verification process
- **Password Reset Emails** - Secure password reset notifications
- **Welcome Emails** - User onboarding emails
- **Security Notifications** - Account security change notifications
- **Template System** - Customizable email templates

### 🛡️ Advanced Security
- **Suspicious Activity Detection** - Automatic detection of malicious patterns
- **IP Blocking** - Automatic IP blocking for repeated violations
- **Security Logging** - Comprehensive security event logging
- **File Upload Security** - Secure file upload validation
- **Data Encryption** - Sensitive data encryption capabilities

## Directory Structure

```
auth-system/
├── backend/
│   ├── api/
│   │   └── auth.php              # Main authentication API endpoint
│   ├── config/
│   │   └── Database.php          # Database configuration and helpers
│   ├── services/
│   │   └── AuthService.php       # Core authentication service
│   └── utils/
│       ├── JWTHelper.php         # JWT token management
│       ├── RateLimiter.php       # Rate limiting functionality
│       ├── SecurityHelper.php    # Security utilities
│       └── EmailService.php      # Email service
├── frontend/
│   ├── stores/
│   │   └── authStore.ts          # Zustand authentication store
│   ├── lib/
│   │   └── api.ts                # API client
│   └── components/
│       └── AuthModal.tsx         # Authentication UI component
├── database/
│   └── auth-schema.sql           # Database schema
└── README.md                     # This file
```

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Node.js 16 or higher (for frontend)
- Composer (for PHP dependencies)
- Web server (Apache/Nginx)

### Backend Setup

1. **Database Setup**
   ```sql
   -- Import the database schema
   mysql -u your_username -p your_database < auth-system/database/auth-schema.sql
   ```

2. **Environment Configuration**
   Create a `.env` file in your project root:
   ```env
   # Database Configuration
   DB_HOST=localhost
   DB_NAME=your_database
   DB_USER=your_username
   DB_PASS=your_password
   
   # JWT Configuration
   JWT_SECRET=your-super-secret-jwt-key-change-this
   JWT_EXPIRY=86400
   
   # Email Configuration
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=your-app-password
   SMTP_ENCRYPTION=tls
   FROM_EMAIL=<EMAIL>
   FROM_NAME=Your App Name
   
   # Application Configuration
   BASE_URL=https://yourdomain.com
   ENCRYPTION_KEY=your-encryption-key-32-chars
   ```

3. **File Permissions**
   ```bash
   chmod 755 auth-system/backend/
   chmod 644 auth-system/backend/api/auth.php
   mkdir auth-system/backend/logs
   chmod 755 auth-system/backend/logs
   ```

4. **Install PHP Dependencies** (Optional - for PHPMailer)
   ```bash
   composer require phpmailer/phpmailer
   ```

### Frontend Setup

1. **Install Dependencies**
   ```bash
   npm install zustand lucide-react
   ```

2. **Environment Configuration**
   Create a `.env.local` file:
   ```env
   NEXT_PUBLIC_API_URL=https://yourdomain.com/api
   ```

3. **Integration**
   ```typescript
   // In your main app component
   import { useAuthStore } from './auth-system/frontend/stores/authStore';
   import AuthModal from './auth-system/frontend/components/AuthModal';
   
   function App() {
     const { isAuthenticated, user } = useAuthStore();
     
     return (
       <div>
         {isAuthenticated ? (
           <div>Welcome, {user?.username}!</div>
         ) : (
           <AuthModal isOpen={true} onClose={() => {}} />
         )}
       </div>
     );
   }
   ```

## Migration from Old System

### 1. Backup Current Data
```sql
-- Backup existing users
CREATE TABLE users_backup AS SELECT * FROM profiles;
```

### 2. Run Migration Script
```sql
-- Migrate existing users to new schema
INSERT INTO users (id, username, email, password_hash, is_active, created_at)
SELECT id, username, username as email, password_hash, 1, created_at
FROM profiles
WHERE username IS NOT NULL;

-- Assign default role to existing users
INSERT INTO user_roles (user_id, role_id)
SELECT id, 2 FROM users; -- 2 = user role
```

### 3. Update API Endpoints
Replace old authentication calls:
```php
// Old way
require_once 'config/database.php';
$user = getCurrentUser();

// New way
require_once 'auth-system/backend/config/Database.php';
$user = AuthHelper::getCurrentUser();
```

### 4. Update Frontend
```typescript
// Old way
import { useAuthStore } from './stores/authStore';

// New way
import { useAuthStore } from './auth-system/frontend/stores/authStore';
```

## API Endpoints

### Authentication
- `POST /api/auth.php` - Login, Register, Logout
- `GET /api/auth.php` - Get current user

### Request Examples

#### Login
```javascript
fetch('/api/auth.php', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'login',
    identifier: '<EMAIL>',
    password: 'password123'
  })
})
```

#### Register
```javascript
fetch('/api/auth.php', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'register',
    username: 'newuser',
    email: '<EMAIL>',
    password: 'password123',
    first_name: 'John',
    last_name: 'Doe'
  })
})
```

#### Password Reset
```javascript
fetch('/api/auth.php', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'request_password_reset',
    email: '<EMAIL>'
  })
})
```

## Security Configuration

### Rate Limiting
Default limits (configurable in `RateLimiter.php`):
- Login: 5 attempts per 15 minutes
- Registration: 3 attempts per hour
- Password reset: 3 attempts per hour
- API requests: 100 per hour

### JWT Configuration
- Default expiry: 24 hours
- Automatic refresh: 5 minutes before expiry
- Secure token storage in HTTP-only cookies (recommended)

### Password Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- No common passwords

## Customization

### Email Templates
Modify templates in `EmailService.php`:
```php
private function getEmailVerificationTemplate() {
    // Customize your email template here
    return 'Your custom HTML template';
}
```

### Security Rules
Adjust security settings in `SecurityHelper.php`:
```php
// Add custom validation rules
public static function validateCustomField($value) {
    // Your validation logic
}
```

### Rate Limits
Modify limits in `RateLimiter.php`:
```php
private $defaultLimits = [
    'login' => ['attempts' => 10, 'window' => 900], // Custom limits
    // ...
];
```

## Monitoring and Logging

### Security Logs
Logs are stored in `auth-system/backend/logs/security.log`:
```json
{
  "timestamp": "2024-01-01 12:00:00",
  "event": "SUSPICIOUS_ACTIVITY",
  "severity": "WARNING",
  "ip": "***********",
  "details": {...}
}
```

### Monitoring Endpoints
```php
// Check system health
require_once 'auth-system/backend/config/Database.php';
$db = Database::getInstance();
if ($db->getConnection()) {
    echo "Database: OK";
}
```

## Troubleshooting

### Common Issues

1. **JWT Token Issues**
   - Check JWT_SECRET in environment
   - Verify token expiry settings
   - Ensure proper token storage

2. **Email Not Sending**
   - Verify SMTP configuration
   - Check email credentials
   - Review email logs

3. **Rate Limiting Too Strict**
   - Adjust limits in RateLimiter.php
   - Check IP whitelisting
   - Review rate limit logs

4. **Database Connection Issues**
   - Verify database credentials
   - Check database server status
   - Review connection pooling settings

### Debug Mode
Enable debug mode for development:
```php
// In auth.php
define('DEBUG_MODE', true);
```

## Security Best Practices

1. **Environment Variables**
   - Never commit `.env` files
   - Use strong, unique secrets
   - Rotate keys regularly

2. **HTTPS Only**
   - Always use HTTPS in production
   - Set secure cookie flags
   - Enable HSTS headers

3. **Regular Updates**
   - Keep dependencies updated
   - Monitor security advisories
   - Review logs regularly

4. **Backup Strategy**
   - Regular database backups
   - Secure backup storage
   - Test restore procedures

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the security logs

---

**Note**: This authentication system is designed for production use but should be thoroughly tested in your specific environment before deployment.