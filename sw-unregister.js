// Service Worker Unregistration Script
// This script helps clear any cached service workers that might be causing issues

(function() {
  'use strict';

  console.log('🧹 Starting browser state cleanup...');

  // Clear service workers
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(function(registrations) {
      if (registrations.length === 0) {
        console.log('✅ No service workers to unregister');
      } else {
        for(let registration of registrations) {
          console.log('🔄 Unregistering service worker:', registration.scope);
          registration.unregister().then(function(boolean) {
            console.log('✅ Service worker unregistered:', boolean);
          }).catch(function(error) {
            console.warn('⚠️ Service worker unregistration failed:', error);
          });
        }
      }
    }).catch(function(error) {
      console.warn('⚠️ Service worker check failed:', error);
    });
  }

  // Clear all caches
  if ('caches' in window) {
    caches.keys().then(function(cacheNames) {
      if (cacheNames.length === 0) {
        console.log('✅ No caches to clear');
        return;
      }

      return Promise.all(
        cacheNames.map(function(cacheName) {
          console.log('🗑️ Deleting cache:', cacheName);
          return caches.delete(cacheName);
        })
      );
    }).then(function() {
      console.log('✅ All caches cleared');
    }).catch(function(error) {
      console.warn('⚠️ Cache clearing failed:', error);
    });
  }

  // Check for potential state conflicts
  try {
    const hasAuthData = localStorage.getItem('sb-lfnxllcoixgfkasdjtnj-auth-token') !== null;
    const hasPreferences = localStorage.getItem('user-preferences-storage') !== null;
    const hasDisclaimer = localStorage.getItem('disclaimerAccepted') !== null;

    if (hasAuthData || hasPreferences || hasDisclaimer) {
      console.log('ℹ️ Browser state detected:');
      if (hasAuthData) console.log('  - Authentication data');
      if (hasPreferences) console.log('  - User preferences');
      if (hasDisclaimer) console.log('  - Disclaimer acceptance');
      console.log('💡 If experiencing issues, consider clearing browser data');
    }
  } catch (error) {
    console.warn('⚠️ Could not check browser state:', error);
  }

  console.log('🎉 Browser state cleanup completed');
})();
