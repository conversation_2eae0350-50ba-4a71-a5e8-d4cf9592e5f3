import{K as e,i as s,n as t,V as a,X as r,s as l,S as i,G as d,o as n,O as c,Q as o,R as x,W as m,Y as u,_ as g,v as h,a as b,a0 as y,a1 as p,a2 as j,a3 as f,a4 as v,k as N,a5 as w,m as C,g as S,a6 as k,D as z,a7 as D,a8 as V,a9 as P}from"./main-Dmn9Lr65.js";const T=[{id:"1",name:"Amateur",slug:"amateur"},{id:"2",name:"Professional",slug:"professional"},{id:"3",name:"Cosplay",slug:"cosplay"},{id:"4",name:"Couples",slug:"couples"},{id:"5",name:"Solo",slug:"solo"},{id:"6",name:"Verified",slug:"verified"},{id:"7",name:"<PERSON>",slug:"hd"},{id:"8",name:"Trending",slug:"trending"}],O=({isOpen:e,onClose:s,video:i})=>{const[d,c]=a.useState(i.title),[o,x]=a.useState(i.description),[m,u]=a.useState(i.category),[g,h]=a.useState({}),[b,y]=a.useState(!1),[p,j]=a.useState(null),[f,v]=a.useState(!1),{updateVideo:N}=t();return a.useEffect((()=>{i&&(c(i.title),x(i.description),u(i.category),h({}),j(null),v(!1))}),[i]),e?r.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70",children:r.jsxs("div",{className:"bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[r.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[r.jsx("h2",{className:"text-xl font-bold text-white",children:"Edit Video"}),r.jsx("button",{className:"p-1 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors",onClick:s,children:r.jsx(n,{size:20})})]}),r.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),(()=>{const e={};return d.trim()||(e.title="Title is required"),h(e),0===Object.keys(e).length})()){y(!0),j(null),v(!1);try{await N(i.id,{title:d,description:o,category:m})?(v(!0),setTimeout((()=>{s()}),1500)):j("Failed to update video. Please try again.")}catch(t){j("An unexpected error occurred. Please try again.")}finally{y(!1)}}},className:"p-4",children:[r.jsxs("div",{className:"space-y-4",children:[r.jsx(C,{label:"Title",placeholder:"Enter video title",value:d,onChange:e=>c(e.target.value),error:g.title,fullWidth:!0}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-1",children:"Description"}),r.jsx("textarea",{className:"w-full rounded-md bg-gray-700 border border-gray-600 text-white focus:border-orange-500 focus:ring-2 focus:ring-orange-500 focus:ring-opacity-20 p-3 transition-colors placeholder:text-gray-400 min-h-[120px] "+(g.description?"border-red-500":""),placeholder:"Describe your video",value:o,onChange:e=>x(e.target.value)}),g.description&&r.jsx("p",{className:"text-sm text-red-500 mt-1",children:g.description})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-1",children:"Category"}),r.jsxs("select",{className:"w-full rounded-md bg-gray-700 border border-gray-600 text-white focus:border-orange-500 focus:ring-2 focus:ring-orange-500 focus:ring-opacity-20 p-3 transition-colors "+(g.category?"border-red-500":""),value:m,onChange:e=>u(e.target.value),children:[r.jsx("option",{value:"",children:"Select a category"}),T.map((e=>r.jsx("option",{value:e.slug,children:e.name},e.id)))]}),g.category&&r.jsx("p",{className:"text-sm text-red-500 mt-1",children:g.category})]}),r.jsx("div",{className:"border border-gray-700 rounded-lg overflow-hidden",children:r.jsx("div",{className:"aspect-video bg-gray-900 relative",children:r.jsx("img",{src:i.thumbnailUrl||"https://placehold.co/640x360/gray/white?text=No+Thumbnail",alt:i.title,className:"w-full h-full object-cover",onError:e=>{e.currentTarget.src="https://placehold.co/640x360/gray/white?text=No+Thumbnail"}})})}),p&&r.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start",children:[r.jsx(S,{size:20,className:"text-red-500 mr-2 flex-shrink-0 mt-0.5"}),r.jsx("p",{className:"text-red-200",children:p})]}),f&&r.jsxs("div",{className:"bg-green-500/20 border border-green-500 rounded-md p-3 flex items-start",children:[r.jsx(S,{size:20,className:"text-green-500 mr-2 flex-shrink-0 mt-0.5"}),r.jsx("p",{className:"text-green-200",children:"Video updated successfully!"})]})]}),r.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[r.jsx(l,{variant:"ghost",type:"button",onClick:s,disabled:b,children:"Cancel"}),r.jsx(l,{variant:"primary",type:"submit",leftIcon:r.jsx(k,{size:18}),isLoading:b,disabled:b,children:"Save Changes"})]})]})]})}):null},B=({isOpen:e,onClose:s,video:i})=>{const[d,c]=a.useState(i.status||"public"),[o,x]=a.useState(""),[m,g]=a.useState(""),[h,b]=a.useState(!1),[y,p]=a.useState(null),[v,N]=a.useState(!1),{updateVideoStatus:w}=t();return a.useEffect((()=>{if(i){if(c(i.status||"public"),i.scheduledFor){const e=new Date(i.scheduledFor);x(e.toISOString().split("T")[0]),g(e.toTimeString().slice(0,5))}else{const e=new Date;e.setDate(e.getDate()+1),e.setHours(12,0,0,0),x(e.toISOString().split("T")[0]),g("12:00")}p(null),N(!1)}}),[i]),e?r.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70",children:r.jsxs("div",{className:"bg-gray-800 rounded-lg w-full max-w-md",children:[r.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[r.jsx("h2",{className:"text-xl font-bold text-white",children:"Change Video Status"}),r.jsx("button",{className:"p-1 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors",onClick:s,disabled:h,children:r.jsx(n,{size:20})})]}),r.jsxs("form",{onSubmit:async e=>{e.preventDefault(),b(!0),p(null),N(!1);try{let e;if("scheduled"===d){if(!o)return p("Please select a publication date"),void b(!1);const s=new Date(`${o}T${m||"12:00"}`);if(s<=new Date)return p("Scheduled date must be in the future"),void b(!1);e=s.toISOString()}await w(i.id,d,e)?(N(!0),setTimeout((()=>{s()}),1500)):p("Failed to update video status. Please try again.")}catch(t){p("An unexpected error occurred. Please try again.")}finally{b(!1)}},className:"p-4",children:[r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-2",children:"Video Status"}),r.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[r.jsxs("button",{type:"button",className:`flex flex-col items-center justify-center p-3 rounded-md border ${"public"===d?"border-green-500 bg-green-500/10 text-green-400":"border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600"} transition-colors`,onClick:()=>c("public"),children:[r.jsx(u,{size:24,className:"mb-2"}),r.jsx("span",{className:"text-sm font-medium",children:"Public"})]}),r.jsxs("button",{type:"button",className:`flex flex-col items-center justify-center p-3 rounded-md border ${"private"===d?"border-gray-500 bg-gray-500/10 text-gray-300":"border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600"} transition-colors`,onClick:()=>c("private"),children:[r.jsx(j,{size:24,className:"mb-2"}),r.jsx("span",{className:"text-sm font-medium",children:"Private"})]}),r.jsxs("button",{type:"button",className:`flex flex-col items-center justify-center p-3 rounded-md border ${"scheduled"===d?"border-blue-500 bg-blue-500/10 text-blue-400":"border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600"} transition-colors`,onClick:()=>c("scheduled"),children:[r.jsx(f,{size:24,className:"mb-2"}),r.jsx("span",{className:"text-sm font-medium",children:"Scheduled"})]})]})]}),"scheduled"===d&&r.jsxs("div",{className:"bg-gray-700 p-4 rounded-md",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-2",children:"Publication Date and Time"}),r.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[r.jsx("div",{children:r.jsx("input",{type:"date",className:"w-full rounded-md bg-gray-600 border border-gray-600 text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 p-2 transition-colors",value:o,onChange:e=>x(e.target.value),min:(new Date).toISOString().split("T")[0]})}),r.jsx("div",{children:r.jsx("input",{type:"time",className:"w-full rounded-md bg-gray-600 border border-gray-600 text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 p-2 transition-colors",value:m,onChange:e=>g(e.target.value)})})]}),r.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"Your video will be automatically published at the specified date and time."})]}),"public"===d&&r.jsx("div",{className:"bg-green-500/10 border border-green-500/30 rounded-md p-3 text-sm text-green-300",children:r.jsx("p",{children:"Your video will be visible to everyone."})}),"private"===d&&r.jsx("div",{className:"bg-gray-700 border border-gray-600 rounded-md p-3 text-sm text-gray-300",children:r.jsx("p",{children:"Your video will only be visible to you."})}),y&&r.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start",children:[r.jsx(S,{size:20,className:"text-red-500 mr-2 flex-shrink-0 mt-0.5"}),r.jsx("p",{className:"text-red-200",children:y})]}),v&&r.jsxs("div",{className:"bg-green-500/20 border border-green-500 rounded-md p-3 flex items-start",children:[r.jsx(S,{size:20,className:"text-green-500 mr-2 flex-shrink-0 mt-0.5"}),r.jsx("p",{className:"text-green-200",children:"Video status updated successfully!"})]})]}),r.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[r.jsx(l,{variant:"ghost",type:"button",onClick:s,disabled:h,children:"Cancel"}),r.jsx(l,{variant:"primary",type:"submit",leftIcon:r.jsx(k,{size:18}),isLoading:h,disabled:h,children:"Save Changes"})]})]})]})}):null},A=({isOpen:e,onClose:s,operation:i,selectedCount:d})=>{const[c,o]=a.useState("public"),[m,h]=a.useState((()=>{const e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]})),[b,y]=a.useState("12:00"),[p,v]=a.useState(""),[N,w]=a.useState(!1),[C,z]=a.useState(null),[D,V]=a.useState(!1),{batchDeleteVideos:P,batchUpdateVideosStatus:O,batchUpdateVideosCategory:B}=t();return e?r.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70",children:r.jsxs("div",{className:"bg-gray-800 rounded-lg w-full max-w-md",children:[r.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[r.jsxs("h2",{className:"text-xl font-bold text-white flex items-center",children:[(()=>{switch(i){case"delete":return r.jsx(g,{size:20,className:"text-red-500 mr-2"});case"status":return r.jsx(u,{size:20,className:"text-blue-500 mr-2"});case"category":return r.jsx(x,{size:20,className:"text-purple-500 mr-2"});default:return null}})(),(()=>{switch(i){case"delete":return"Delete Selected Videos";case"status":return"Change Video Status";case"category":return"Change Video Category";default:return"Batch Operation"}})()]}),r.jsx("button",{className:"p-1 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors",onClick:s,disabled:N,children:r.jsx(n,{size:20})})]}),r.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),0!==d){w(!0),z(null),V(!1);try{let e=!1;switch(i){case"delete":e=await P();break;case"status":e=await O(c);break;case"category":if(!p)return z("Please select a category"),void w(!1);e=await B(p)}e?(V(!0),setTimeout((()=>{s()}),1500)):z("Operation failed. Please try again.")}catch(t){z("An unexpected error occurred. Please try again.")}finally{w(!1)}}else z("No videos selected")},className:"p-4",children:[r.jsxs("div",{className:"space-y-4",children:["delete"===i&&r.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-md p-4",children:r.jsxs("div",{className:"flex items-start",children:[r.jsx(S,{size:24,className:"text-red-500 mr-3 flex-shrink-0 mt-0.5"}),r.jsxs("div",{children:[r.jsx("h3",{className:"text-lg font-medium text-red-300 mb-1",children:"Confirm Deletion"}),r.jsxs("p",{className:"text-red-200",children:["Are you sure you want to delete ",d," selected video",1!==d?"s":"","? This action cannot be undone."]})]})]})}),"status"===i&&r.jsxs(r.Fragment,{children:[r.jsxs("div",{children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-200 mb-2",children:["New Status for ",d," video",1!==d?"s":""]}),r.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[r.jsxs("button",{type:"button",className:`flex flex-col items-center justify-center p-3 rounded-md border ${"public"===c?"border-green-500 bg-green-500/10 text-green-400":"border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600"} transition-colors`,onClick:()=>o("public"),children:[r.jsx(u,{size:24,className:"mb-2"}),r.jsx("span",{className:"text-sm font-medium",children:"Public"})]}),r.jsxs("button",{type:"button",className:`flex flex-col items-center justify-center p-3 rounded-md border ${"private"===c?"border-gray-500 bg-gray-500/10 text-gray-300":"border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600"} transition-colors`,onClick:()=>o("private"),children:[r.jsx(j,{size:24,className:"mb-2"}),r.jsx("span",{className:"text-sm font-medium",children:"Private"})]}),r.jsxs("button",{type:"button",className:`flex flex-col items-center justify-center p-3 rounded-md border ${"scheduled"===c?"border-blue-500 bg-blue-500/10 text-blue-400":"border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600"} transition-colors`,onClick:()=>o("scheduled"),children:[r.jsx(f,{size:24,className:"mb-2"}),r.jsx("span",{className:"text-sm font-medium",children:"Scheduled"})]})]})]}),"scheduled"===c&&r.jsxs("div",{className:"bg-gray-700 p-4 rounded-md",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-2",children:"Publication Date and Time"}),r.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[r.jsx("div",{children:r.jsx("input",{type:"date",className:"w-full rounded-md bg-gray-600 border border-gray-600 text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 p-2 transition-colors",value:m,onChange:e=>h(e.target.value),min:(new Date).toISOString().split("T")[0]})}),r.jsx("div",{children:r.jsx("input",{type:"time",className:"w-full rounded-md bg-gray-600 border border-gray-600 text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 p-2 transition-colors",value:b,onChange:e=>y(e.target.value)})})]})]})]}),"category"===i&&r.jsxs("div",{children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-200 mb-2",children:["New Category for ",d," video",1!==d?"s":""]}),r.jsxs("select",{className:"w-full rounded-md bg-gray-700 border border-gray-600 text-white focus:border-purple-500 focus:ring-2 focus:ring-purple-500 focus:ring-opacity-20 p-3 transition-colors",value:p,onChange:e=>v(e.target.value),children:[r.jsx("option",{value:"",children:"Select a category"}),T.map((e=>r.jsx("option",{value:e.slug,children:e.name},e.id)))]})]}),C&&r.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start",children:[r.jsx(S,{size:20,className:"text-red-500 mr-2 flex-shrink-0 mt-0.5"}),r.jsx("p",{className:"text-red-200",children:C})]}),D&&r.jsxs("div",{className:"bg-green-500/20 border border-green-500 rounded-md p-3 flex items-start",children:[r.jsx(S,{size:20,className:"text-green-500 mr-2 flex-shrink-0 mt-0.5"}),r.jsx("p",{className:"text-green-200",children:"Operation completed successfully!"})]})]}),r.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[r.jsx(l,{variant:"ghost",type:"button",onClick:s,disabled:N,children:"Cancel"}),r.jsx(l,{variant:"delete"===i?"danger":"primary",type:"submit",leftIcon:"delete"===i?r.jsx(g,{size:18}):r.jsx(k,{size:18}),isLoading:N,disabled:N||0===d,children:"delete"===i?"Delete":"Save Changes"})]})]})]})}):null},I=({isOpen:e,onClose:s,video:i})=>{const[d,c]=a.useState(null),[o,x]=a.useState(!1),[m,g]=a.useState(null),{fetchVideoAnalytics:h}=t();return a.useEffect((()=>{e&&i&&(async()=>{x(!0),g(null);try{const e=await h(i.id);c(e)}catch(e){g("Failed to load analytics data")}finally{x(!1)}})()}),[e,i,h]),e?r.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70",children:r.jsxs("div",{className:"bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[r.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[r.jsxs("h2",{className:"text-xl font-bold text-white flex items-center",children:[r.jsx(p,{size:20,className:"text-blue-700 mr-2"}),"Video Analytics: ",i.title]}),r.jsx("button",{className:"p-1 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors",onClick:s,children:r.jsx(n,{size:20})})]}),r.jsxs("div",{className:"p-4",children:[o?r.jsx("div",{className:"flex justify-center items-center h-64",children:r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"})}):m?r.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-4 flex items-start",children:[r.jsx(S,{size:24,className:"text-red-500 mr-3 flex-shrink-0 mt-0.5"}),r.jsxs("div",{children:[r.jsx("h3",{className:"text-lg font-medium text-red-300 mb-1",children:"Error Loading Analytics"}),r.jsx("p",{className:"text-red-200",children:m})]})]}):d?r.jsxs("div",{className:"space-y-6",children:[r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[r.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[r.jsxs("div",{className:"flex items-center mb-2",children:[r.jsx(u,{size:18,className:"text-blue-400 mr-2"}),r.jsx("h3",{className:"text-gray-300 font-medium",children:"Total Views"})]}),r.jsx("div",{className:"text-2xl font-bold text-white",children:b(i.views)}),r.jsxs("div",{className:"text-sm text-green-400 mt-1 flex items-center",children:[r.jsx(z,{size:14,className:"mr-1"}),"+",b(d.viewsLast7Days)," in last 7 days"]})]}),r.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[r.jsxs("div",{className:"flex items-center mb-2",children:[r.jsx(D,{size:18,className:"text-green-400 mr-2"}),r.jsx("h3",{className:"text-gray-300 font-medium",children:"Total Likes"})]}),r.jsx("div",{className:"text-2xl font-bold text-white",children:b(i.likes)}),r.jsxs("div",{className:"text-sm text-green-400 mt-1 flex items-center",children:[r.jsx(z,{size:14,className:"mr-1"}),"+",b(d.likesLast7Days)," in last 7 days"]})]}),r.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[r.jsxs("div",{className:"flex items-center mb-2",children:[r.jsx(V,{size:18,className:"text-purple-400 mr-2"}),r.jsx("h3",{className:"text-gray-300 font-medium",children:"Top Country"})]}),r.jsx("div",{className:"text-2xl font-bold text-white",children:d.viewsByCountry&&d.viewsByCountry.length>0?d.viewsByCountry[0].country:"N/A"}),r.jsx("div",{className:"text-sm text-gray-400 mt-1",children:d.viewsByCountry&&d.viewsByCountry.length>0?`${b(d.viewsByCountry[0].count)} views`:"No data available"})]}),r.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[r.jsxs("div",{className:"flex items-center mb-2",children:[r.jsx(P,{size:18,className:"text-orange-400 mr-2"}),r.jsx("h3",{className:"text-gray-300 font-medium",children:"Top Device"})]}),r.jsx("div",{className:"text-2xl font-bold text-white",children:d.viewsByDevice&&d.viewsByDevice.length>0?d.viewsByDevice[0].device:"N/A"}),r.jsx("div",{className:"text-sm text-gray-400 mt-1",children:d.viewsByDevice&&d.viewsByDevice.length>0?`${b(d.viewsByDevice[0].count)} views`:"No data available"})]})]}),r.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[r.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Views Over Time"}),r.jsx("div",{className:"h-64 relative",children:r.jsx("div",{className:"absolute inset-0 flex items-end",children:d.viewsByDay.map(((e,s)=>{const t=Math.max(...d.viewsByDay.map((e=>e.count))),a=t>0?e.count/t*100:0;return r.jsxs("div",{className:"flex-1 flex flex-col items-center",children:[r.jsx("div",{className:"w-full bg-orange-500 rounded-t",style:{height:`${a}%`}}),s%5==0&&r.jsx("div",{className:"text-xs text-gray-400 mt-1 rotate-45 origin-left",children:new Date(e.date).toLocaleDateString(void 0,{month:"short",day:"numeric"})})]},s)}))})})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[r.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[r.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Top Countries"}),r.jsx("div",{className:"space-y-2",children:d.viewsByCountry?.map(((e,s)=>r.jsxs("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-32 text-gray-300",children:e.country}),r.jsx("div",{className:"flex-1 h-4 bg-gray-600 rounded-full overflow-hidden",children:r.jsx("div",{className:"h-full bg-blue-500 rounded-full",style:{width:(d.viewsByCountry?e.count/d.viewsByCountry[0].count*100:0)+"%"}})}),r.jsx("div",{className:"w-16 text-right text-gray-400 text-sm",children:b(e.count)})]},s)))})]}),r.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[r.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Devices"}),r.jsx("div",{className:"space-y-2",children:d.viewsByDevice?.map(((e,s)=>r.jsxs("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-32 text-gray-300",children:e.device}),r.jsx("div",{className:"flex-1 h-4 bg-gray-600 rounded-full overflow-hidden",children:r.jsx("div",{className:"h-full bg-green-500 rounded-full",style:{width:(d.viewsByDevice?e.count/d.viewsByDevice[0].count*100:0)+"%"}})}),r.jsx("div",{className:"w-16 text-right text-gray-400 text-sm",children:b(e.count)})]},s)))})]})]})]}):r.jsx("div",{className:"text-center py-8 text-gray-400",children:"No analytics data available"}),r.jsx("div",{className:"flex justify-end mt-6",children:r.jsx(l,{variant:"primary",onClick:s,children:"Close"})})]})]})}):null},L=()=>{const C=e(),{user:S,isLoading:k}=s(),{userVideos:z,selectedVideoIds:D,sortOptions:V,filterOptions:P,pagination:L,isLoading:$,error:E,fetchUserVideos:F,deleteVideo:U,selectVideo:Y,selectAllVideos:G,batchDeleteVideos:H,batchUpdateVideosStatus:M,batchUpdateVideosCategory:R,setSortOptions:W,setFilterOptions:q,setPage:Q}=t(),[_,J]=a.useState(null),[K,X]=a.useState(!1),[Z,ee]=a.useState(!1),[se,te]=a.useState(!1),[ae,re]=a.useState(!1),[le,ie]=a.useState(!1),[de,ne]=a.useState(!1),[ce,oe]=a.useState(!1),[xe,me]=a.useState(""),[ue,ge]=a.useState(null),[he,be]=a.useState(!1),[ye,pe]=a.useState("delete");a.useEffect((()=>{k||S||C("/")}),[S,k,C]),a.useEffect((()=>{S&&F(V,P,L.currentPage)}),[S,F,V,P,L.currentPage]);const je=e=>{pe(e),ie(!0)},fe=e=>{const s=V.field===e&&"asc"===V.direction?"desc":"asc";W({field:e,direction:s}),oe(!1)},ve=e=>{q({...P,...e}),ne(!1)};if(k||$&&0===z.length)return r.jsx("div",{className:"container mx-auto px-4 py-8",children:r.jsx("div",{className:"flex justify-center items-center h-64",children:r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"})})});if(E&&!z.length)return r.jsx("div",{className:"container mx-auto px-4 py-8",children:r.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto",children:[r.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Error Loading Videos"}),r.jsx("p",{className:"text-red-200",children:E})]})});const Ne=e=>{switch(e){case"public":return r.jsx("span",{className:"px-2 py-0.5 bg-green-500/20 text-green-400 rounded text-xs",children:"Public"});case"private":return r.jsx("span",{className:"px-2 py-0.5 bg-gray-500/20 text-gray-400 rounded text-xs",children:"Private"});case"scheduled":return r.jsx("span",{className:"px-2 py-0.5 bg-blue-500/20 text-blue-400 rounded text-xs",children:"Scheduled"});default:return null}};return r.jsxs("div",{className:"container mx-auto px-4 py-8",children:[r.jsxs("div",{className:"flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4",children:[r.jsx("h1",{className:"text-2xl md:text-3xl font-bold",children:"Manage Your Videos"}),S&&r.jsx("div",{className:"flex items-center space-x-2",children:r.jsx(l,{variant:"primary",leftIcon:r.jsx(i,{size:18}),onClick:()=>C("/upload"),children:"Upload New Video"})})]}),0===z.length?r.jsxs("div",{className:"bg-gray-800 rounded-lg p-8 text-center",children:[r.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"You haven't uploaded any videos yet"}),r.jsx("p",{className:"text-gray-400 mb-6",children:"Get started by uploading your first video"}),S&&r.jsx(l,{variant:"primary",leftIcon:r.jsx(i,{size:18}),onClick:()=>C("/upload"),children:"Upload Video"})]}):r.jsxs(r.Fragment,{children:[r.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 mb-4",children:[r.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[r.jsx("div",{className:"flex-1",children:r.jsxs("form",{onSubmit:e=>{e.preventDefault(),q({...P,search:xe})},className:"relative",children:[r.jsx("input",{type:"text",placeholder:"Search videos...",className:"w-full bg-gray-700 border border-gray-600 rounded-md py-2 pl-10 pr-4 text-white focus:outline-none focus:ring-2 focus:ring-orange-500",value:xe,onChange:e=>me(e.target.value)}),r.jsx(d,{className:"absolute left-3 top-2.5 text-gray-400",size:18}),xe&&r.jsx("button",{type:"button",className:"absolute right-3 top-2.5 text-gray-400 hover:text-white",onClick:()=>{me(""),q({...P,search:void 0})},children:r.jsx(n,{size:18})})]})}),r.jsxs("div",{className:"flex gap-2",children:[r.jsxs("div",{className:"relative",children:[r.jsxs(l,{variant:"secondary",leftIcon:r.jsx(c,{size:18}),onClick:()=>oe(!ce),className:"whitespace-nowrap",children:["Sort",r.jsx(o,{size:16,className:"ml-1"})]}),ce&&r.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-md shadow-lg z-10",children:r.jsxs("div",{className:"py-1",children:[r.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+("title"===V.field?"text-orange-500":"text-white"),onClick:()=>fe("title"),children:["Title ","title"===V.field&&("asc"===V.direction?"↑":"↓")]}),r.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+("views"===V.field?"text-orange-500":"text-white"),onClick:()=>fe("views"),children:["Views ","views"===V.field&&("asc"===V.direction?"↑":"↓")]}),r.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+("createdAt"===V.field?"text-orange-500":"text-white"),onClick:()=>fe("createdAt"),children:["Date ","createdAt"===V.field&&("asc"===V.direction?"↑":"↓")]}),r.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+("status"===V.field?"text-orange-500":"text-white"),onClick:()=>fe("status"),children:["Status ","status"===V.field&&("asc"===V.direction?"↑":"↓")]})]})})]}),r.jsxs("div",{className:"relative",children:[r.jsxs(l,{variant:"secondary",leftIcon:r.jsx(x,{size:18}),onClick:()=>ne(!de),className:"whitespace-nowrap "+(Object.keys(P).length>0?"border-orange-500":""),children:["Filter",r.jsx(o,{size:16,className:"ml-1"})]}),de&&r.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-md shadow-lg z-10",children:r.jsxs("div",{className:"py-1",children:[r.jsx("div",{className:"px-4 py-2 text-sm text-gray-300 font-medium border-b border-gray-700",children:"Status"}),r.jsx("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+("public"===P.status?"text-orange-500":"text-white"),onClick:()=>ve({status:"public"}),children:"Public"}),r.jsx("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+("private"===P.status?"text-orange-500":"text-white"),onClick:()=>ve({status:"private"}),children:"Private"}),r.jsx("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+("scheduled"===P.status?"text-orange-500":"text-white"),onClick:()=>ve({status:"scheduled"}),children:"Scheduled"}),r.jsx("div",{className:"px-4 py-2 text-sm text-gray-300 font-medium border-b border-t border-gray-700",children:"Category"}),T.map((e=>r.jsx("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+(P.category===e.slug?"text-orange-500":"text-white"),onClick:()=>ve({category:e.slug}),children:e.name},e.id))),r.jsx("div",{className:"border-t border-gray-700 mt-1 pt-1",children:r.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-orange-500 hover:bg-gray-700",onClick:()=>{q({}),me("")},children:"Clear Filters"})})]})})]}),r.jsx(l,{variant:"secondary",leftIcon:r.jsx(m,{size:18}),onClick:()=>F(),isLoading:$,disabled:$,className:"whitespace-nowrap",children:"Refresh"})]})]}),D.length>0&&r.jsxs("div",{className:"mt-4 p-3 bg-gray-700 rounded-md flex flex-wrap items-center gap-3",children:[r.jsxs("div",{className:"text-white font-medium",children:[D.length," video",1!==D.length?"s":""," selected"]}),r.jsx("div",{className:"flex-1"}),r.jsx(l,{variant:"secondary",size:"sm",leftIcon:r.jsx(u,{size:16}),onClick:()=>je("status"),children:"Change Status"}),r.jsx(l,{variant:"secondary",size:"sm",leftIcon:r.jsx(x,{size:16}),onClick:()=>je("category"),children:"Change Category"}),r.jsx(l,{variant:"danger",size:"sm",leftIcon:r.jsx(g,{size:16}),onClick:()=>je("delete"),children:"Delete Selected"})]})]}),r.jsxs("div",{className:"bg-gray-800 rounded-lg overflow-hidden",children:[r.jsx("div",{className:"overflow-x-auto",children:r.jsxs("table",{className:"w-full",children:[r.jsx("thead",{className:"bg-gray-700",children:r.jsxs("tr",{children:[r.jsx("th",{className:"px-4 py-3 text-left",children:r.jsx("div",{className:"flex items-center",children:r.jsx("input",{type:"checkbox",className:"rounded bg-gray-700 border-gray-600 text-orange-500 focus:ring-orange-500",onChange:e=>{G(e.target.checked)},checked:D.length>0&&D.length===z.length})})}),r.jsx("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-200",children:"Video"}),r.jsx("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-200 hidden md:table-cell",children:"Status"}),r.jsx("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-200 hidden md:table-cell",children:"Date"}),r.jsx("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-200 hidden md:table-cell",children:"Views"}),r.jsx("th",{className:"px-4 py-3 text-right text-sm font-medium text-gray-200",children:"Actions"})]})}),r.jsx("tbody",{className:"divide-y divide-gray-700",children:z.map((e=>r.jsxs("tr",{className:"hover:bg-gray-700/50 transition-colors "+(D.includes(e.id)?"bg-gray-700/70":""),children:[r.jsx("td",{className:"px-4 py-4",children:r.jsx("input",{type:"checkbox",className:"rounded bg-gray-700 border-gray-600 text-orange-500 focus:ring-orange-500",checked:D.includes(e.id),onChange:s=>((e,s)=>{Y(e.id,s)})(e,s.target.checked)})}),r.jsx("td",{className:"px-4 py-4",children:r.jsxs("div",{className:"flex items-center",children:[r.jsxs("div",{className:"relative w-24 h-14 flex-shrink-0 mr-3",children:[r.jsx("img",{src:e.thumbnailUrl||"https://placehold.co/640x360/gray/white?text=No+Thumbnail",alt:e.title,className:"w-full h-full object-cover rounded",onError:e=>{e.currentTarget.src="https://placehold.co/640x360/gray/white?text=No+Thumbnail"}}),e.duration>0&&r.jsx("div",{className:"absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1 rounded",children:h(e.duration)})]}),r.jsxs("div",{className:"flex-1 min-w-0",children:[r.jsx("h3",{className:"text-white font-medium truncate",children:e.title}),r.jsx("p",{className:"text-gray-400 text-sm truncate",children:e.description})]})]})}),r.jsx("td",{className:"px-4 py-4 text-sm hidden md:table-cell",children:Ne(e.status)}),r.jsx("td",{className:"px-4 py-4 text-sm text-gray-400 hidden md:table-cell",children:new Date(e.createdAt).toLocaleDateString()}),r.jsx("td",{className:"px-4 py-4 text-sm text-gray-400 hidden md:table-cell",children:b(e.views||0)}),r.jsx("td",{className:"px-4 py-4 text-right",children:r.jsxs("div",{className:"flex items-center justify-end space-x-2",children:[r.jsx("button",{className:"p-1.5 rounded-full bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 transition-colors",onClick:()=>(e=>{C(`/video/${e.id}`)})(e),title:"Play",children:r.jsx(y,{size:16})}),r.jsx("button",{className:"p-1.5 rounded-full bg-green-500/20 text-green-400 hover:bg-green-500/30 transition-colors",onClick:()=>(e=>{J(e),re(!0)})(e),title:"Analytics",children:r.jsx(p,{size:16})}),r.jsx("button",{className:"p-1.5 rounded-full bg-purple-500/20 text-purple-400 hover:bg-purple-500/30 transition-colors",onClick:()=>(e=>{J(e),te(!0)})(e),title:"Change Status",children:"public"===e.status?r.jsx(u,{size:16}):"private"===e.status?r.jsx(j,{size:16}):r.jsx(f,{size:16})}),r.jsx("button",{className:"p-1.5 rounded-full bg-orange-500/20 text-orange-400 hover:bg-orange-500/30 transition-colors",onClick:()=>(e=>{J(e),X(!0)})(e),title:"Edit",children:r.jsx(v,{size:16})}),r.jsx("button",{className:"p-1.5 rounded-full bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors",onClick:()=>(e=>{J(e),ee(!0)})(e),title:"Delete",children:r.jsx(g,{size:16})})]})})]},e.id)))})]})}),!$&&z.length>0&&r.jsx("div",{className:"mt-6 flex justify-center",children:r.jsx(N,{currentPage:L.currentPage,totalPages:L.totalPages,onPageChange:e=>Q(e),className:"mb-4"})})]})]}),_&&r.jsx(O,{isOpen:K,onClose:()=>X(!1),video:_}),r.jsx(w,{isOpen:Z,onClose:()=>ee(!1),onConfirm:async()=>{if(_){be(!0),ge(null);try{await U(_.id)?(ee(!1),J(null)):ge("Failed to delete video. Please try again.")}catch(e){ge("An unexpected error occurred. Please try again.")}finally{be(!1)}}},title:"Delete Video",message:`Are you sure you want to delete "${_?.title}"? This action cannot be undone.`,isLoading:he,error:ue}),_&&r.jsx(B,{isOpen:se,onClose:()=>te(!1),video:_}),_&&r.jsx(I,{isOpen:ae,onClose:()=>re(!1),video:_}),r.jsx(A,{isOpen:le,onClose:()=>ie(!1),operation:ye,selectedCount:D.length})]})};export{L as default};
