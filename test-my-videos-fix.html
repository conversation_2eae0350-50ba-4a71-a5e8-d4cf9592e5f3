<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Videos & Upload Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #2a2a2a;
        }
        .success {
            border-color: #4ade80;
            background-color: #1a2e1a;
        }
        .error {
            border-color: #ef4444;
            background-color: #2e1a1a;
        }
        .loading {
            border-color: #fbbf24;
            background-color: #2e2a1a;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
        input {
            background-color: #374151;
            color: white;
            border: 1px solid #6b7280;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
        pre {
            background-color: #111;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
        .user-info {
            background-color: #374151;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎬 My Videos & Upload Fix Test</h1>
    <p>This page tests the authentication, "My Videos", and "Upload" functionality fixes.</p>

    <div class="test-section">
        <h3>Step 1: Login Test</h3>
        <input type="text" id="username" placeholder="Username" value="username">
        <input type="password" id="password" placeholder="Password" value="password123">
        <button onclick="testLogin()">Login</button>
        <div id="login-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 2: Check Authentication Status</h3>
        <button onclick="checkAuthStatus()">Check Auth Status</button>
        <div id="auth-status-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 3: Test User Videos API</h3>
        <button onclick="testUserVideos()">Get My Videos</button>
        <div id="user-videos-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 4: Test Upload Access</h3>
        <button onclick="testUploadAccess()">Check Upload Access</button>
        <div id="upload-access-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 5: Test Navigation Links</h3>
        <button onclick="testNavigation()">Test My Videos & Upload Links</button>
        <div id="navigation-result"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://www.bluefilmx.com/api';
        let currentUser = null;

        function setResult(elementId, message, type = 'loading') {
            const element = document.getElementById(elementId);
            element.className = `test-section ${type}`;
            element.innerHTML = message;
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            setResult('login-result', `Logging in as ${username}...`, 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth.php?action=login`, {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data.user) {
                    currentUser = data.data.user;
                    setResult('login-result', `
                        <strong>✅ Login Successful!</strong><br>
                        <div class="user-info">
                            <strong>User ID:</strong> ${currentUser.id}<br>
                            <strong>Username:</strong> ${currentUser.username}<br>
                            <strong>Approved:</strong> ${currentUser.is_approved ? 'Yes' : 'No'}
                        </div>
                        <small>You can now test the other features</small>
                    `, 'success');
                } else {
                    throw new Error('Login failed: ' + (data.error || 'Unknown error'));
                }

            } catch (error) {
                setResult('login-result', `
                    <strong>❌ Login Failed:</strong><br>
                    ${error.message}
                `, 'error');
                console.error('Login Error:', error);
            }
        }

        async function checkAuthStatus() {
            setResult('auth-status-result', 'Checking authentication status...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth.php`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.status === 401) {
                    setResult('auth-status-result', `
                        <strong>❌ Not Authenticated</strong><br>
                        Please login first
                    `, 'error');
                    return;
                }

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data.user) {
                    currentUser = data.data.user;
                    setResult('auth-status-result', `
                        <strong>✅ Authenticated!</strong><br>
                        <div class="user-info">
                            <strong>User ID:</strong> ${currentUser.id}<br>
                            <strong>Username:</strong> ${currentUser.username}<br>
                            <strong>Approved:</strong> ${currentUser.is_approved ? 'Yes' : 'No'}<br>
                            <strong>Avatar:</strong> ${currentUser.avatar_url || 'None'}<br>
                            <strong>Created:</strong> ${currentUser.created_at}
                        </div>
                    `, 'success');
                } else {
                    throw new Error('Auth check failed');
                }

            } catch (error) {
                setResult('auth-status-result', `
                    <strong>❌ Auth Check Failed:</strong><br>
                    ${error.message}
                `, 'error');
                console.error('Auth Check Error:', error);
            }
        }

        async function testUserVideos() {
            setResult('user-videos-result', 'Fetching user videos...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/videos.php?user_only=true&limit=10`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success) {
                    const videoCount = data.data.videos.length;
                    setResult('user-videos-result', `
                        <strong>✅ User Videos API Working!</strong><br>
                        <strong>Videos Found:</strong> ${videoCount}<br>
                        <strong>Total:</strong> ${data.data.pagination.total}<br>
                        <strong>Pages:</strong> ${data.data.pagination.pages}<br>
                        ${videoCount === 0 ? '<small>No videos uploaded yet (this is normal for new users)</small>' : ''}
                        <pre>${JSON.stringify(data, null, 2).substring(0, 500)}...</pre>
                    `, 'success');
                } else {
                    throw new Error('User videos API failed');
                }

            } catch (error) {
                setResult('user-videos-result', `
                    <strong>❌ User Videos API Failed:</strong><br>
                    ${error.message}
                `, 'error');
                console.error('User Videos Error:', error);
            }
        }

        async function testUploadAccess() {
            setResult('upload-access-result', 'Testing upload access...', 'loading');
            
            if (!currentUser) {
                setResult('upload-access-result', `
                    <strong>❌ No User Session</strong><br>
                    Please login first
                `, 'error');
                return;
            }

            const isAuthenticated = !!currentUser;
            const isApproved = currentUser.is_approved;
            
            setResult('upload-access-result', `
                <strong>📋 Upload Access Check:</strong><br>
                <strong>Authenticated:</strong> ${isAuthenticated ? '✅ Yes' : '❌ No'}<br>
                <strong>Approved:</strong> ${isApproved ? '✅ Yes' : '❌ No'}<br>
                <strong>Upload Access:</strong> ${isAuthenticated && isApproved ? '✅ Allowed' : '❌ Denied'}<br>
                <br>
                ${isAuthenticated && isApproved ? 
                    '<strong>🎉 You should be able to access the Upload page!</strong>' : 
                    '<strong>⚠️ Upload access requires authentication and approval</strong>'
                }
            `, isAuthenticated && isApproved ? 'success' : 'error');
        }

        async function testNavigation() {
            setResult('navigation-result', 'Testing navigation links...', 'loading');
            
            const links = [
                { name: 'My Videos', url: 'https://www.bluefilmx.com/manage' },
                { name: 'Upload', url: 'https://www.bluefilmx.com/upload' }
            ];

            let results = '<strong>🔗 Navigation Test Results:</strong><br><br>';
            
            for (const link of links) {
                try {
                    const response = await fetch(link.url, { 
                        method: 'HEAD',
                        credentials: 'include'
                    });
                    
                    results += `<strong>${link.name}:</strong> ${response.ok ? '✅ Accessible' : '❌ Error ' + response.status}<br>`;
                } catch (error) {
                    results += `<strong>${link.name}:</strong> ❌ Network Error<br>`;
                }
            }

            results += '<br><strong>💡 Note:</strong> You can manually test by visiting:<br>';
            results += '• <a href="https://www.bluefilmx.com/manage" target="_blank" style="color: #60a5fa;">My Videos Page</a><br>';
            results += '• <a href="https://www.bluefilmx.com/upload" target="_blank" style="color: #60a5fa;">Upload Page</a>';

            setResult('navigation-result', results, 'success');
        }

        // Auto-check auth status on page load
        window.addEventListener('load', () => {
            console.log('🧪 My Videos & Upload Fix Test Page Loaded');
            checkAuthStatus();
        });
    </script>
</body>
</html>
