<?php
/**
 * Admin Management API
 * Handles admin-specific operations like user approval
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/config/database.php';

class AdminAPI {
    private $db;
    
    public function __construct() {
        $database = Database::getInstance();
        $this->db = $database->getConnection();
    }
    
    public function verifyAdmin($token) {
        try {
            $stmt = $this->db->prepare("
                SELECT u.id, p.is_admin 
                FROM session s 
                JOIN user u ON s.userId = u.id 
                LEFT JOIN profiles p ON u.id = p.id
                WHERE s.token = ? AND s.expiresAt > ?
            ");
            $stmt->execute([$token, time() * 1000]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $session && $session['is_admin'];
        } catch (Exception $e) {
            return false;
        }
    }
    
    public function getPendingUsers() {
        try {
            $stmt = $this->db->prepare("
                SELECT p.id, p.username, u.email, u.name, p.created_at 
                FROM profiles p
                JOIN user u ON p.id = u.id
                WHERE p.is_approved = FALSE AND p.is_admin = FALSE
                ORDER BY p.created_at DESC
            ");
            $stmt->execute();
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return ['success' => true, 'users' => $users];
        } catch (Exception $e) {
            error_log('GetPendingUsers error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to get pending users'];
        }
    }
    
    public function approveUser($userId) {
        try {
            $stmt = $this->db->prepare("UPDATE profiles SET is_approved = TRUE WHERE id = ?");
            $stmt->execute([$userId]);
            
            if ($stmt->rowCount() > 0) {
                return ['success' => true, 'message' => 'User approved successfully'];
            } else {
                return ['success' => false, 'error' => 'User not found'];
            }
        } catch (Exception $e) {
            error_log('ApproveUser error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to approve user'];
        }
    }
    
    public function rejectUser($userId) {
        try {
            // Start transaction to delete user completely
            $this->db->beginTransaction();
            
            // Delete from profiles
            $stmt = $this->db->prepare("DELETE FROM profiles WHERE id = ?");
            $stmt->execute([$userId]);
            
            // Delete sessions
            $stmt = $this->db->prepare("DELETE FROM session WHERE userId = ?");
            $stmt->execute([$userId]);
            
            // Delete accounts
            $stmt = $this->db->prepare("DELETE FROM account WHERE userId = ?");
            $stmt->execute([$userId]);
            
            // Delete user
            $stmt = $this->db->prepare("DELETE FROM user WHERE id = ?");
            $stmt->execute([$userId]);
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'User rejected and removed'];
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('RejectUser error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to reject user'];
        }
    }
    
    public function getSystemStats() {
        try {
            // Count total users
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM profiles");
            $totalUsers = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            // Count admins
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM profiles WHERE is_admin = TRUE");
            $adminCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            // Count pending users
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM profiles WHERE is_approved = FALSE AND is_admin = FALSE");
            $pendingCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            // Count approved users
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM profiles WHERE is_approved = TRUE AND is_admin = FALSE");
            $approvedCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            // Count videos
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM videos");
            $videoCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            return [
                'success' => true,
                'stats' => [
                    'total_users' => $totalUsers,
                    'admins' => $adminCount,
                    'pending_approval' => $pendingCount,
                    'approved_users' => $approvedCount,
                    'total_videos' => $videoCount
                ]
            ];
        } catch (Exception $e) {
            error_log('GetSystemStats error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to get system stats'];
        }
    }
    
    public function getAllUsers() {
        try {
            $stmt = $this->db->prepare("
                SELECT p.id, p.username, u.email, u.name, p.is_admin, p.is_approved, p.created_at 
                FROM profiles p
                JOIN user u ON p.id = u.id
                ORDER BY p.created_at DESC
            ");
            $stmt->execute();
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return ['success' => true, 'users' => $users];
        } catch (Exception $e) {
            error_log('GetAllUsers error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to get users'];
        }
    }
}

// Handle requests
try {
    $admin = new AdminAPI();
    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true) ?? [];
    $action = $_GET['action'] ?? '';
    
    // Get token from Authorization header or input
    $token = '';
    if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
    } elseif (isset($input['token'])) {
        $token = $input['token'];
    } elseif (isset($_GET['token'])) {
        $token = $_GET['token'];
    }
    
    // Verify admin access for all operations
    if (!$admin->verifyAdmin($token)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Admin access required']);
        exit;
    }
    
    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'pending-users':
                    $result = $admin->getPendingUsers();
                    break;
                case 'stats':
                    $result = $admin->getSystemStats();
                    break;
                case 'all-users':
                    $result = $admin->getAllUsers();
                    break;
                default:
                    $result = ['success' => false, 'error' => 'Invalid action'];
            }
            break;
            
        case 'POST':
            switch ($action) {
                case 'approve-user':
                    $result = $admin->approveUser($input['user_id'] ?? '');
                    break;
                case 'reject-user':
                    $result = $admin->rejectUser($input['user_id'] ?? '');
                    break;
                default:
                    $result = ['success' => false, 'error' => 'Invalid action'];
            }
            break;
            
        default:
            $result = ['success' => false, 'error' => 'Method not allowed'];
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    error_log('Admin API error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}
?>