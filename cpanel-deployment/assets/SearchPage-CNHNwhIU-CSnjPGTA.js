import{K as e,z as s,aa as t,V as a,X as r,H as d,G as c}from"./main-Dmn9Lr65.js";import{i as l}from"./VideoGrid-Bp65TBX0-K1IpAx6B.js";const i=()=>{const i=e(),m=s(),{searchQuery:n,searchResults:x,isLoading:o,error:h,setSearchQuery:b,search:p}=t();return a.useEffect((()=>{const e=new URLSearchParams(m.search).get("q");e&&(b(e),p(e))}),[m.search,b,p]),r.jsxs("div",{className:"container mx-auto px-4 py-4 md:py-8",children:[r.jsxs("div",{className:"mb-6",children:[r.jsx("div",{className:"relative search-bar-container px-1 mb-4",children:r.jsx(d,{className:"w-full mobile-search-bar search-page-search"})}),r.jsxs("h1",{className:"text-xl md:text-3xl font-bold text-white mb-2 flex items-center",children:[r.jsx(c,{size:20,className:"mr-2 text-blue-700"}),"Search Results"]}),n&&r.jsxs("p",{className:"text-gray-400 text-sm md:text-base",children:["Showing results for ",r.jsxs("span",{className:"text-white font-medium",children:['"',n,'"']})]})]}),h&&r.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-4 mb-8",children:[r.jsx("h3",{className:"text-white font-bold mb-2",children:"Error"}),r.jsx("p",{className:"text-red-200",children:h}),h.includes("column")&&r.jsxs("div",{className:"mt-3 text-sm",children:[r.jsx("p",{className:"text-white",children:"This appears to be a database schema issue. The search is trying to access columns that don't exist."}),r.jsx("p",{className:"text-gray-300 mt-2",children:"We've updated the search to only look in title and description fields."}),r.jsx("button",{onClick:()=>p(n),className:"mt-3 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md",children:"Try Again"})]})]}),o?r.jsx("div",{className:"flex justify-center py-12",children:r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"})}):x.length>0?r.jsx(l,{videos:x,onVideoClick:e=>{return s=e.id,void i(`/video/${s}`);var s}}):n?r.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 md:p-8 text-center",children:[r.jsx(c,{size:36,className:"mx-auto text-gray-600 mb-3"}),r.jsx("h2",{className:"text-lg md:text-xl font-bold text-white mb-2",children:"No results found"}),r.jsxs("p",{className:"text-gray-400 mb-3 text-sm md:text-base",children:["We couldn't find any videos matching \"",n,'"']}),r.jsx("p",{className:"text-gray-500 text-xs md:text-sm",children:"Try different keywords or check for spelling mistakes"})]}):r.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 md:p-8 text-center",children:[r.jsx(c,{size:36,className:"mx-auto text-gray-600 mb-3"}),r.jsx("h2",{className:"text-lg md:text-xl font-bold text-white mb-2",children:"Search for videos"}),r.jsx("p",{className:"text-gray-400 text-sm md:text-base",children:"Use the search bar above to find videos"})]})]})};export{i as default};
