<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Final Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #2a2a2a;
        }
        .success {
            border-color: #4ade80;
            background-color: #1a2e1a;
        }
        .error {
            border-color: #ef4444;
            background-color: #2e1a1a;
        }
        .loading {
            border-color: #fbbf24;
            background-color: #2e2a1a;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
        button:disabled {
            background-color: #6b7280;
            cursor: not-allowed;
        }
        input[type="file"] {
            background-color: #374151;
            color: white;
            border: 1px solid #6b7280;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px;
            width: 300px;
        }
        input[type="text"] {
            background-color: #374151;
            color: white;
            border: 1px solid #6b7280;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px;
            width: 300px;
        }
        pre {
            background-color: #111;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .console-log {
            background-color: #1a1a1a;
            border: 1px solid #333;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-info { color: #60a5fa; }
        .log-success { color: #4ade80; }
        .log-error { color: #ef4444; }
        .log-warn { color: #fbbf24; }
    </style>
</head>
<body>
    <h1>📤 Upload Final Fix Test</h1>
    <p>This page tests the complete upload functionality with detailed debugging.</p>
    <p><strong>BuildId:</strong> 17497358 - Fixed parameter mismatch and added detailed logging</p>

    <div class="test-section">
        <h3>Step 1: Login</h3>
        <input type="text" id="username" placeholder="Username" value="username">
        <input type="password" id="password" placeholder="Password" value="password123">
        <button onclick="testLogin()">Login</button>
        <div id="login-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 2: Test Upload APIs</h3>
        <button onclick="testUploadAPI()">Test Upload API</button>
        <button onclick="testVideosAPI()">Test Videos API</button>
        <div id="api-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 3: Test File Upload</h3>
        <input type="file" id="testFile" accept="video/*">
        <button onclick="testFileUpload()" id="uploadBtn" disabled>Upload Test File</button>
        <div id="file-upload-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 4: Test Frontend Upload</h3>
        <button onclick="testFrontendUpload()">Open Upload Page</button>
        <div id="frontend-upload-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 5: Console Logs</h3>
        <button onclick="clearLogs()">Clear Logs</button>
        <div id="console-logs" class="console-log"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://www.bluefilmx.com/api';
        let currentUser = null;
        let logs = [];

        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog('info', args.join(' '));
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addLog('error', args.join(' '));
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addLog('warn', args.join(' '));
        };

        function addLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push({ type, message, timestamp });
            updateLogsDisplay();
        }

        function updateLogsDisplay() {
            const container = document.getElementById('console-logs');
            const recentLogs = logs.slice(-50); // Show last 50 logs
            
            container.innerHTML = recentLogs.map(log => 
                `<div class="log-entry log-${log.type}">[${log.timestamp}] ${log.message}</div>`
            ).join('');
            
            container.scrollTop = container.scrollHeight;
        }

        function clearLogs() {
            logs = [];
            updateLogsDisplay();
        }

        function setResult(elementId, message, type = 'loading') {
            const element = document.getElementById(elementId);
            element.className = `test-section ${type}`;
            element.innerHTML = message;
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            setResult('login-result', `Logging in as ${username}...`, 'loading');
            addLog('info', `Starting login for user: ${username}`);
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth.php?action=login`, {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                addLog('info', `Login response: ${JSON.stringify(data)}`);
                
                if (data.success && data.data.user) {
                    currentUser = data.data.user;
                    setResult('login-result', `
                        <strong>✅ Login Successful!</strong><br>
                        <strong>User ID:</strong> ${currentUser.id}<br>
                        <strong>Username:</strong> ${currentUser.username}<br>
                        <strong>Approved:</strong> ${currentUser.is_approved ? 'Yes' : 'No'}
                    `, 'success');
                    
                    // Enable upload button
                    document.getElementById('uploadBtn').disabled = false;
                    addLog('success', 'Login successful, upload enabled');
                } else {
                    throw new Error('Login failed: ' + (data.error || 'Unknown error'));
                }

            } catch (error) {
                addLog('error', `Login failed: ${error.message}`);
                setResult('login-result', `
                    <strong>❌ Login Failed:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        async function testUploadAPI() {
            setResult('api-result', 'Testing Upload API...', 'loading');
            addLog('info', 'Testing upload API endpoint');
            
            try {
                const response = await fetch(`${API_BASE_URL}/upload.php`, {
                    credentials: 'include',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();
                addLog('info', `Upload API response: ${JSON.stringify(data)}`);
                
                if (data.success) {
                    setResult('api-result', `
                        <strong>✅ Upload API Working!</strong><br>
                        <strong>Authenticated:</strong> ${data.authenticated ? 'Yes' : 'No'}<br>
                        <strong>User ID:</strong> ${data.user_id || 'None'}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, 'success');
                } else {
                    throw new Error(data.error || 'API failed');
                }

            } catch (error) {
                addLog('error', `Upload API test failed: ${error.message}`);
                setResult('api-result', `
                    <strong>❌ Upload API Failed:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        async function testVideosAPI() {
            addLog('info', 'Testing videos API endpoint');
            
            try {
                const response = await fetch(`${API_BASE_URL}/videos.php`, {
                    credentials: 'include',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();
                addLog('info', `Videos API response: ${JSON.stringify(data).substring(0, 200)}...`);
                
                if (data.success) {
                    addLog('success', 'Videos API working correctly');
                } else {
                    addLog('error', `Videos API failed: ${data.error}`);
                }

            } catch (error) {
                addLog('error', `Videos API test failed: ${error.message}`);
            }
        }

        async function testFileUpload() {
            const fileInput = document.getElementById('testFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a file first');
                return;
            }

            if (!currentUser) {
                alert('Please login first');
                return;
            }

            setResult('file-upload-result', 'Uploading file...', 'loading');
            addLog('info', `Starting file upload: ${file.name} (${file.size} bytes)`);
            
            try {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('type', file.type.startsWith('video/') ? 'video' : 'thumbnail');

                addLog('info', 'Sending upload request...');
                const response = await fetch(`${API_BASE_URL}/upload.php`, {
                    method: 'POST',
                    credentials: 'include',
                    body: formData
                });

                addLog('info', `Upload response status: ${response.status}`);
                const data = await response.json();
                addLog('info', `Upload response data: ${JSON.stringify(data)}`);
                
                if (data.success) {
                    setResult('file-upload-result', `
                        <strong>✅ File Upload Successful!</strong><br>
                        <strong>URL:</strong> <a href="${data.data.url}" target="_blank" style="color: #60a5fa;">${data.data.url}</a><br>
                        <strong>Filename:</strong> ${data.data.filename}<br>
                        <strong>Size:</strong> ${(data.data.size / 1024 / 1024).toFixed(2)} MB<br>
                        <strong>Type:</strong> ${data.data.type}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, 'success');
                    
                    addLog('success', `File uploaded successfully: ${data.data.url}`);
                } else {
                    throw new Error(data.error || 'Upload failed');
                }

            } catch (error) {
                addLog('error', `File upload failed: ${error.message}`);
                setResult('file-upload-result', `
                    <strong>❌ File Upload Failed:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        function testFrontendUpload() {
            setResult('frontend-upload-result', `
                <strong>🔗 Testing Frontend Upload</strong><br>
                Opening the upload page to test the complete frontend integration...<br>
                <br>
                <strong>What to test:</strong><br>
                • Page loads without errors<br>
                • File selection works<br>
                • Upload progress shows<br>
                • No "Network error during upload" messages<br>
                • Check browser console for detailed logs<br>
                <br>
                <a href="https://www.bluefilmx.com/upload" target="_blank" style="color: #60a5fa;">Open Upload Page</a>
            `, 'success');
            
            addLog('info', 'Opening frontend upload page for testing');
            
            // Auto-open upload page
            setTimeout(() => {
                window.open('https://www.bluefilmx.com/upload', '_blank');
            }, 1000);
        }

        // Auto-login on page load for quick testing
        window.addEventListener('load', () => {
            addLog('info', '🧪 Upload Final Fix Test Page Loaded - BuildId: 17497358');
            setTimeout(() => {
                if (confirm('Auto-login for testing?')) {
                    testLogin();
                }
            }, 500);
        });
    </script>
</body>
</html>
