import{K as e,i as s,V as a,X as r,ag as t,s as l,ah as d,g as i}from"./main-Dmn9Lr65.js";const c=()=>{const c=e(),{user:n,isLoading:o}=s(),[x,m]=a.useState(!1),[p,h]=a.useState(!0),[u,f]=a.useState([]),[b,j]=a.useState(!1),[y,N]=a.useState(null),[g,v]=a.useState(null);return a.useEffect((()=>{(async()=>{if(n)try{const{data:e,error:s}=await supabase.from("user_roles").select("role").eq("user_id",n.id).single();m(!s&&"admin"===e?.role)}catch(e){m(!1)}finally{h(!1)}else h(!1)})()}),[n]),a.useEffect((()=>{o||n||c("/")}),[n,o,c]),a.useEffect((()=>{x&&!p&&(async()=>{if(x){j(!0),N(null);try{const{data:e,error:s}=await supabase.from("profiles").select("*").eq("is_approved",!1).order("created_at",{ascending:!1});if(s)throw s;f(e||[])}catch(e){N("Failed to load pending users. Please try again.")}finally{j(!1)}}})()}),[x,p]),o||p?r.jsx("div",{className:"container mx-auto px-4 py-8",children:r.jsx("div",{className:"flex justify-center items-center h-64",children:r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})})}):n?x?r.jsxs("div",{className:"container mx-auto px-4 py-8",children:[r.jsx("h1",{className:"text-2xl md:text-3xl font-bold mb-6",children:"Admin Dashboard"}),r.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[r.jsxs("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[r.jsx(t,{className:"mr-2"})," Pending User Approvals"]}),y&&r.jsx("div",{className:"bg-red-900/30 border border-red-500 text-red-300 px-4 py-3 rounded mb-4",children:y}),g&&r.jsx("div",{className:"bg-green-900/30 border border-green-500 text-green-300 px-4 py-3 rounded mb-4",children:g}),b?r.jsx("div",{className:"flex justify-center py-8",children:r.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"})}):0===u.length?r.jsx("div",{className:"text-center py-8 text-gray-400",children:"No pending users to approve"}):r.jsx("div",{className:"overflow-x-auto",children:r.jsxs("table",{className:"w-full",children:[r.jsx("thead",{children:r.jsxs("tr",{className:"border-b border-gray-700",children:[r.jsx("th",{className:"px-4 py-2 text-left",children:"Username"}),r.jsx("th",{className:"px-4 py-2 text-left",children:"Email"}),r.jsx("th",{className:"px-4 py-2 text-left",children:"Registered"}),r.jsx("th",{className:"px-4 py-2 text-right",children:"Actions"})]})}),r.jsx("tbody",{children:u.map((e=>r.jsxs("tr",{className:"border-b border-gray-700",children:[r.jsx("td",{className:"px-4 py-3",children:e.username}),r.jsx("td",{className:"px-4 py-3",children:e.email||"N/A"}),r.jsx("td",{className:"px-4 py-3",children:new Date(e.created_at).toLocaleDateString()}),r.jsx("td",{className:"px-4 py-3 text-right",children:r.jsx(l,{variant:"success",size:"sm",leftIcon:r.jsx(d,{size:16}),onClick:()=>(async e=>{try{N(null);const{error:s}=await supabase.from("profiles").update({is_approved:!0}).eq("id",e);if(s)throw s;f(u.filter((s=>s.id!==e))),v("User approved successfully"),setTimeout((()=>v(null)),3e3)}catch(s){N("Failed to approve user. Please try again.")}})(e.id),children:"Approve"})})]},e.id)))})]})})]})]}):r.jsx("div",{className:"container mx-auto px-4 py-8",children:r.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:r.jsxs("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[r.jsx(i,{size:48,className:"text-red-500 mb-4"}),r.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Access Denied"}),r.jsx("p",{className:"text-gray-400 max-w-md",children:"You don't have permission to access the admin area."})]})})}):null};export{c as default};
