# cPanel Deployment Instructions

## 🎯 Quick Deployment Steps

### Step 1: Database Setup (5 minutes)
1. **Create MySQL Database in cPanel:**
   - Database name: `bluerpcm_bluefilmx` (or your preferred name)
   - Username: `bluerpcm_dbuser` (or your preferred username)
   - Password: Choose a strong password
   - Grant ALL PRIVILEGES to the user

2. **Import Database Schema:**
   - Go to phpMyAdmin in cPanel
   - Select your database
   - Import `database/create-auth-schema.sql`
   - Import `database/create-mysql-schema.sql` (if available)
   - Import `database/import-data.sql` (if available)

### Step 2: Update Database Configuration (2 minutes)
1. **Edit `api/config/database.php`:**
   ```php
   $dbname = 'your_actual_database_name';
   $username = 'your_actual_username';
   $password = 'your_actual_password';
   ```

### Step 3: Upload Files (5 minutes)
1. **Upload Frontend Files:**
   - Upload ALL files from this deployment folder (except `api/` and `database/`) to `public_html/`
   - Make sure `index.html` is in the root of `public_html/`

2. **Upload API Files:**
   - Upload the entire `api/` folder to `public_html/api/`
   - Ensure `.htaccess` file is included

### Step 4: Test Deployment (2 minutes)
1. **Test API Endpoints:**
   - Visit: `https://yourdomain.com/api/auth-mysql.php?action=session`
   - Should return JSON (not 404)

2. **Test Website:**
   - Visit: `https://yourdomain.com`
   - Should load without errors
   - Try logging in with test credentials

## 🔧 Configuration Notes

### Database Credentials
Update these in `api/config/database.php`:
- `$host = 'localhost';` (usually correct for cPanel)
- `$dbname = 'your_database_name';`
- `$username = 'your_database_user';`
- `$password = 'your_database_password';`

### CORS Configuration
The API is configured for:
- `https://www.bluefilmx.com`
- Local development URLs

Update in `api/config/database.php` if your domain is different.

## 🚨 Troubleshooting

### If API returns 404:
- Check that `.htaccess` file exists in `api/` folder
- Verify mod_rewrite is enabled on your hosting

### If database connection fails:
- Verify database credentials in `api/config/database.php`
- Check that database user has proper privileges
- Test connection in phpMyAdmin

### If authentication doesn't work:
- Check browser console for CORS errors
- Verify API endpoints return JSON
- Test with browser developer tools

## 📞 Support
If you encounter issues:
1. Check the browser console for errors
2. Test API endpoints directly
3. Verify database connection in phpMyAdmin
4. Check cPanel error logs

---
**Total Deployment Time: ~15 minutes**
