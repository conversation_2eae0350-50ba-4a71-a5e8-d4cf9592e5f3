<?php
require_once 'config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if (!isset($_SESSION)) {
    session_start();
}

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
        $offset = ($page - 1) * $limit;
        
        // Get total count
        $countStmt = $db->prepare("SELECT COUNT(*) as total FROM videos");
        $countStmt->execute();
        $total = $countStmt->fetch()['total'];
        
        // Get videos
        $stmt = $db->prepare("
            SELECT 
                v.id,
                v.title,
                v.description,
                v.thumbnail_url,
                v.video_url,
                v.duration,
                v.views,
                v.likes,
                v.is_hd,
                v.category,
                v.user_id,
                v.created_at,
                v.updated_at,
                p.username,
                p.avatar_url as user_avatar
            FROM videos v
            LEFT JOIN profiles p ON v.user_id = p.id
            ORDER BY v.created_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $stmt->execute([$limit, $offset]);
        $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Convert fields
        foreach ($videos as &$video) {
            $video['is_hd'] = (bool)$video['is_hd'];
            $video['views'] = (int)$video['views'];
            $video['likes'] = (int)$video['likes'];
            $video['duration'] = (int)$video['duration'];
            $video['description'] = $video['description'] ?? '';
            $video['thumbnail_url'] = $video['thumbnail_url'] ?? '';
            $video['username'] = $video['username'] ?? 'Unknown User';
            $video['user_avatar'] = $video['user_avatar'] ?? '';
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'videos' => $videos,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => (int)$total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    
} elseif ($method === 'POST') {
    // Simple create video
    $userId = $_SESSION['user_id'] ?? null;
    if (!$userId) {
        http_response_code(401);
        echo json_encode(['success' => false, 'error' => 'Authentication required']);
        exit();
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['title']) || !isset($input['video_url'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Title and video URL are required']);
        exit();
    }
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $videoId = sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
        
        $stmt = $db->prepare("
            INSERT INTO videos (
                id, title, description, thumbnail_url, video_url, 
                duration, category, user_id, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
        ");
        
        $stmt->execute([
            $videoId,
            $input['title'],
            $input['description'] ?? '',
            $input['thumbnail_url'] ?? '',
            $input['video_url'],
            $input['duration'] ?? 0,
            $input['category'] ?? 'uncategorized',
            $userId
        ]);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $videoId,
                'message' => 'Video created successfully'
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to create video: ' . $e->getMessage()]);
    }
    
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
}
?>
