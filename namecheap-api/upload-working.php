<?php
/**
 * Working File Upload API
 * Handles video and thumbnail uploads
 */

// Set CORS headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Start session
if (!isset($_SESSION)) {
    session_start();
}

$method = $_SERVER['REQUEST_METHOD'];

// For testing, allow GET requests
if ($method === 'GET') {
    $userId = $_SESSION['user_id'] ?? null;
    echo json_encode([
        'success' => true,
        'message' => 'Upload API is working',
        'method' => $method,
        'authenticated' => !empty($userId),
        'user_id' => $userId
    ]);
    exit();
}

if ($method !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

// Check authentication
$userId = $_SESSION['user_id'] ?? null;
if (!$userId) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Authentication required']);
    exit();
}

$uploadType = $_POST['type'] ?? 'video'; // 'video' or 'thumbnail'

// Configuration
$maxFileSize = 100 * 1024 * 1024; // 100MB
$allowedVideoTypes = ['mp4', 'mov', 'avi', 'mkv', 'webm'];
$allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp', 'gif'];

// Upload directories
$baseUploadDir = '../media/';
$videoDir = $baseUploadDir . 'videos/';
$thumbnailDir = $baseUploadDir . 'thumbnails/';

// Ensure directories exist
if (!is_dir($videoDir)) {
    mkdir($videoDir, 0755, true);
}
if (!is_dir($thumbnailDir)) {
    mkdir($thumbnailDir, 0755, true);
}

if (!isset($_FILES['file'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'No file uploaded']);
    exit();
}

$file = $_FILES['file'];

// Check for upload errors
if ($file['error'] !== UPLOAD_ERR_OK) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'File upload error: ' . $file['error']]);
    exit();
}

// Check file size
if ($file['size'] > $maxFileSize) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'File too large. Maximum size is 100MB']);
    exit();
}

// Get file extension
$pathInfo = pathinfo($file['name']);
$extension = strtolower($pathInfo['extension']);

// Validate file type
if ($uploadType === 'video') {
    if (!in_array($extension, $allowedVideoTypes)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid video format. Allowed: ' . implode(', ', $allowedVideoTypes)]);
        exit();
    }
    $uploadDir = $videoDir;
} else {
    if (!in_array($extension, $allowedImageTypes)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid image format. Allowed: ' . implode(', ', $allowedImageTypes)]);
        exit();
    }
    $uploadDir = $thumbnailDir;
}

try {
    // Generate unique filename
    $timestamp = time();
    $randomString = bin2hex(random_bytes(8));
    $safeFilename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $pathInfo['filename']);
    $newFilename = $timestamp . '_' . $randomString . '_' . $safeFilename . '.' . $extension;
    
    $targetPath = $uploadDir . $newFilename;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to save file']);
        exit();
    }
    
    // Generate URL
    $baseUrl = 'https://www.bluefilmx.com/media/';
    $fileUrl = $baseUrl . ($uploadType === 'video' ? 'videos/' : 'thumbnails/') . $newFilename;
    
    echo json_encode([
        'success' => true,
        'data' => [
            'url' => $fileUrl,
            'filename' => $newFilename,
            'size' => $file['size'],
            'type' => $uploadType
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Upload error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Upload failed: ' . $e->getMessage()]);
}
?>
