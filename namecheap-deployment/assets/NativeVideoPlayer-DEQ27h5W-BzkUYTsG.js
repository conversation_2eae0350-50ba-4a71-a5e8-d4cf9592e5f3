import{e,ad as t,V as r,ae as a,n as s,X as i,af as n}from"./main-Dmn9Lr65.js";const o=e()(t(((e,t)=>({watchHistory:{},likedVideos:[],watchLater:[],viewedCategories:{},isLoading:!1,error:null,updateWatchProgress:(t,r,a)=>{const s=Math.min(Math.round(r/a*100),100),i=s>=90;e((e=>({watchHistory:{...e.watchHistory,[t]:{currentTime:r,duration:a,percent:s,lastWatched:(new Date).toISOString(),completed:i}}}))),(async()=>{const{data:{user:e}}=await supabase.auth.getUser();e&&await supabase.from("watch_history").upsert({user_id:e.id,video_id:t,playback_position:r,duration:a,percent:s,last_watched:(new Date).toISOString(),completed:i},{onConflict:"user_id,video_id"})})()},getWatchProgress:e=>t().watchHistory[e]||null,getContinueWatchingVideos:async()=>{e({isLoading:!0,error:null});try{const{data:{user:r}}=await supabase.auth.getUser();if(!r){const r=t().watchHistory,a=new Date;a.setDate(a.getDate()-30);const s=Object.entries(r).filter((([e,t])=>{const r=new Date(t.lastWatched);return!t.completed&&r>a})).sort((([e,t],[r,a])=>new Date(a.lastWatched).getTime()-new Date(t.lastWatched).getTime())).map((([e,t])=>e));return e({isLoading:!1}),s}const{data:a,error:s}=await supabase.from("watch_history").select("video_id, last_watched, completed, percent").eq("user_id",r.id).eq("completed",!1).gt("percent",5).lt("percent",90).order("last_watched",{ascending:!1}).limit(10);if(s)throw new Error(s.message);const i={...t().watchHistory};return a.forEach((e=>{i[e.video_id]={currentTime:0,duration:0,percent:e.percent,lastWatched:e.last_watched,completed:e.completed}})),e({watchHistory:i,isLoading:!1}),a.map((e=>e.video_id))}catch(r){return e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1}),[]}},likeVideo:async t=>{e({isLoading:!0,error:null});try{const{data:{user:r}}=await supabase.auth.getUser();if(!r)return e((e=>({likedVideos:[...e.likedVideos,t],isLoading:!1}))),!0;const{error:a}=await supabase.from("video_likes").insert({user_id:r.id,video_id:t,liked_at:(new Date).toISOString()});if(a)throw new Error(a.message);return await supabase.rpc("increment_video_likes",{video_id:t}),e((e=>({likedVideos:[...e.likedVideos,t],isLoading:!1}))),!0}catch(r){return e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1}),!1}},unlikeVideo:async t=>{e({isLoading:!0,error:null});try{const{data:{user:r}}=await supabase.auth.getUser();if(!r)return e((e=>({likedVideos:e.likedVideos.filter((e=>e!==t)),isLoading:!1}))),!0;const{error:a}=await supabase.from("video_likes").delete().eq("user_id",r.id).eq("video_id",t);if(a)throw new Error(a.message);return await supabase.rpc("decrement_video_likes",{video_id:t}),e((e=>({likedVideos:e.likedVideos.filter((e=>e!==t)),isLoading:!1}))),!0}catch(r){return e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1}),!1}},isVideoLiked:e=>t().likedVideos.includes(e),addToWatchLater:t=>{e((e=>({watchLater:[...e.watchLater,t]}))),(async()=>{const{data:{user:e}}=await supabase.auth.getUser();e&&await supabase.from("watch_later").insert({user_id:e.id,video_id:t,added_at:(new Date).toISOString()})})()},removeFromWatchLater:t=>{e((e=>({watchLater:e.watchLater.filter((e=>e!==t))}))),(async()=>{const{data:{user:e}}=await supabase.auth.getUser();e&&await supabase.from("watch_later").delete().eq("user_id",e.id).eq("video_id",t)})()},isInWatchLater:e=>t().watchLater.includes(e),trackCategoryView:t=>{t&&e((e=>{const r={...e.viewedCategories};return r[t]=(r[t]||0)+1,{viewedCategories:r}}))},getMostViewedCategories:(e=5)=>{const r=t().viewedCategories;return Object.entries(r).sort((([e,t],[r,a])=>a-t)).slice(0,e).map((([e,t])=>e))}})),{name:"user-preferences-storage",partialize:e=>({watchHistory:e.watchHistory,likedVideos:e.likedVideos,watchLater:e.watchLater,viewedCategories:e.viewedCategories})})),d=r.memo((({video:e,onBack:t})=>{const[d,c]=r.useState(!1),[l,u]=r.useState(0),[m,h]=r.useState(e.duration||0),[v,w]=r.useState(a()),[g,p]=r.useState(!1),[f,b]=r.useState(!1),[x,L]=r.useState(null),[k,E]=r.useState(0);r.useState(!1),r.useEffect((()=>{}),[e]);const y=r.useRef(null),_=r.useRef(null),j=r.useRef(!1),{updateWatchProgress:V,getWatchProgress:S}=o(),{incrementVideoViews:N}=s();return r.useEffect((()=>{const e=navigator.userAgent||navigator.vendor||window.opera;b(/iPad|iPhone|iPod/.test(e)&&!window.MSStream),p(/Android/i.test(e))}),[]),r.useEffect((()=>{const e=()=>{w(a())};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[]),r.useEffect((()=>{const e=()=>{y.current&&!isInFullscreen()&&y.current.paused};return document.addEventListener("fullscreenchange",e),document.addEventListener("webkitfullscreenchange",e),document.addEventListener("mozfullscreenchange",e),document.addEventListener("MSFullscreenChange",e),()=>{document.removeEventListener("fullscreenchange",e),document.removeEventListener("webkitfullscreenchange",e),document.removeEventListener("mozfullscreenchange",e),document.removeEventListener("MSFullscreenChange",e)}}),[]),r.useEffect((()=>{if(!y.current)return;const t=()=>{if(y.current){const t=y.current.currentTime,r=y.current.duration,a=t/r*100;u(t),h(r),Math.floor(t)%5==0&&V(e.id,t,r),a>=10&&!j.current&&(N(e.id),j.current=!0)}},r=()=>c(!0),a=()=>c(!1),s=()=>{c(!1),m>0&&V(e.id,m,m)},i=()=>{if(y.current){h(y.current.duration);const t=S(e.id);t&&!t.completed&&(y.current.currentTime=t.currentTime,u(t.currentTime))}},n=e=>{const t=e.target.error;if(t){let e="Unknown error";switch(t.code){case t.MEDIA_ERR_ABORTED:e="Video loading was aborted";break;case t.MEDIA_ERR_NETWORK:e="Network error while loading video";break;case t.MEDIA_ERR_DECODE:e="Video decoding error - format may not be supported";break;case t.MEDIA_ERR_SRC_NOT_SUPPORTED:e="Video format not supported by this browser"}L(`Video failed to load: ${e}`)}},o=()=>{g&&y.current&&(isInFullscreen()?exitFullscreen():requestFullscreen(y.current))},d=y.current;return d.addEventListener("timeupdate",t),d.addEventListener("play",r),d.addEventListener("pause",a),d.addEventListener("ended",s),d.addEventListener("loadedmetadata",i),d.addEventListener("error",n),g&&d.addEventListener("dblclick",o),f&&(d.addEventListener("webkitbeginfullscreen",(()=>{})),d.addEventListener("webkitendfullscreen",(()=>{}))),()=>{d.removeEventListener("timeupdate",t),d.removeEventListener("play",r),d.removeEventListener("pause",a),d.removeEventListener("ended",s),d.removeEventListener("loadedmetadata",i),d.removeEventListener("error",n),g&&d.removeEventListener("dblclick",o),f&&(d.removeEventListener("webkitbeginfullscreen",(()=>{})),d.removeEventListener("webkitendfullscreen",(()=>{}))),m>0&&V(e.id,l,m)}}),[e.id,V,N,S,l,m,f,g]),i.jsxs("div",{className:"bg-black video-player-container relative z-10",children:[i.jsx("div",{ref:_,className:"relative aspect-video max-h-[70vh] bg-black video-container",children:x?i.jsx("div",{className:"w-full h-full flex items-center justify-center bg-gray-800",children:i.jsxs("div",{className:"text-center text-white p-4",children:[i.jsx("h3",{className:"text-lg font-bold mb-2",children:"Video Unavailable"}),i.jsx("p",{className:"text-gray-300 text-sm",children:x}),i.jsx("p",{className:"text-gray-400 text-xs mt-2",children:"This video may have been moved or is temporarily unavailable."}),i.jsxs("div",{className:"mt-4 space-x-2",children:[i.jsx("button",{onClick:()=>{L(null),E(0),y.current&&y.current.load()},className:"inline-block bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm transition-colors",children:"Retry Video"}),i.jsx("a",{href:n(e.videoUrl),target:"_blank",rel:"noopener noreferrer",className:"inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors",children:"Download Video File"})]}),i.jsxs("details",{className:"mt-4 text-left",children:[i.jsx("summary",{className:"cursor-pointer text-gray-400 text-xs",children:"Technical Details"}),i.jsxs("div",{className:"mt-2 text-xs text-gray-500 font-mono",children:[i.jsxs("p",{children:["Video URL: ",n(e.videoUrl)]}),i.jsxs("p",{children:["Original URL: ",e.videoUrl]}),i.jsxs("p",{children:["Error: ",x]}),i.jsxs("p",{children:["Browser: ",navigator.userAgent]})]})]})]})}):i.jsxs("video",{ref:y,className:"w-full h-full object-contain bg-black native-video-player",poster:n(e.thumbnailUrl),controls:!0,playsInline:!0,"webkit-playsinline":"true","x-webkit-airplay":"allow",preload:"metadata",autoPlay:!0,controlsList:"nodownload",disablePictureInPicture:v,children:[i.jsx("source",{src:n(e.videoUrl),type:"video/mp4"}),i.jsx("source",{src:n(e.videoUrl),type:"video/webm"}),i.jsx("source",{src:n(e.videoUrl),type:"video/ogg"}),i.jsxs("p",{className:"text-white p-4",children:["Your browser does not support the video tag.",i.jsx("a",{href:n(e.videoUrl),className:"text-blue-400 underline ml-1",children:"Download the video file"})]})]})}),t&&i.jsx("div",{className:"mt-4 flex justify-start",children:i.jsxs("button",{className:"bg-blue-700 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors flex items-center",onClick:t,"aria-label":"Back to previous page",children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-2",children:i.jsx("path",{d:"M19 12H5M12 19l-7-7 7-7"})}),"Back"]})})]})}));export{d};
