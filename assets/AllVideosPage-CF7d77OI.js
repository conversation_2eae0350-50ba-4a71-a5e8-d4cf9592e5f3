import{r as e,j as t}from"./react-core-B9nwsbCA.js";import{V as i,a as n,b as r,c as o,P as s}from"./ui-components-Ct00E1u3.js";import{v as l,w as a}from"./utils-D7twGpBa.js";import{u as c}from"./useVideos-63LnTHn1.js";import{u as d}from"./router-BDggJ1ol.js";import{o as u}from"./icons-BWE0bDFO.js";import"./state-4gBW_4Nx.js";function h(){return h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)({}).hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},h.apply(null,arguments)}function f(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function m(e,t){return m=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},m(e,t)}function p(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,m(e,t)}var g=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function _(e,t){if(e.length!==t.length)return!1;for(var i=0;i<e.length;i++)if(n=e[i],r=t[i],!(n===r||g(n)&&g(r)))return!1;var n,r;return!0}function v(e,t){var i;void 0===t&&(t=_);var n,r=[],o=!1;return function(){for(var s=[],l=0;l<arguments.length;l++)s[l]=arguments[l];return o&&i===this&&t(s,r)||(n=e.apply(this,s),o=!0,i=this,r=s),n}}var S="object"==typeof performance&&"function"==typeof performance.now?function(){return performance.now()}:function(){return Date.now()};function w(e){cancelAnimationFrame(e.id)}function x(e,t){var i=S();var n={id:requestAnimationFrame((function r(){S()-i>=t?e.call(null):n.id=requestAnimationFrame(r)}))};return n}var y=-1;function I(e){if(void 0===e&&(e=!1),-1===y||e){var t=document.createElement("div"),i=t.style;i.width="50px",i.height="50px",i.overflow="scroll",document.body.appendChild(t),y=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return y}var b=null;function C(e){if(void 0===e&&(e=!1),null===b||e){var t=document.createElement("div"),i=t.style;i.width="50px",i.height="50px",i.overflow="scroll",i.direction="rtl";var n=document.createElement("div"),r=n.style;return r.width="100px",r.height="100px",t.appendChild(n),document.body.appendChild(t),t.scrollLeft>0?b="positive-descending":(t.scrollLeft=1,b=0===t.scrollLeft?"negative":"positive-ascending"),document.body.removeChild(t),b}return b}var R=function(e){var t=e.columnIndex;return e.data,e.rowIndex+":"+t};function z(t){var i,n=t.getColumnOffset,r=t.getColumnStartIndexForOffset,o=t.getColumnStopIndexForStartIndex,s=t.getColumnWidth,l=t.getEstimatedTotalHeight,a=t.getEstimatedTotalWidth,c=t.getOffsetForColumnAndAlignment,d=t.getOffsetForRowAndAlignment,u=t.getRowHeight,m=t.getRowOffset,g=t.getRowStartIndexForOffset,_=t.getRowStopIndexForStartIndex,S=t.initInstanceProps,y=t.shouldResetStyleCacheOnItemSizeChange,b=t.validateProps;return(i=function(t){function i(e){var i;return(i=t.call(this,e)||this)._instanceProps=S(i.props,f(i)),i._resetIsScrollingTimeoutId=null,i._outerRef=void 0,i.state={instance:f(i),isScrolling:!1,horizontalScrollDirection:"forward",scrollLeft:"number"==typeof i.props.initialScrollLeft?i.props.initialScrollLeft:0,scrollTop:"number"==typeof i.props.initialScrollTop?i.props.initialScrollTop:0,scrollUpdateWasRequested:!1,verticalScrollDirection:"forward"},i._callOnItemsRendered=void 0,i._callOnItemsRendered=v((function(e,t,n,r,o,s,l,a){return i.props.onItemsRendered({overscanColumnStartIndex:e,overscanColumnStopIndex:t,overscanRowStartIndex:n,overscanRowStopIndex:r,visibleColumnStartIndex:o,visibleColumnStopIndex:s,visibleRowStartIndex:l,visibleRowStopIndex:a})})),i._callOnScroll=void 0,i._callOnScroll=v((function(e,t,n,r,o){return i.props.onScroll({horizontalScrollDirection:n,scrollLeft:e,scrollTop:t,verticalScrollDirection:r,scrollUpdateWasRequested:o})})),i._getItemStyle=void 0,i._getItemStyle=function(e,t){var r,o=i.props,l=o.columnWidth,a=o.direction,c=o.rowHeight,d=i._getItemStyleCache(y&&l,y&&a,y&&c),h=e+":"+t;if(d.hasOwnProperty(h))r=d[h];else{var f=n(i.props,t,i._instanceProps),p="rtl"===a;d[h]=r={position:"absolute",left:p?void 0:f,right:p?f:void 0,top:m(i.props,e,i._instanceProps),height:u(i.props,e,i._instanceProps),width:s(i.props,t,i._instanceProps)}}return r},i._getItemStyleCache=void 0,i._getItemStyleCache=v((function(e,t,i){return{}})),i._onScroll=function(e){var t=e.currentTarget,n=t.clientHeight,r=t.clientWidth,o=t.scrollLeft,s=t.scrollTop,l=t.scrollHeight,a=t.scrollWidth;i.setState((function(e){if(e.scrollLeft===o&&e.scrollTop===s)return null;var t=i.props.direction,c=o;if("rtl"===t)switch(C()){case"negative":c=-o;break;case"positive-descending":c=a-r-o}c=Math.max(0,Math.min(c,a-r));var d=Math.max(0,Math.min(s,l-n));return{isScrolling:!0,horizontalScrollDirection:e.scrollLeft<o?"forward":"backward",scrollLeft:c,scrollTop:d,verticalScrollDirection:e.scrollTop<s?"forward":"backward",scrollUpdateWasRequested:!1}}),i._resetIsScrollingDebounced)},i._outerRefSetter=function(e){var t=i.props.outerRef;i._outerRef=e,"function"==typeof t?t(e):null!=t&&"object"==typeof t&&t.hasOwnProperty("current")&&(t.current=e)},i._resetIsScrollingDebounced=function(){null!==i._resetIsScrollingTimeoutId&&w(i._resetIsScrollingTimeoutId),i._resetIsScrollingTimeoutId=x(i._resetIsScrolling,150)},i._resetIsScrolling=function(){i._resetIsScrollingTimeoutId=null,i.setState({isScrolling:!1},(function(){i._getItemStyleCache(-1)}))},i}p(i,t),i.getDerivedStateFromProps=function(e,t){return T(e,t),b(e),null};var z=i.prototype;return z.scrollTo=function(e){var t=e.scrollLeft,i=e.scrollTop;void 0!==t&&(t=Math.max(0,t)),void 0!==i&&(i=Math.max(0,i)),this.setState((function(e){return void 0===t&&(t=e.scrollLeft),void 0===i&&(i=e.scrollTop),e.scrollLeft===t&&e.scrollTop===i?null:{horizontalScrollDirection:e.scrollLeft<t?"forward":"backward",scrollLeft:t,scrollTop:i,scrollUpdateWasRequested:!0,verticalScrollDirection:e.scrollTop<i?"forward":"backward"}}),this._resetIsScrollingDebounced)},z.scrollToItem=function(e){var t=e.align,i=void 0===t?"auto":t,n=e.columnIndex,r=e.rowIndex,o=this.props,s=o.columnCount,u=o.height,h=o.rowCount,f=o.width,m=this.state,p=m.scrollLeft,g=m.scrollTop,_=I();void 0!==n&&(n=Math.max(0,Math.min(n,s-1))),void 0!==r&&(r=Math.max(0,Math.min(r,h-1)));var v=l(this.props,this._instanceProps),S=a(this.props,this._instanceProps)>f?_:0,w=v>u?_:0;this.scrollTo({scrollLeft:void 0!==n?c(this.props,n,i,p,this._instanceProps,w):p,scrollTop:void 0!==r?d(this.props,r,i,g,this._instanceProps,S):g})},z.componentDidMount=function(){var e=this.props,t=e.initialScrollLeft,i=e.initialScrollTop;if(null!=this._outerRef){var n=this._outerRef;"number"==typeof t&&(n.scrollLeft=t),"number"==typeof i&&(n.scrollTop=i)}this._callPropsCallbacks()},z.componentDidUpdate=function(){var e=this.props.direction,t=this.state,i=t.scrollLeft,n=t.scrollTop;if(t.scrollUpdateWasRequested&&null!=this._outerRef){var r=this._outerRef;if("rtl"===e)switch(C()){case"negative":r.scrollLeft=-i;break;case"positive-ascending":r.scrollLeft=i;break;default:var o=r.clientWidth,s=r.scrollWidth;r.scrollLeft=s-o-i}else r.scrollLeft=Math.max(0,i);r.scrollTop=Math.max(0,n)}this._callPropsCallbacks()},z.componentWillUnmount=function(){null!==this._resetIsScrollingTimeoutId&&w(this._resetIsScrollingTimeoutId)},z.render=function(){var t=this.props,i=t.children,n=t.className,r=t.columnCount,o=t.direction,s=t.height,c=t.innerRef,d=t.innerElementType,u=t.innerTagName,f=t.itemData,m=t.itemKey,p=void 0===m?R:m,g=t.outerElementType,_=t.outerTagName,v=t.rowCount,S=t.style,w=t.useIsScrolling,x=t.width,y=this.state.isScrolling,I=this._getHorizontalRangeToRender(),b=I[0],C=I[1],z=this._getVerticalRangeToRender(),T=z[0],O=z[1],M=[];if(r>0&&v)for(var L=T;L<=O;L++)for(var W=b;W<=C;W++)M.push(e.createElement(i,{columnIndex:W,data:f,isScrolling:w?y:void 0,key:p({columnIndex:W,data:f,rowIndex:L}),rowIndex:L,style:this._getItemStyle(L,W)}));var N=l(this.props,this._instanceProps),k=a(this.props,this._instanceProps);return e.createElement(g||_||"div",{className:n,onScroll:this._onScroll,ref:this._outerRefSetter,style:h({position:"relative",height:s,width:x,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:o},S)},e.createElement(d||u||"div",{children:M,ref:c,style:{height:N,pointerEvents:y?"none":void 0,width:k}}))},z._callPropsCallbacks=function(){var e=this.props,t=e.columnCount,i=e.onItemsRendered,n=e.onScroll,r=e.rowCount;if("function"==typeof i&&t>0&&r>0){var o=this._getHorizontalRangeToRender(),s=o[0],l=o[1],a=o[2],c=o[3],d=this._getVerticalRangeToRender(),u=d[0],h=d[1],f=d[2],m=d[3];this._callOnItemsRendered(s,l,u,h,a,c,f,m)}if("function"==typeof n){var p=this.state,g=p.horizontalScrollDirection,_=p.scrollLeft,v=p.scrollTop,S=p.scrollUpdateWasRequested,w=p.verticalScrollDirection;this._callOnScroll(_,v,g,w,S)}},z._getHorizontalRangeToRender=function(){var e=this.props,t=e.columnCount,i=e.overscanColumnCount,n=e.overscanColumnsCount,s=e.overscanCount,l=e.rowCount,a=this.state,c=a.horizontalScrollDirection,d=a.isScrolling,u=a.scrollLeft,h=i||n||s||1;if(0===t||0===l)return[0,0,0,0];var f=r(this.props,u,this._instanceProps),m=o(this.props,f,u,this._instanceProps),p=d&&"backward"!==c?1:Math.max(1,h),g=d&&"forward"!==c?1:Math.max(1,h);return[Math.max(0,f-p),Math.max(0,Math.min(t-1,m+g)),f,m]},z._getVerticalRangeToRender=function(){var e=this.props,t=e.columnCount,i=e.overscanCount,n=e.overscanRowCount,r=e.overscanRowsCount,o=e.rowCount,s=this.state,l=s.isScrolling,a=s.verticalScrollDirection,c=s.scrollTop,d=n||r||i||1;if(0===t||0===o)return[0,0,0,0];var u=g(this.props,c,this._instanceProps),h=_(this.props,u,c,this._instanceProps),f=l&&"backward"!==a?1:Math.max(1,d),m=l&&"forward"!==a?1:Math.max(1,d);return[Math.max(0,u-f),Math.max(0,Math.min(o-1,h+m)),u,h]},i}(e.PureComponent)).defaultProps={direction:"ltr",itemData:void 0,useIsScrolling:!1},i}var T=function(e,t){e.children,e.direction,e.height,e.innerTagName,e.outerTagName,e.overscanColumnsCount,e.overscanCount,e.overscanRowsCount,e.width,t.instance},O=function(e,t){return e};function M(t){var i,n=t.getItemOffset,r=t.getEstimatedTotalSize,o=t.getItemSize,s=t.getOffsetForIndexAndAlignment,l=t.getStartIndexForOffset,a=t.getStopIndexForStartIndex,c=t.initInstanceProps,d=t.shouldResetStyleCacheOnItemSizeChange,u=t.validateProps;return(i=function(t){function i(e){var i;return(i=t.call(this,e)||this)._instanceProps=c(i.props,f(i)),i._outerRef=void 0,i._resetIsScrollingTimeoutId=null,i.state={instance:f(i),isScrolling:!1,scrollDirection:"forward",scrollOffset:"number"==typeof i.props.initialScrollOffset?i.props.initialScrollOffset:0,scrollUpdateWasRequested:!1},i._callOnItemsRendered=void 0,i._callOnItemsRendered=v((function(e,t,n,r){return i.props.onItemsRendered({overscanStartIndex:e,overscanStopIndex:t,visibleStartIndex:n,visibleStopIndex:r})})),i._callOnScroll=void 0,i._callOnScroll=v((function(e,t,n){return i.props.onScroll({scrollDirection:e,scrollOffset:t,scrollUpdateWasRequested:n})})),i._getItemStyle=void 0,i._getItemStyle=function(e){var t,r=i.props,s=r.direction,l=r.itemSize,a=r.layout,c=i._getItemStyleCache(d&&l,d&&a,d&&s);if(c.hasOwnProperty(e))t=c[e];else{var u=n(i.props,e,i._instanceProps),h=o(i.props,e,i._instanceProps),f="horizontal"===s||"horizontal"===a,m="rtl"===s,p=f?u:0;c[e]=t={position:"absolute",left:m?void 0:p,right:m?p:void 0,top:f?0:u,height:f?"100%":h,width:f?h:"100%"}}return t},i._getItemStyleCache=void 0,i._getItemStyleCache=v((function(e,t,i){return{}})),i._onScrollHorizontal=function(e){var t=e.currentTarget,n=t.clientWidth,r=t.scrollLeft,o=t.scrollWidth;i.setState((function(e){if(e.scrollOffset===r)return null;var t=i.props.direction,s=r;if("rtl"===t)switch(C()){case"negative":s=-r;break;case"positive-descending":s=o-n-r}return s=Math.max(0,Math.min(s,o-n)),{isScrolling:!0,scrollDirection:e.scrollOffset<s?"forward":"backward",scrollOffset:s,scrollUpdateWasRequested:!1}}),i._resetIsScrollingDebounced)},i._onScrollVertical=function(e){var t=e.currentTarget,n=t.clientHeight,r=t.scrollHeight,o=t.scrollTop;i.setState((function(e){if(e.scrollOffset===o)return null;var t=Math.max(0,Math.min(o,r-n));return{isScrolling:!0,scrollDirection:e.scrollOffset<t?"forward":"backward",scrollOffset:t,scrollUpdateWasRequested:!1}}),i._resetIsScrollingDebounced)},i._outerRefSetter=function(e){var t=i.props.outerRef;i._outerRef=e,"function"==typeof t?t(e):null!=t&&"object"==typeof t&&t.hasOwnProperty("current")&&(t.current=e)},i._resetIsScrollingDebounced=function(){null!==i._resetIsScrollingTimeoutId&&w(i._resetIsScrollingTimeoutId),i._resetIsScrollingTimeoutId=x(i._resetIsScrolling,150)},i._resetIsScrolling=function(){i._resetIsScrollingTimeoutId=null,i.setState({isScrolling:!1},(function(){i._getItemStyleCache(-1,null)}))},i}p(i,t),i.getDerivedStateFromProps=function(e,t){return L(e,t),u(e),null};var m=i.prototype;return m.scrollTo=function(e){e=Math.max(0,e),this.setState((function(t){return t.scrollOffset===e?null:{scrollDirection:t.scrollOffset<e?"forward":"backward",scrollOffset:e,scrollUpdateWasRequested:!0}}),this._resetIsScrollingDebounced)},m.scrollToItem=function(e,t){void 0===t&&(t="auto");var i=this.props,n=i.itemCount,r=i.layout,o=this.state.scrollOffset;e=Math.max(0,Math.min(e,n-1));var l=0;if(this._outerRef){var a=this._outerRef;l="vertical"===r?a.scrollWidth>a.clientWidth?I():0:a.scrollHeight>a.clientHeight?I():0}this.scrollTo(s(this.props,e,t,o,this._instanceProps,l))},m.componentDidMount=function(){var e=this.props,t=e.direction,i=e.initialScrollOffset,n=e.layout;if("number"==typeof i&&null!=this._outerRef){var r=this._outerRef;"horizontal"===t||"horizontal"===n?r.scrollLeft=i:r.scrollTop=i}this._callPropsCallbacks()},m.componentDidUpdate=function(){var e=this.props,t=e.direction,i=e.layout,n=this.state,r=n.scrollOffset;if(n.scrollUpdateWasRequested&&null!=this._outerRef){var o=this._outerRef;if("horizontal"===t||"horizontal"===i)if("rtl"===t)switch(C()){case"negative":o.scrollLeft=-r;break;case"positive-ascending":o.scrollLeft=r;break;default:var s=o.clientWidth,l=o.scrollWidth;o.scrollLeft=l-s-r}else o.scrollLeft=r;else o.scrollTop=r}this._callPropsCallbacks()},m.componentWillUnmount=function(){null!==this._resetIsScrollingTimeoutId&&w(this._resetIsScrollingTimeoutId)},m.render=function(){var t=this.props,i=t.children,n=t.className,o=t.direction,s=t.height,l=t.innerRef,a=t.innerElementType,c=t.innerTagName,d=t.itemCount,u=t.itemData,f=t.itemKey,m=void 0===f?O:f,p=t.layout,g=t.outerElementType,_=t.outerTagName,v=t.style,S=t.useIsScrolling,w=t.width,x=this.state.isScrolling,y="horizontal"===o||"horizontal"===p,I=y?this._onScrollHorizontal:this._onScrollVertical,b=this._getRangeToRender(),C=b[0],R=b[1],z=[];if(d>0)for(var T=C;T<=R;T++)z.push(e.createElement(i,{data:u,key:m(T,u),index:T,isScrolling:S?x:void 0,style:this._getItemStyle(T)}));var M=r(this.props,this._instanceProps);return e.createElement(g||_||"div",{className:n,onScroll:I,ref:this._outerRefSetter,style:h({position:"relative",height:s,width:w,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:o},v)},e.createElement(a||c||"div",{children:z,ref:l,style:{height:y?"100%":M,pointerEvents:x?"none":void 0,width:y?M:"100%"}}))},m._callPropsCallbacks=function(){if("function"==typeof this.props.onItemsRendered&&this.props.itemCount>0){var e=this._getRangeToRender(),t=e[0],i=e[1],n=e[2],r=e[3];this._callOnItemsRendered(t,i,n,r)}if("function"==typeof this.props.onScroll){var o=this.state,s=o.scrollDirection,l=o.scrollOffset,a=o.scrollUpdateWasRequested;this._callOnScroll(s,l,a)}},m._getRangeToRender=function(){var e=this.props,t=e.itemCount,i=e.overscanCount,n=this.state,r=n.isScrolling,o=n.scrollDirection,s=n.scrollOffset;if(0===t)return[0,0,0,0];var c=l(this.props,s,this._instanceProps),d=a(this.props,c,s,this._instanceProps),u=r&&"backward"!==o?1:Math.max(1,i),h=r&&"forward"!==o?1:Math.max(1,i);return[Math.max(0,c-u),Math.max(0,Math.min(t-1,d+h)),c,d]},i}(e.PureComponent)).defaultProps={direction:"ltr",itemData:void 0,layout:"vertical",overscanCount:2,useIsScrolling:!1},i}var L=function(e,t){e.children,e.direction,e.height,e.layout,e.innerTagName,e.outerTagName,e.width,t.instance},W=z({getColumnOffset:function(e,t){return t*e.columnWidth},getColumnWidth:function(e,t){return e.columnWidth},getRowOffset:function(e,t){return t*e.rowHeight},getRowHeight:function(e,t){return e.rowHeight},getEstimatedTotalHeight:function(e){var t=e.rowCount;return e.rowHeight*t},getEstimatedTotalWidth:function(e){var t=e.columnCount;return e.columnWidth*t},getOffsetForColumnAndAlignment:function(e,t,i,n,r,o){var s=e.columnCount,l=e.columnWidth,a=e.width,c=Math.max(0,s*l-a),d=Math.min(c,t*l),u=Math.max(0,t*l-a+o+l);switch("smart"===i&&(i=n>=u-a&&n<=d+a?"auto":"center"),i){case"start":return d;case"end":return u;case"center":var h=Math.round(u+(d-u)/2);return h<Math.ceil(a/2)?0:h>c+Math.floor(a/2)?c:h;default:return n>=u&&n<=d?n:u>d||n<u?u:d}},getOffsetForRowAndAlignment:function(e,t,i,n,r,o){var s=e.rowHeight,l=e.height,a=e.rowCount,c=Math.max(0,a*s-l),d=Math.min(c,t*s),u=Math.max(0,t*s-l+o+s);switch("smart"===i&&(i=n>=u-l&&n<=d+l?"auto":"center"),i){case"start":return d;case"end":return u;case"center":var h=Math.round(u+(d-u)/2);return h<Math.ceil(l/2)?0:h>c+Math.floor(l/2)?c:h;default:return n>=u&&n<=d?n:u>d||n<u?u:d}},getColumnStartIndexForOffset:function(e,t){var i=e.columnWidth,n=e.columnCount;return Math.max(0,Math.min(n-1,Math.floor(t/i)))},getColumnStopIndexForStartIndex:function(e,t,i){var n=e.columnWidth,r=e.columnCount,o=e.width,s=t*n,l=Math.ceil((o+i-s)/n);return Math.max(0,Math.min(r-1,t+l-1))},getRowStartIndexForOffset:function(e,t){var i=e.rowHeight,n=e.rowCount;return Math.max(0,Math.min(n-1,Math.floor(t/i)))},getRowStopIndexForStartIndex:function(e,t,i){var n=e.rowHeight,r=e.rowCount,o=e.height,s=t*n,l=Math.ceil((o+i-s)/n);return Math.max(0,Math.min(r-1,t+l-1))},initInstanceProps:function(e){},shouldResetStyleCacheOnItemSizeChange:!0,validateProps:function(e){e.columnWidth,e.rowHeight}}),N=M({getItemOffset:function(e,t){return t*e.itemSize},getItemSize:function(e,t){return e.itemSize},getEstimatedTotalSize:function(e){var t=e.itemCount;return e.itemSize*t},getOffsetForIndexAndAlignment:function(e,t,i,n,r,o){var s=e.direction,l=e.height,a=e.itemCount,c=e.itemSize,d=e.layout,u=e.width,h="horizontal"===s||"horizontal"===d?u:l,f=Math.max(0,a*c-h),m=Math.min(f,t*c),p=Math.max(0,t*c-h+c+o);switch("smart"===i&&(i=n>=p-h&&n<=m+h?"auto":"center"),i){case"start":return m;case"end":return p;case"center":var g=Math.round(p+(m-p)/2);return g<Math.ceil(h/2)?0:g>f+Math.floor(h/2)?f:g;default:return n>=p&&n<=m?n:n<p?p:m}},getStartIndexForOffset:function(e,t){var i=e.itemCount,n=e.itemSize;return Math.max(0,Math.min(i-1,Math.floor(t/n)))},getStopIndexForStartIndex:function(e,t,i){var n=e.direction,r=e.height,o=e.itemCount,s=e.itemSize,l=e.layout,a=e.width,c=t*s,d="horizontal"===n||"horizontal"===l?a:r,u=Math.ceil((d+i-c)/s);return Math.max(0,Math.min(o-1,t+u-1))},initInstanceProps:function(e){},shouldResetStyleCacheOnItemSizeChange:!0,validateProps:function(e){e.itemSize}});let k;k="undefined"!=typeof window?window:"undefined"!=typeof self?self:global;let P=null,j=null;const E=k.clearTimeout,D=k.setTimeout,H=k.cancelAnimationFrame||k.mozCancelAnimationFrame||k.webkitCancelAnimationFrame,A=k.requestAnimationFrame||k.mozRequestAnimationFrame||k.webkitRequestAnimationFrame;function F(e){let t,i,n,r,o,s,l;const a="undefined"!=typeof document&&document.attachEvent;if(!a){s=function(e){const t=e.__resizeTriggers__,i=t.firstElementChild,n=t.lastElementChild,r=i.firstElementChild;n.scrollLeft=n.scrollWidth,n.scrollTop=n.scrollHeight,r.style.width=i.offsetWidth+1+"px",r.style.height=i.offsetHeight+1+"px",i.scrollLeft=i.scrollWidth,i.scrollTop=i.scrollHeight},o=function(e){return e.offsetWidth!==e.__resizeLast__.width||e.offsetHeight!==e.__resizeLast__.height},l=function(e){if(e.target.className&&"function"==typeof e.target.className.indexOf&&e.target.className.indexOf("contract-trigger")<0&&e.target.className.indexOf("expand-trigger")<0)return;const t=this;s(this),this.__resizeRAF__&&P(this.__resizeRAF__),this.__resizeRAF__=j((function(){o(t)&&(t.__resizeLast__.width=t.offsetWidth,t.__resizeLast__.height=t.offsetHeight,t.__resizeListeners__.forEach((function(i){i.call(t,e)})))}))};let e=!1,a="";n="animationstart";const c="Webkit Moz O ms".split(" ");let d="webkitAnimationStart animationstart oAnimationStart MSAnimationStart".split(" "),u="";{const t=document.createElement("fakeelement");if(void 0!==t.style.animationName&&(e=!0),!1===e)for(let i=0;i<c.length;i++)if(void 0!==t.style[c[i]+"AnimationName"]){u=c[i],a="-"+u.toLowerCase()+"-",n=d[i],e=!0;break}}i="resizeanim",t="@"+a+"keyframes "+i+" { from { opacity: 0; } to { opacity: 0; } } ",r=a+"animation: 1ms "+i+"; "}return{addResizeListener:function(o,c){if(a)o.attachEvent("onresize",c);else{if(!o.__resizeTriggers__){const a=o.ownerDocument,c=k.getComputedStyle(o);c&&"static"===c.position&&(o.style.position="relative"),function(i){if(!i.getElementById("detectElementResize")){const n=(t||"")+".resize-triggers { "+(r||"")+'visibility: hidden; opacity: 0; } .resize-triggers, .resize-triggers > div, .contract-trigger:before { content: " "; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; z-index: -1; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',o=i.head||i.getElementsByTagName("head")[0],s=i.createElement("style");s.id="detectElementResize",s.type="text/css",null!=e&&s.setAttribute("nonce",e),s.styleSheet?s.styleSheet.cssText=n:s.appendChild(i.createTextNode(n)),o.appendChild(s)}}(a),o.__resizeLast__={},o.__resizeListeners__=[],(o.__resizeTriggers__=a.createElement("div")).className="resize-triggers";const d=a.createElement("div");d.className="expand-trigger",d.appendChild(a.createElement("div"));const u=a.createElement("div");u.className="contract-trigger",o.__resizeTriggers__.appendChild(d),o.__resizeTriggers__.appendChild(u),o.appendChild(o.__resizeTriggers__),s(o),o.addEventListener("scroll",l,!0),n&&(o.__resizeTriggers__.__animationListener__=function(e){e.animationName===i&&s(o)},o.__resizeTriggers__.addEventListener(n,o.__resizeTriggers__.__animationListener__))}o.__resizeListeners__.push(c)}},removeResizeListener:function(e,t){if(a)e.detachEvent("onresize",t);else if(e.__resizeListeners__.splice(e.__resizeListeners__.indexOf(t),1),!e.__resizeListeners__.length){e.removeEventListener("scroll",l,!0),e.__resizeTriggers__.__animationListener__&&(e.__resizeTriggers__.removeEventListener(n,e.__resizeTriggers__.__animationListener__),e.__resizeTriggers__.__animationListener__=null);try{e.__resizeTriggers__=!e.removeChild(e.__resizeTriggers__)}catch(i){}}}}}null==H||null==A?(P=E,j=function(e){return D(e,20)}):(P=function([e,t]){H(e),E(t)},j=function(e){const t=A((function(){E(i),e()})),i=D((function(){H(t),e()}),20);return[t,i]});class V extends e.Component{constructor(...e){super(...e),this.state={height:this.props.defaultHeight||0,width:this.props.defaultWidth||0},this._autoSizer=null,this._detectElementResize=null,this._didLogDeprecationWarning=!1,this._parentNode=null,this._resizeObserver=null,this._timeoutId=null,this._onResize=()=>{this._timeoutId=null;const{disableHeight:e,disableWidth:t,onResize:i}=this.props;if(this._parentNode){const n=window.getComputedStyle(this._parentNode)||{},r=parseFloat(n.paddingLeft||"0"),o=parseFloat(n.paddingRight||"0"),s=parseFloat(n.paddingTop||"0"),l=parseFloat(n.paddingBottom||"0"),a=this._parentNode.getBoundingClientRect(),c=a.height-s-l,d=a.width-r-o;if(!e&&this.state.height!==c||!t&&this.state.width!==d){this.setState({height:c,width:d});const e=()=>{this._didLogDeprecationWarning||(this._didLogDeprecationWarning=!0)};"function"==typeof i&&i({height:c,width:d,get scaledHeight(){return e(),c},get scaledWidth(){return e(),d}})}}},this._setRef=e=>{this._autoSizer=e}}componentDidMount(){const{nonce:e}=this.props,t=this._autoSizer?this._autoSizer.parentNode:null;if(null!=t&&t.ownerDocument&&t.ownerDocument.defaultView&&t instanceof t.ownerDocument.defaultView.HTMLElement){this._parentNode=t;const i=t.ownerDocument.defaultView.ResizeObserver;null!=i?(this._resizeObserver=new i((()=>{this._timeoutId=setTimeout(this._onResize,0)})),this._resizeObserver.observe(t)):(this._detectElementResize=F(e),this._detectElementResize.addResizeListener(t,this._onResize)),this._onResize()}}componentWillUnmount(){this._parentNode&&(this._detectElementResize&&this._detectElementResize.removeResizeListener(this._parentNode,this._onResize),null!==this._timeoutId&&clearTimeout(this._timeoutId),this._resizeObserver&&this._resizeObserver.disconnect())}render(){const{children:t,defaultHeight:i,defaultWidth:n,disableHeight:r=!1,disableWidth:o=!1,doNotBailOutOnEmptyChildren:s=!1,nonce:l,onResize:a,style:c={},tagName:d="div",...u}=this.props,{height:h,width:f}=this.state,m={overflow:"visible"},p={};let g=!1;return r||(0===h&&(g=!0),m.height=0,p.height=h,p.scaledHeight=h),o||(0===f&&(g=!0),m.width=0,p.width=f,p.scaledWidth=f),s&&(g=!1),e.createElement(d,{ref:this._setRef,style:{...m,...c},...u},!g&&t(p))}}var U=new Map,q=new WeakMap,B=0;function $(e){return Object.keys(e).sort().filter((t=>void 0!==e[t])).map((t=>{return`${t}_${"root"===t?(i=e.root,i?(q.has(i)||(B+=1,q.set(i,B.toString())),q.get(i)):"0"):e[t]}`;var i})).toString()}function K(e,t,i={},n=undefined){if(void 0===window.IntersectionObserver&&void 0!==n){const r=e.getBoundingClientRect();return t(n,{isIntersecting:n,target:e,intersectionRatio:"number"==typeof i.threshold?i.threshold:0,time:0,boundingClientRect:r,intersectionRect:r,rootBounds:r}),()=>{}}const{id:r,observer:o,elements:s}=function(e){const t=$(e);let i=U.get(t);if(!i){const n=new Map;let r;const o=new IntersectionObserver((t=>{t.forEach((t=>{var i;const o=t.isIntersecting&&r.some((e=>t.intersectionRatio>=e));e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=o),null==(i=n.get(t.target))||i.forEach((e=>{e(o,t)}))}))}),e);r=o.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),i={id:t,observer:o,elements:n},U.set(t,i)}return i}(i),l=s.get(e)||[];return s.has(e)||s.set(e,l),l.push(t),o.observe(e),function(){l.splice(l.indexOf(t),1),0===l.length&&(s.delete(e),o.unobserve(e)),0===s.size&&(o.disconnect(),U.delete(r))}}const G=({title:s,videos:a,onVideoClick:c,isLoading:d=!1,viewAll:u,className:h="",showProgressBar:f=!1,itemHeight:m=280,overscanCount:p=5,horizontal:g=!1,gridColumns:_=4})=>{const{ref:v,inView:S}=function({threshold:t,delay:i,trackVisibility:n,rootMargin:r,root:o,triggerOnce:s,skip:l,initialInView:a,fallbackInView:c,onChange:d}={}){var u;const[h,f]=e.useState(null),m=e.useRef(d),[p,g]=e.useState({inView:!!a,entry:void 0});m.current=d,e.useEffect((()=>{if(l||!h)return;let e;return e=K(h,((t,i)=>{g({inView:t,entry:i}),m.current&&m.current(t,i),i.isIntersecting&&s&&e&&(e(),e=void 0)}),{root:o,rootMargin:r,threshold:t,trackVisibility:n,delay:i},c),()=>{e&&e()}}),[Array.isArray(t)?t.toString():t,h,o,r,s,l,n,c,i]);const _=null==(u=p.entry)?void 0:u.target,v=e.useRef(void 0);h||!_||s||l||v.current===_||(v.current=_,g({inView:!!a,entry:void 0}));const S=[f,p.inView,p.entry];return S.ref=S[0],S.inView=S[1],S.entry=S[2],S}({threshold:.1,triggerOnce:!1}),w=Array(8).fill(0).map(((e,n)=>t.jsx(i,{},`skeleton-${n}`))),x=e.useMemo((()=>d||0===a.length?[]:l(a)),[a,d]),y=e=>{if(!c)return;const t=a.find((t=>t.id===e));t&&c(t)},I=({columnIndex:e,rowIndex:i,style:s,data:l})=>{const{items:a,columnCount:c,onItemClick:d}=l,u=i*c+e;if(u>=a.length)return null;const h=a[u],m={...s,padding:window.innerWidth<640?"4px 8px":"8px"};if("baseTitle"in h&&"videos"in h){const e=h;return t.jsx("div",{style:m,children:t.jsx(n,{series:e,onClick:y})})}{const e=h;return t.jsx("div",{style:m,children:e.progress&&f?t.jsx(r,{video:e,onClick:d}):t.jsx(o,{video:e,onClick:d})})}},b=({index:e,style:i,data:s})=>{const{items:l,onItemClick:a}=s;if(e>=l.length)return null;const c=l[e],d={...i,padding:window.innerWidth<640?"0 4px":"0 8px"};if("baseTitle"in c&&"videos"in c){const e=c;return t.jsx("div",{style:d,children:t.jsx(n,{series:e,onClick:y,compact:!0})})}{const e=c;return t.jsx("div",{style:d,children:e.progress&&f?t.jsx(r,{video:e,onClick:a}):t.jsx(o,{video:e,onClick:a})})}};return t.jsxs("section",{className:`py-3 md:py-5 ${h}`,ref:v,children:[s&&t.jsxs("div",{className:"flex justify-between items-center mb-2 md:mb-4",children:[t.jsx("h2",{className:"text-lg md:text-2xl font-bold text-white",children:s}),t.jsxs("div",{className:"flex items-center",children:[d&&a.length>0&&t.jsx("div",{className:"w-4 h-4 md:w-5 md:h-5 border-t-2 border-b-2 border-blue-700 rounded-full animate-spin mr-2 md:mr-3"}),u&&t.jsx("button",{onClick:u,className:"text-gray-400 hover:text-blue-700 text-xs md:text-sm font-medium transition-colors",children:"View All"})]})]}),d&&0===a.length?t.jsx("div",{className:"grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4",children:w}):d||0!==a.length?t.jsx("div",{className:"w-full",style:{height:g?m+20:window.innerWidth<640?"calc(100vh - 200px)":"calc(100vh - 300px)",minHeight:window.innerWidth<640?"300px":"400px",maxHeight:"1200px"},children:t.jsx(V,{children:({height:e,width:i})=>{if(g)return t.jsx(N,{height:e,width:i,itemCount:x.length,itemSize:280,layout:"horizontal",overscanCount:p,itemData:{items:x,onItemClick:c},children:b});{const n=(e=>e<640||e<768?1:e<1024?2:_)(i),r=Math.ceil(x.length/n);return t.jsx(W,{columnCount:n,columnWidth:i/n,height:e,rowCount:r,rowHeight:m,width:i,overscanRowCount:p,itemData:{items:x,columnCount:n,onItemClick:c},children:I})}}})}):t.jsx("div",{className:"py-8 text-center text-gray-400 w-full",children:t.jsx("p",{children:"No videos found"})})]})},J=()=>{const i=d(),[n,r]=e.useState(1),[o,l]=e.useState(null),[h,f]=e.useState([]),[m,p]=e.useState(!1),[g,_]=e.useState([]),[v,S]=e.useState(!1);e.useState(!1);const{videos:w,pagination:x,isLoading:y,error:I}=c("",n,12),b=y&&0===w.length&&!I&&!m&&!v,C=v?g:m?h:w,R=!v&&!m&&y,z=v||m?null:I,T=m?{currentPage:n,totalPages:1,totalCount:h.length,pageSize:12}:x,O=()=>{i("/")};return e.useEffect((()=>{if(C.length>0&&!R){const e=C.map((e=>({thumbnailUrl:e.thumbnailUrl,videoUrl:e.videoUrl})));a(e,{priority:"high",maxPreload:6,includeNextPage:!1}).catch((e=>{}))}}),[C,R]),t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsxs("div",{className:"flex items-center mb-6",children:[t.jsx("button",{onClick:O,className:"mr-4 p-2 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors","aria-label":"Go back",children:t.jsx(u,{size:20,className:"text-white"})}),t.jsx("h1",{className:"text-2xl font-bold text-white",children:"All Videos"})]}),z&&t.jsxs("div",{className:"bg-red-500/20 border border-red-500 text-white p-4 rounded-lg mb-6",children:[t.jsx("h3",{className:"font-bold mb-2",children:"Error Loading Videos"}),t.jsx("p",{children:z})]}),!1,!1,!1,t.jsx(G,{title:"All Videos",videos:C,onVideoClick:e=>{l(e.id),i(`/video/${e.id}`)},isLoading:R,gridColumns:4,itemHeight:320,overscanCount:3}),!R&&C.length>0&&t.jsx("div",{className:"mt-8",children:t.jsx(s,{currentPage:T.currentPage,totalPages:T.totalPages,onPageChange:e=>r(e),className:"mb-8"})}),!R&&0===C.length&&!b&&t.jsxs("div",{className:"text-center py-12",children:[t.jsx("h2",{className:"text-xl text-gray-400 mb-4",children:"No videos found"}),t.jsx("button",{onClick:O,className:"px-6 py-2 bg-blue-700 text-white rounded-md hover:bg-blue-600 transition-colors",children:"Back to Home"})]})]})};export{J as default};
