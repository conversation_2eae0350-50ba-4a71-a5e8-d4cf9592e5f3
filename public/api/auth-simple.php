<?php
header('Content-Type: application/json');

// CORS headers
$allowed_origins = [
    'https://bluefilmx.com',
    'https://www.bluefilmx.com',
    'http://localhost:3000',
    'http://localhost:5173'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowed_origins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
}
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Simple auth response - no database required
// For now, return that user is not authenticated
// This prevents the 500 error and allows the app to work

$response = [
    'success' => true,
    'data' => [
        'user' => null,
        'isAuthenticated' => false
    ]
];

echo json_encode($response);
?>
