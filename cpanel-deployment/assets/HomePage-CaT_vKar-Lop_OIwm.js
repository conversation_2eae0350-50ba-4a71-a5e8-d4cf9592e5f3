import{K as e,z as a,w as s,V as t,b as r,X as o,H as i,k as n}from"./main-Dmn9Lr65.js";import{i as d}from"./VideoGrid-Bp65TBX0-K1IpAx6B.js";import{e as l}from"./useVideos-iT4pmMdb-UKXDSXuC.js";const c=({onCategoryChange:e,selectedCategory:a})=>o.jsx("div",{className:"mb-6",children:o.jsxs("div",{className:"flex space-x-3 sm:space-x-6 md:space-x-8 py-2 overflow-x-auto scrollbar-hide",children:[o.jsx("button",{className:"px-3 py-2 sm:px-4 rounded-full whitespace-nowrap transition-colors text-sm sm:text-base "+(""===a?"bg-blue-700 text-white":"bg-gray-800 text-gray-300 hover:bg-gray-700"),onClick:()=>e(""),children:"All Videos"}),[{id:"trending",name:"Trending",slug:"trending"},{id:"recommended",name:"Recommended",slug:"recommended"},{id:"hot",name:"Hot",slug:"hot"},{id:"new",name:"New",slug:"new"}].map((s=>o.jsx("button",{className:"px-3 py-2 sm:px-4 rounded-full whitespace-nowrap transition-colors text-sm sm:text-base "+(a===s.slug?"bg-blue-700 text-white":"bg-gray-800 text-gray-300 hover:bg-gray-700"),onClick:()=>e(s.slug),children:s.name},s.id)))]})}),m=()=>{const m=e(),g=a(),[x,h]=s(),p=parseInt(x.get("page")||"1"),u=x.get("category")||"",[b,v]=t.useState(p),[j,y]=t.useState(u);t.useEffect((()=>{if("/"===g.pathname&&!x.has("page")){v(1);const e={page:"1"};j&&(e.category=j),h(e)}}),[g.pathname,x,h,j]),t.useEffect((()=>{const e=parseInt(x.get("page")||"1"),a=x.get("category")||"";e!==b&&v(e),a!==j&&y(a)}),[x,b,j]);const{videos:N,pagination:w,isLoading:f,error:C}=l(j,b,24),k=N.map((e=>({id:e.id,title:e.title,description:e.description||"",thumbnail:e.thumbnail_url||r(),thumbnailUrl:e.thumbnail_url||r(),videoUrl:e.video_url,duration:e.duration||0,views:e.views||0,likes:e.likes||0,isHD:e.is_hd||!1,category:e.category||"uncategorized",uploadDate:e.created_at,creator:{id:e.user_id,name:e.username||"Unknown User",avatar:e.user_avatar||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0}})));return C&&!k.length?o.jsx("div",{className:"container mx-auto px-4 py-16",children:o.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto",children:[o.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Error Loading Videos"}),o.jsx("p",{className:"text-red-200",children:C})]})}):o.jsx("div",{className:"pb-10",children:o.jsxs("div",{className:"container mx-auto px-4",children:[o.jsx("div",{className:"md:hidden my-4",children:o.jsx("div",{className:"relative search-bar-container px-1",children:o.jsx(i,{className:"w-full mobile-search-bar homepage-search"})})}),o.jsx("div",{className:"mb-6",children:o.jsx(c,{selectedCategory:j,onCategoryChange:e=>{y(e),v(1);const a={page:"1"};e&&(a.category=e),h(a),window.scrollTo({top:0,behavior:"smooth"})}})}),C&&o.jsxs("div",{className:"bg-red-500/20 border border-red-500 text-white p-4 rounded-lg mb-6",children:[o.jsx("h3",{className:"font-bold mb-2",children:"Error Loading Videos"}),o.jsx("p",{children:C})]}),o.jsx("div",{className:"mt-6",children:o.jsx(d,{videos:k,onVideoClick:e=>{m(`/video/${e.id}`)},isLoading:f,className:"mb-8"})}),!f&&k.length>0&&w.totalPages>1&&o.jsx("div",{className:"mt-12 mb-8",children:o.jsx(n,{currentPage:w.currentPage,totalPages:w.totalPages,onPageChange:e=>{v(e);const a={page:e.toString()};j&&(a.category=j),h(a),window.scrollTo({top:0,behavior:"smooth"})},className:"justify-center"})}),!f&&0===k.length&&!C&&o.jsxs("div",{className:"text-center py-16",children:[o.jsx("div",{className:"text-gray-400 text-lg mb-4",children:"No videos found"}),o.jsx("p",{className:"text-gray-500 text-sm",children:"Check back later for new content!"})]})]})})};export{m as default};
