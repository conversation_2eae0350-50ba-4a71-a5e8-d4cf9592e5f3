# Authentication Setup for Namecheap Hosting

This guide will help you set up the PHP-based authentication system on your Namecheap server.

## Prerequisites

1. **MySQL Database**: You need a MySQL database on your Namecheap hosting account
2. **PHP Support**: Your hosting should support PHP (most Namecheap plans do)

## Setup Steps

### 1. Create MySQL Database

1. Log into your Namecheap cPanel
2. Go to "MySQL Databases"
3. Create a new database (e.g., `your_username_bluefilmx`)
4. Create a database user with a strong password
5. Add the user to the database with "All Privileges"
6. Note down:
   - Database name
   - Database username  
   - Database password
   - Database host (usually `localhost`)

### 2. Configure Database Connection

1. Open `public/api/config.php`
2. Update the database configuration:

```php
$DB_CONFIG = [
    'host' => 'localhost',  // Usually 'localhost'
    'dbname' => 'your_actual_database_name',  // From step 1
    'username' => 'your_actual_db_username',  // From step 1
    'password' => 'your_actual_db_password'   // From step 1
];
```

### 3. Set Up Database Tables

After uploading the files to your server, run the database setup:

1. Visit: `https://www.bluefilmx.com/api/setup-db.php`
2. This will create the necessary tables:
   - `users` - stores user accounts
   - `user_sessions` - stores login sessions

### 4. Test the Authentication

1. Try signing up for a new account on your website
2. Try signing in with the account you created
3. Check that sessions work properly

## API Endpoints

The authentication system provides these endpoints:

- `POST /api/auth.php?action=signin` - Sign in
- `POST /api/auth.php?action=signup` - Sign up  
- `POST /api/auth.php?action=signout` - Sign out
- `GET /api/auth.php?action=session&token=TOKEN` - Validate session

## Security Notes

1. **Change Default Passwords**: Make sure to use strong, unique database passwords
2. **HTTPS**: Ensure your site uses HTTPS for secure authentication
3. **Regular Updates**: Keep your hosting environment updated

## Troubleshooting

### Common Issues:

1. **500 Error**: Check database connection settings in `config.php`
2. **Database Connection Failed**: Verify database credentials
3. **CORS Issues**: The API includes CORS headers for cross-origin requests

### Debug Steps:

1. Check PHP error logs in cPanel
2. Verify database tables were created correctly
3. Test database connection separately

## File Structure

```
public/
├── api/
│   ├── auth.php          # Main authentication API
│   ├── config.php        # Database configuration
│   └── setup-db.php      # Database setup script
└── [your other files]
```

## Next Steps

After setting up authentication:

1. Test login/signup functionality
2. Verify user sessions work correctly
3. Consider adding password reset functionality
4. Add user profile management features

## Support

If you encounter issues:

1. Check the browser console for JavaScript errors
2. Check PHP error logs in cPanel
3. Verify all database credentials are correct
4. Ensure the database tables were created successfully
