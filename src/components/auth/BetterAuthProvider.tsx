import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useBetterAuthStore } from '../../stores/betterAuthStore';
import type { User, Session } from '../../lib/auth-client';

interface BetterAuthContextType {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string, name?: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
}

const BetterAuthContext = createContext<BetterAuthContextType | undefined>(undefined);

export const useBetterAuth = () => {
  const context = useContext(BetterAuthContext);
  if (context === undefined) {
    throw new Error('useBetterAuth must be used within a BetterAuthProvider');
  }
  return context;
};

interface BetterAuthProviderProps {
  children: ReactNode;
}

export const BetterAuthProvider: React.FC<BetterAuthProviderProps> = ({ children }) => {
  const {
    user,
    session,
    isLoading,
    signIn,
    signUp,
    signOut,
    initialize
  } = useBetterAuthStore();

  const isAuthenticated = !!user && !!session;

  useEffect(() => {
    initialize();
  }, [initialize]);

  const value: BetterAuthContextType = {
    user,
    session,
    isLoading,
    isAuthenticated,
    signIn,
    signUp,
    signOut,
  };

  return (
    <BetterAuthContext.Provider value={value}>
      {children}
    </BetterAuthContext.Provider>
  );
};