<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Homepage Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #2a2a2a;
        }
        .success {
            border-color: #4ade80;
            background-color: #1a2e1a;
        }
        .error {
            border-color: #ef4444;
            background-color: #2e1a1a;
        }
        .loading {
            border-color: #fbbf24;
            background-color: #2e2a1a;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
        pre {
            background-color: #111;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .video-card {
            background-color: #374151;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        .video-title {
            font-weight: bold;
            color: #60a5fa;
        }
        .video-meta {
            font-size: 12px;
            color: #9ca3af;
        }
    </style>
</head>
<body>
    <h1>🏠 Homepage Fix Test</h1>
    <p>This page tests that the homepage "Error Loading Videos" issue is resolved.</p>

    <div class="test-section">
        <h3>Step 1: Test Videos API</h3>
        <button onclick="testVideosAPI()">Test Videos API</button>
        <div id="api-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 2: Test Data Structure</h3>
        <button onclick="testDataStructure()">Test Expected Data Structure</button>
        <div id="structure-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 3: Test Homepage</h3>
        <button onclick="testHomepage()">Open Homepage</button>
        <div id="homepage-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 4: Sample Videos</h3>
        <div id="sample-videos"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://www.bluefilmx.com/api';

        function setResult(elementId, message, type = 'loading') {
            const element = document.getElementById(elementId);
            element.className = `test-section ${type}`;
            element.innerHTML = message;
        }

        async function testVideosAPI() {
            setResult('api-result', 'Testing Videos API...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/videos.php?limit=5`, {
                    headers: { 'Content-Type': 'application/json' }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data && data.data.videos) {
                    setResult('api-result', `
                        <strong>✅ Videos API Working!</strong><br>
                        <strong>Videos Found:</strong> ${data.data.videos.length}<br>
                        <strong>Total Videos:</strong> ${data.data.pagination.total}<br>
                        <strong>Total Pages:</strong> ${data.data.pagination.pages}<br>
                        <strong>Current Page:</strong> ${data.data.pagination.page}<br>
                        <pre>${JSON.stringify(data, null, 2).substring(0, 1000)}...</pre>
                    `, 'success');
                    
                    // Show sample videos
                    showSampleVideos(data.data.videos);
                } else {
                    throw new Error('Invalid API response structure');
                }

            } catch (error) {
                setResult('api-result', `
                    <strong>❌ Videos API Failed:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        async function testDataStructure() {
            setResult('structure-result', 'Testing data structure compatibility...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/videos.php?limit=1`);
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error('API returned error: ' + data.error);
                }

                // Check required structure
                const checks = [
                    { name: 'data object', check: !!data.data },
                    { name: 'videos array', check: Array.isArray(data.data.videos) },
                    { name: 'pagination object', check: !!data.data.pagination },
                    { name: 'pagination.page', check: typeof data.data.pagination.page === 'number' },
                    { name: 'pagination.total', check: typeof data.data.pagination.total === 'number' },
                    { name: 'pagination.pages', check: typeof data.data.pagination.pages === 'number' },
                ];

                if (data.data.videos.length > 0) {
                    const video = data.data.videos[0];
                    checks.push(
                        { name: 'video.id', check: !!video.id },
                        { name: 'video.title', check: !!video.title },
                        { name: 'video.video_url', check: !!video.video_url },
                        { name: 'video.thumbnail_url', check: video.thumbnail_url !== undefined },
                        { name: 'video.views (number)', check: typeof video.views === 'number' },
                        { name: 'video.likes (number)', check: typeof video.likes === 'number' },
                        { name: 'video.is_hd (boolean)', check: typeof video.is_hd === 'boolean' }
                    );
                }

                const passed = checks.filter(c => c.check).length;
                const total = checks.length;

                let resultHtml = `<strong>📋 Data Structure Check: ${passed}/${total} passed</strong><br><br>`;
                
                checks.forEach(check => {
                    resultHtml += `${check.check ? '✅' : '❌'} ${check.name}<br>`;
                });

                if (passed === total) {
                    resultHtml += '<br><strong>🎉 All checks passed! Homepage should work correctly.</strong>';
                    setResult('structure-result', resultHtml, 'success');
                } else {
                    resultHtml += '<br><strong>⚠️ Some checks failed. Homepage may have issues.</strong>';
                    setResult('structure-result', resultHtml, 'error');
                }

            } catch (error) {
                setResult('structure-result', `
                    <strong>❌ Data Structure Test Failed:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        function testHomepage() {
            setResult('homepage-result', `
                <strong>🔗 Testing Homepage</strong><br>
                Opening the homepage to verify the "Error Loading Videos" issue is fixed...<br>
                <br>
                <strong>What to check:</strong><br>
                • No "Error Loading Videos" message<br>
                • No "Cannot read properties of undefined (reading 'videos')" error<br>
                • Videos load and display properly<br>
                • Pagination works if there are multiple pages<br>
                <br>
                <a href="https://www.bluefilmx.com/" target="_blank" style="color: #60a5fa;">Open Homepage</a>
            `, 'success');
            
            // Auto-open homepage
            setTimeout(() => {
                window.open('https://www.bluefilmx.com/', '_blank');
            }, 1000);
        }

        function showSampleVideos(videos) {
            const container = document.getElementById('sample-videos');
            
            if (videos.length === 0) {
                container.innerHTML = '<p>No videos found</p>';
                return;
            }

            let html = '<strong>📹 Sample Videos from API:</strong><br><br>';
            
            videos.slice(0, 3).forEach(video => {
                html += `
                    <div class="video-card">
                        <div class="video-title">${video.title}</div>
                        <div class="video-meta">
                            👁️ ${video.views} views • 
                            👍 ${video.likes} likes • 
                            📁 ${video.category} • 
                            👤 ${video.username || 'Unknown User'}
                        </div>
                        <div class="video-meta">
                            🆔 ${video.id}<br>
                            🎬 ${video.video_url ? 'Video URL available' : 'No video URL'}<br>
                            🖼️ ${video.thumbnail_url ? 'Thumbnail available' : 'No thumbnail'}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            console.log('🧪 Homepage Fix Test Page Loaded');
            setTimeout(() => {
                testVideosAPI();
            }, 500);
        });
    </script>
</body>
</html>
