import{e,K as r,i as t,V as a,X as s,ab as o,s as i,h as d}from"./main-Dmn9Lr65.js";import{i as n}from"./VideoGrid-Bp65TBX0-K1IpAx6B.js";const c=e=>({id:e.id,title:e.title,description:e.description||"",thumbnailUrl:e.thumbnail_url||"https://placehold.co/640x360/gray/white?text=No+Thumbnail",videoUrl:e.video_url,duration:e.duration||0,views:e.views||0,likes:e.likes||0,createdAt:e.created_at,updatedAt:e.updated_at||e.created_at,publishedAt:e.created_at,scheduledFor:void 0,status:"public",isHD:e.is_hd||!1,isPremium:!1,tags:e.tags?e.tags.split(",").map((e=>e.trim())):[],category:e.category||"uncategorized",creator:{id:e.user_id,email:"",avatar:e.user_avatar||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0}}),l=e(((e,r)=>({favorites:[],isLoading:!1,error:null,fetchFavorites:async()=>{const{user:r}=t.getState();if(r){e({isLoading:!0,error:null});try{const r=await d.getFavorites();if(!r.success||!r.data.videos)throw new Error("Failed to fetch favorites");{const t=r.data.videos.map(c);e({favorites:t,isLoading:!1})}}catch(a){e({error:a instanceof Error?a.message:"An unknown error occurred",isLoading:!1})}}else e({favorites:[],error:null})},addToFavorites:async a=>{const{user:s}=t.getState();if(!s)return e({error:"You must be logged in to add favorites"}),!1;try{if((await d.addToFavorites(a)).success)return await r().fetchFavorites(),!0;throw new Error("Failed to add to favorites")}catch(n){return e({error:n instanceof Error?n.message:"An unknown error occurred"}),!1}},removeFromFavorites:async r=>{const{user:a}=t.getState();if(!a)return e({error:"You must be logged in to remove favorites"}),!1;try{if((await d.removeFromFavorites(r)).success)return e((e=>({favorites:e.favorites.filter((e=>e.id!==r))}))),!0;throw new Error("Failed to remove from favorites")}catch(s){return e({error:s instanceof Error?s.message:"An unknown error occurred"}),!1}},isFavorite:e=>r().favorites.some((r=>r.id===e))}))),m=()=>{const e=r(),{user:d}=t(),{favorites:c,isLoading:m,error:u,fetchFavorites:x}=l();return a.useEffect((()=>{x()}),[x]),d?u?s.jsx("div",{className:"container mx-auto px-4 py-16",children:s.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Error Loading Favorites"}),s.jsx("p",{className:"text-red-200",children:u})]})}):s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsxs("div",{className:"flex items-center mb-8",children:[s.jsx(o,{size:24,className:"text-orange-500 mr-3"}),s.jsx("h1",{className:"text-2xl md:text-3xl font-bold text-white",children:"Your Favorites"})]}),m?s.jsx("div",{className:"flex justify-center py-12",children:s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"})}):0===c.length?s.jsxs("div",{className:"bg-gray-800 rounded-lg p-8 text-center",children:[s.jsx(o,{size:48,className:"mx-auto text-gray-600 mb-4"}),s.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"No Favorites Yet"}),s.jsx("p",{className:"text-gray-400 mb-6",children:"You haven't added any videos to your favorites yet."}),s.jsx(i,{variant:"primary",onClick:()=>e("/"),children:"Explore Videos"})]}):s.jsx(n,{videos:c,onVideoClick:r=>{return t=r.id,void e(`/video/${t}`);var t},isLoading:m})]}):s.jsx("div",{className:"container mx-auto px-4 py-16",children:s.jsxs("div",{className:"bg-gray-800 rounded-lg p-8 max-w-2xl mx-auto text-center",children:[s.jsx(o,{size:48,className:"mx-auto text-gray-600 mb-4"}),s.jsx("h1",{className:"text-2xl font-bold text-white mb-4",children:"Favorites"}),s.jsx("p",{className:"text-gray-400 mb-6",children:"You need to be logged in to view and manage your favorite videos."}),s.jsx(i,{variant:"primary",onClick:()=>e("/"),children:"Go to Home"})]})})};export{m as default};
