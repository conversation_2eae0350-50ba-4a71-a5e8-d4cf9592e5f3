import{r as a}from"./react-core-B9nwsbCA.js";import{n as t}from"./utils-D7twGpBa.js";function e(e="all",o=1,s=20){const[i,n]=a.useState([]),[r,c]=a.useState(!0),[l,u]=a.useState(null),[d,f]=a.useState({currentPage:1,totalPages:1,totalCount:0,pageSize:s}),g=a.useCallback((async()=>{try{c(!0),u(null);const a=await t.getVideos({page:o,limit:s,category:"all"===e?void 0:e,sort:"created_at",order:"DESC"});if(!a.success||!a.data.videos)throw new Error("Failed to fetch videos");n(a.data.videos),f({currentPage:a.data.pagination.page,totalPages:a.data.pagination.pages,totalCount:a.data.pagination.total,pageSize:a.data.pagination.limit})}catch(a){const t=a instanceof Error?a.message:"Failed to fetch videos";u(t)}finally{c(!1)}}),[e,o,s]);a.useEffect((()=>{g()}),[g]);return{videos:i,isLoading:r,error:l,pagination:d,refetch:a.useCallback((()=>{g()}),[g])}}function o(e){const[o,s]=a.useState(null),[i,n]=a.useState(!0),[r,c]=a.useState(null),l=a.useCallback((async()=>{if(e)try{n(!0),c(null);const a=await t.getVideo(e);if(!a.success||!a.data)throw new Error("Video not found");s(a.data)}catch(a){const t=a instanceof Error?a.message:"Failed to fetch video";c(t)}finally{n(!1)}}),[e]);a.useEffect((()=>{l()}),[l]);return{video:o,isLoading:i,error:r,refetch:a.useCallback((()=>{l()}),[l])}}export{o as a,e as u};
