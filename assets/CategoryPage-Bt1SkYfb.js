import{r as e,j as s}from"./react-core-B9nwsbCA.js";import{d as t,u as a,c as o}from"./router-BDggJ1ol.js";import{a as r,S as i}from"./main-c4q8g3R4.js";import{u as l}from"./useVideos-iT4pmMdb.js";import{V as c}from"./VideoGrid-Bp65TBX0.js";import{P as n}from"./ui-components-D5L5LPe1.js";import{o as d}from"./icons-BWE0bDFO.js";import"./utils-Dga8D_Ky.js";import"./state-4gBW_4Nx.js";const m=()=>{const{slug:m}=t(),x=a(),[g,h]=o(),j=parseInt(g.get("page")||"1"),[b,p]=e.useState(j);e.useEffect((()=>{const e=parseInt(g.get("page")||"1");e!==b&&p(e)}),[g,b]);const{videos:u,pagination:f,isLoading:v,error:N}=l(m||"",b,24),{getCategoryBySlug:y,fetchCategories:w}=r();e.useEffect((()=>{w()}),[w]);const C=m?y(m):void 0,P=()=>{x("/",{replace:!0})};return N&&!u.length?s.jsx("div",{className:"container mx-auto px-4 py-16",children:s.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Error Loading Videos"}),s.jsx("p",{className:"text-red-200",children:N})]})}):s.jsx("div",{className:"pb-10",children:s.jsxs("div",{className:"container mx-auto px-4",children:[s.jsxs("div",{className:"flex items-center mb-6 pt-4",children:[s.jsx("button",{onClick:P,className:"mr-4 p-2 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors","aria-label":"Go back",children:s.jsx(d,{size:20,className:"text-white"})}),s.jsx("h1",{className:"text-2xl font-bold text-white",children:C?C.name:"Category Not Found"})]}),s.jsx("div",{className:"md:hidden my-4",children:s.jsx("div",{className:"relative search-bar-container px-1",children:s.jsx(i,{className:"w-full mobile-search-bar homepage-search"})})}),N&&s.jsxs("div",{className:"bg-red-500/20 border border-red-500 text-white p-4 rounded-lg mb-6",children:[s.jsx("h3",{className:"font-bold mb-2",children:"Error Loading Videos"}),s.jsx("p",{children:N})]}),!v&&u.length>0&&s.jsxs("div",{className:"flex justify-between items-center mb-4 text-sm text-gray-400",children:[s.jsxs("div",{children:["Showing ",24*(b-1)+1,"-",Math.min(24*b,f.totalCount)," of ",f.totalCount," videos"]}),s.jsxs("div",{children:["Page ",b," of ",f.totalPages]})]}),s.jsx("div",{className:"mt-6",children:s.jsx(c,{title:`${C?.name||"Category"} Videos`,videos:u,onVideoClick:e=>{x(`/video/${e.id}`)},isLoading:v,className:"mb-8"})}),!v&&u.length>0&&f.totalPages>1&&s.jsx("div",{className:"mt-12 mb-8",children:s.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[s.jsxs("div",{className:"text-center text-gray-400 text-sm",children:["Page ",b," of ",f.totalPages," • ",f.totalCount," total videos"]}),s.jsx(n,{currentPage:f.currentPage,totalPages:f.totalPages,onPageChange:e=>{p(e),h({page:e.toString()}),window.scrollTo({top:0,behavior:"smooth"})},className:"justify-center"})]})}),!v&&0===u.length&&!N&&s.jsxs("div",{className:"text-center py-16",children:[s.jsx("div",{className:"text-gray-400 text-lg mb-4",children:"No videos found in this category"}),s.jsx("p",{className:"text-gray-500 text-sm mb-4",children:"Try browsing other categories or check back later!"}),s.jsx("button",{onClick:P,className:"px-6 py-2 bg-blue-700 text-white rounded-lg hover:bg-blue-600 transition-colors",children:"Back to Home"})]})]})})};export{m as default};
