import{j as e,r as t}from"./react-core-B9nwsbCA.js";import{u as s,i as a,v as r,w as i,x as l,y as n,z as o,A as c,B as d,C as u}from"./utils-Dga8D_Ky.js";import{I as m,B as h}from"./ui-components-D5L5LPe1.js";import{c as p}from"./state-4gBW_4Nx.js";import{F as x,j as g,e as b,d as f,p as j,q as v,X as y,n as N,I as w,U as k}from"./icons-BWE0bDFO.js";import{u as P}from"./router-BDggJ1ol.js";const S=p(((e,t)=>({isUploading:!1,uploadProgress:0,error:null,uploadedVideoUrl:null,uploadedThumbnailUrl:null,uploadVideo:async t=>{const{videoFile:r,thumbnailFile:i,title:l,description:n,category:o}=t;try{const{user:t}=s.getState();if(!t)throw new Error("You must be logged in to upload videos");e({isUploading:!0,uploadProgress:0,error:null,uploadedVideoUrl:null,uploadedThumbnailUrl:null}),e({uploadProgress:5});const d=await a.uploadFile(r,"video");if(!d.success)throw new Error(`Failed to upload video file: ${d.error||"Unknown error"}`);const u=d.data.url;e({uploadProgress:70,uploadedVideoUrl:u});let m=null;if(i){e({uploadProgress:75});try{const t=await a.uploadFile(i,"thumbnail");t.success&&(m=t.data.url,e({uploadedThumbnailUrl:m}))}catch(c){}}e({uploadProgress:85}),e({uploadProgress:90});const h=await a.createVideo({title:l,description:n,video_url:u,thumbnail_url:m||void 0,category:o,duration:0});if(!h.success)throw new Error(`Failed to create video record: ${h.error||"Unknown error"}`);e({uploadProgress:100});const p=h.data.id;return setTimeout((()=>{e({isUploading:!1,uploadProgress:0})}),2e3),p}catch(d){throw e({isUploading:!1,error:d instanceof Error?d.message:"Upload failed",uploadProgress:0}),d}},clearUpload:()=>{e({isUploading:!1,uploadProgress:0,error:null,uploadedVideoUrl:null,uploadedThumbnailUrl:null})},setUploadProgress:t=>{e({uploadProgress:Math.min(100,Math.max(0,t))})}}))),U=[{id:"1",name:"Hot",slug:"hot",icon:e.jsx(x,{size:16,className:"text-red-500"})},{id:"2",name:"Trending",slug:"trending",icon:e.jsx(g,{size:16,className:"text-blue-500"})},{id:"3",name:"New",slug:"new",icon:e.jsx(b,{size:16,className:"text-green-500"})}],T=({onSelect:t,selectedCategory:s="new"})=>e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-white mb-2",children:"Category"}),e.jsx("div",{className:"grid grid-cols-3 gap-3",children:U.map((a=>e.jsxs("button",{type:"button",className:"flex flex-col items-center justify-center p-3 rounded-md transition-colors "+(s===a.slug?"bg-gray-700 border-2 border-blue-700":"bg-gray-800 border border-gray-700 hover:border-gray-600"),onClick:()=>t(a.slug),children:[e.jsx("div",{className:"mb-2",children:a.icon}),e.jsx("span",{className:"text-sm font-medium",children:a.name})]},a.id)))})]}),C=()=>{const a=P(),p=t.useRef(null),x=t.useRef(null),[g,b]=t.useState(""),[U,C]=t.useState(""),[L,R]=t.useState("new"),[A,E]=t.useState(""),[z,F]=t.useState(null),[I,V]=t.useState(null),[O,M]=t.useState(null),[D,Y]=t.useState(null),[q,$]=t.useState([]),[B,H]=t.useState(-1),[W,_]=t.useState(!1),[G,K]=t.useState({}),[J,Q]=t.useState(null),[X,Z]=t.useState(null),{uploadVideo:ee,isUploading:te,uploadProgress:se,error:ae}=S(),re=t.useCallback((e=>{S.setState({error:e})}),[]);t.useEffect((()=>{const e=r();if(Z(e),e.isSupported||re(`Your browser is missing required features: ${e.missingFeatures.join(", ")}`),i()){l();const e=setInterval((()=>{l()}),3e4);return()=>clearInterval(e)}}),[re]),t.useEffect((()=>{te&&Q(null)}),[te]);const ie=e=>{const t=e.target.files?.[0];if(t){const e=n(t);if(!e.isValid)return void K({...G,video:e.error||"Invalid video file"});F(t);try{const e=URL.createObjectURL(t);M(e)}catch(s){}if($([]),H(-1),G.video){const e={...G};delete e.video,K(e)}}};return e.jsxs("div",{className:"space-y-6",children:[(i()||/iPad|iPhone|iPod/.test(navigator.userAgent))&&e.jsx("div",{className:"p-4 bg-blue-900/50 border border-blue-700 rounded-lg",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(f,{size:20,className:"text-blue-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-blue-200 mb-1",children:"Mobile Upload Tips"}),e.jsxs("div",{className:"text-sm text-blue-300 space-y-1",children:[e.jsx("p",{children:"• Use MP4 format for best compatibility"}),e.jsx("p",{children:"• Keep files under 60MB for faster uploads"}),e.jsx("p",{children:"• Ensure stable internet connection"}),e.jsx("p",{children:"• Large files will be uploaded in chunks for reliability"}),e.jsx("p",{children:"• Stay on this page during upload to prevent logout"}),e.jsx("p",{children:"• If you experience logout issues during thumbnail selection, try refreshing the page and uploading without auto-generated thumbnails"})]})]})]})}),"undefined"!=typeof navigator&&navigator.connection&&(()=>{const t=navigator.connection;return"slow-2g"===t.effectiveType||"2g"===t.effectiveType?e.jsx("div",{className:"p-4 bg-yellow-900/50 border border-yellow-700 rounded-lg",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(f,{size:20,className:"text-yellow-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-yellow-200 mb-1",children:"Slow Connection Detected"}),e.jsx("p",{className:"text-sm text-yellow-300",children:"Your connection appears to be slow. Consider uploading smaller files or waiting for a better connection."})]})]})}):null})(),X&&!X.isSupported&&e.jsx("div",{className:"p-4 bg-red-900/50 border border-red-700 rounded-lg",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(f,{size:20,className:"text-red-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-red-200 mb-1",children:"Browser Compatibility Issue"}),e.jsxs("p",{className:"text-sm text-red-300",children:["Your browser is missing: ",X.missingFeatures.join(", "),". Please update your browser or try a different one."]})]})]})}),e.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),!(()=>{const e={};return g.trim()||(e.title="Title is required"),U.trim()||(e.description="Description is required"),z||(e.video="Video file is required"),K(e),0===Object.keys(e).length})())return;if(!z)return;const t=A.split(",").map((e=>e.trim())).filter((e=>e.length>0));try{re(null);const e=await ee({title:g,description:U,category:L,tags:t,videoFile:z,thumbnailFile:I});e?(Q("Video uploaded successfully! Redirecting to video page..."),setTimeout((()=>{a(`/video/${e}`)}),2e3)):ae||re("Upload failed. Please try again.")}catch(s){const e=s instanceof Error?s.message:"An unknown error occurred";re(e)}},className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx(m,{label:"Video Title",placeholder:"Enter a descriptive title",value:g,onChange:e=>b(e.target.value),error:G.title,fullWidth:!0}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-1",children:"Description"}),e.jsx("textarea",{className:"w-full rounded-md bg-gray-800 border border-gray-700 text-white focus:border-orange-500 focus:ring-2 focus:ring-orange-500 focus:ring-opacity-20 p-4 transition-colors placeholder:text-gray-400 min-h-[120px] "+(G.description?"border-red-500":""),placeholder:"Describe your video",value:U,onChange:e=>C(e.target.value)}),G.description&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:G.description})]}),e.jsx(T,{selectedCategory:L,onSelect:R}),e.jsx(m,{label:"Tags",placeholder:"Enter tags separated by commas",value:A,onChange:e=>E(e.target.value),fullWidth:!0})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-2",children:"Video File"}),e.jsx("input",{type:"file",ref:p,className:"hidden",accept:"video/*,video/mp4,video/webm,video/quicktime,video/x-msvideo,video/3gpp,video/x-ms-wmv",onChange:ie}),z?e.jsxs("div",{className:"relative border rounded-lg overflow-hidden h-[200px]",children:[O&&e.jsx("video",{src:O,className:"w-full h-full object-cover",controls:!0}),e.jsx("button",{type:"button",className:"absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors",onClick:()=>{O&&URL.revokeObjectURL(O),F(null),M(null),p.current&&(p.current.value="")},children:e.jsx(y,{size:16})})]}):e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-orange-500 transition-colors h-[200px] border-gray-700",onClick:e=>{e&&(e.preventDefault(),e.stopPropagation()),setTimeout((()=>{p.current?.click()}),100)},children:[e.jsx(j,{size:48,className:"text-gray-500 mb-3"}),e.jsx("p",{className:"text-gray-400 text-center",children:"Click to choose from gallery"}),e.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"MP4, WebM or MOV (max. 50MB)"})]}),(i()||/iPad|iPhone|iPod/.test(navigator.userAgent))&&e.jsx("div",{className:"flex justify-center",children:e.jsx(h,{type:"button",variant:"secondary",size:"sm",leftIcon:e.jsx(v,{size:16}),onClick:e=>{e&&(e.preventDefault(),e.stopPropagation());const t=document.createElement("input");t.type="file",t.accept="video/*",t.capture="environment",t.style.position="absolute",t.style.left="-9999px",t.style.opacity="0",t.onchange=e=>{const s=e.target.files?.[0];if(s){ie({target:{files:[s]}})}document.body.removeChild(t)},document.body.appendChild(t),setTimeout((()=>{t.click()}),100)},className:"w-full sm:w-auto",children:"Record with Camera"})})]}),G.video&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:G.video})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-200",children:"Thumbnail Image (Optional)"}),z&&e.jsx(h,{variant:"secondary",size:"sm",leftIcon:e.jsx(v,{size:16}),onClick:async()=>{if(z)try{_(!0);const{user:t}=s.getState();if(!t)return void K({...G,thumbnail:"Please log in again to generate thumbnails"});if(i()){const e=navigator.deviceMemory;if(e&&e<2)return void K({...G,thumbnail:"Device memory too low for thumbnail generation. Please upload a custom thumbnail instead."});await c(500)}const a=await d((()=>u(z,3)),(()=>{K({...G,thumbnail:"Authentication lost during thumbnail generation. Please refresh the page and try again."})})),{user:r}=s.getState();if(!r)return void K({...G,thumbnail:"Authentication lost during thumbnail generation. Please refresh the page and try again."});let l=[];try{l=a.map((e=>URL.createObjectURL(e))),$(l),i()&&setTimeout((()=>{l.forEach((e=>{try{URL.revokeObjectURL(e)}catch(t){}}))}),3e4)}catch(e){return void K({...G,thumbnail:"Failed to create thumbnail previews. Please try uploading a custom thumbnail."})}if(V(null),Y(null),G.thumbnail){const e={...G};delete e.thumbnail,K(e)}i()&&setTimeout((()=>{const{user:e}=s.getState();e||K({...G,thumbnail:"Authentication lost. Please refresh the page and try again."})}),200)}catch(t){const{user:e}=s.getState();if(!e)return void K({...G,thumbnail:"Authentication lost during thumbnail generation. Please refresh the page and try again."});let a="Failed to generate thumbnails";t instanceof Error&&(a=t.message.includes("Authentication")?"Authentication lost. Please refresh the page and try again.":t.message.includes("memory")?"Insufficient device memory. Try uploading without custom thumbnails.":t.message.includes("timeout")?"Thumbnail generation timed out. Your device may be too slow for this operation.":t.message),K({...G,thumbnail:a})}finally{_(!1)}else K({...G,thumbnail:"Please upload a video first"})},isLoading:W,disabled:W,children:"Auto-Generate"})]}),e.jsx("input",{type:"file",ref:x,className:"hidden",accept:"image/*",onChange:e=>{const t=e.target.files?.[0];if(t){const{user:e}=s.getState();if(!e)return void K({...G,thumbnail:"Please log in again to select a thumbnail"});const r=o(t);if(!r.isValid)return void K({...G,thumbnail:r.error||"Invalid image file"});V(t);try{if(i()){const e=navigator.deviceMemory;if(e&&e<2)Y(null);else{const e=URL.createObjectURL(t);Y(e),setTimeout((()=>{try{URL.revokeObjectURL(e)}catch(t){}}),5e3)}}else{const e=URL.createObjectURL(t);Y(e)}}catch(a){Y(null)}if(G.thumbnail){const e={...G};delete e.thumbnail,K(e)}setTimeout((()=>{const{user:e}=s.getState();e||K({...G,thumbnail:"Authentication lost. Please refresh the page and try again."})}),100)}}}),q.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-sm text-gray-300 mb-2",children:"Select a generated thumbnail:"}),e.jsx("div",{className:"grid grid-cols-3 gap-2",children:q.map(((t,a)=>e.jsxs("div",{className:"relative border-2 rounded overflow-hidden cursor-pointer transition-all "+(B===a?"border-orange-500 scale-105":"border-gray-700 hover:border-gray-500"),onClick:()=>(async e=>{if(e>=0&&e<q.length){const{user:a}=s.getState();if(!a)return void K({...G,thumbnail:"Please log in again to select a thumbnail"});H(e);try{const t=await fetch(q[e]),a=await t.blob(),r=new File([a],`thumbnail-${Date.now()}.jpg`,{type:"image/jpeg"});V(r),Y(q[e]),setTimeout((()=>{const{user:e}=s.getState();e||K({...G,thumbnail:"Authentication lost. Please refresh the page and try again."})}),100)}catch(t){const{user:e}=s.getState();K(e?{...G,thumbnail:"Failed to select thumbnail"}:{...G,thumbnail:"Authentication lost during thumbnail selection. Please refresh the page and try again."})}}})(a),children:[e.jsx("img",{src:t,alt:`Generated thumbnail ${a+1}`,className:"w-full aspect-video object-cover"}),B===a&&e.jsx("div",{className:"absolute top-1 right-1 bg-orange-500 text-white rounded-full p-0.5",children:e.jsx(N,{size:14})})]},a)))})]}),I&&-1!==B?e.jsxs("div",{className:"relative border rounded-lg overflow-hidden h-[200px]",children:[D&&e.jsx("img",{src:D,alt:"Thumbnail preview",className:"w-full h-full object-cover"}),e.jsx("button",{type:"button",className:"absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors",onClick:()=>{V(null),Y(null),H(-1),x.current&&(x.current.value="")},children:e.jsx(y,{size:16})})]}):e.jsxs("div",{className:"border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-orange-500 transition-colors h-[200px] "+(G.thumbnail?"border-red-500":"border-gray-700"),onClick:()=>{x.current?.click()},children:[e.jsx(w,{size:48,className:"text-gray-500 mb-3"}),e.jsx("p",{className:"text-gray-400 text-center",children:"Click to upload custom thumbnail"}),e.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"JPG, PNG or GIF (recommended: 1280×720, max 5MB)"})]}),G.thumbnail&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:G.thumbnail})]})]})]}),ae&&e.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start",children:[e.jsx(f,{size:20,className:"text-red-500 mr-2 flex-shrink-0 mt-0.5"}),e.jsxs("div",{className:"text-red-200",children:[e.jsx("p",{children:ae}),ae.includes("Storage buckets don't exist")&&e.jsxs("div",{className:"mt-2 text-sm",children:[e.jsx("p",{children:"This is likely because:"}),e.jsxs("ul",{className:"list-disc pl-5 mt-1 space-y-1",children:[e.jsx("li",{children:"The storage buckets haven't been created in Supabase"}),e.jsx("li",{children:"Your user account doesn't have permission to access the buckets"})]}),e.jsxs("p",{className:"mt-2",children:[e.jsx("strong",{children:"Solution:"})," An administrator needs to create the 'videos' and 'thumbnails' buckets in the Supabase dashboard."]})]}),ae.includes("Row Level Security (RLS) policies")&&e.jsxs("div",{className:"mt-2 text-sm",children:[e.jsx("p",{children:"This is likely because:"}),e.jsxs("ul",{className:"list-disc pl-5 mt-1 space-y-1",children:[e.jsx("li",{children:"The Row Level Security (RLS) policies for the storage buckets are not properly configured"}),e.jsx("li",{children:"Your user account doesn't have permission to upload files to these buckets"})]}),e.jsxs("p",{className:"mt-2",children:[e.jsx("strong",{children:"Solution:"})," An administrator needs to check and update the RLS policies for the storage buckets with the following SQL:"]}),e.jsx("pre",{className:"bg-gray-800 p-2 rounded mt-2 overflow-x-auto text-xs",children:"-- First, check existing policies\nSELECT policyname, tablename, cmd, qual, with_check\nFROM pg_policies\nWHERE tablename = 'objects' AND schemaname = 'storage';\n\n-- Then, update the policies if needed\nALTER POLICY \"Users can upload videos to their own folder\"\nON storage.objects\nWITH CHECK (\n  bucket_id = 'videos' AND\n  auth.uid()::text = (storage.foldername(name))[1]\n);\n\nALTER POLICY \"Users can upload thumbnails to their own folder\"\nON storage.objects\nWITH CHECK (\n  bucket_id = 'thumbnails' AND\n  auth.uid()::text = (storage.foldername(name))[1]\n);"})]}),(ae.includes("violates foreign key constraint")||ae.includes("user profile does not exist"))&&e.jsxs("div",{className:"mt-2 text-sm",children:[e.jsx("p",{children:"This is likely because your user profile hasn't been properly created in the database."}),e.jsxs("p",{className:"mt-2",children:[e.jsx("strong",{children:"Solution:"})," Please try the following steps:"]}),e.jsxs("ol",{className:"list-decimal pl-5 mt-1 space-y-1",children:[e.jsx("li",{children:"Log out and log back in to trigger automatic profile creation"}),e.jsx("li",{children:"Refresh the page and try uploading again"}),e.jsx("li",{children:"If the issue persists, contact support"})]})]})]})]}),J&&!ae&&e.jsxs("div",{className:"bg-green-500/20 border border-green-500 rounded-md p-3 flex items-start",children:[e.jsx(N,{size:20,className:"text-green-500 mr-2 flex-shrink-0 mt-0.5"}),e.jsx("p",{className:"text-green-200",children:J})]}),te&&e.jsxs("div",{className:"bg-gray-700 rounded-full overflow-hidden",children:[e.jsx("div",{className:"bg-orange-500 h-2 transition-all duration-300",style:{width:`${se}%`}}),e.jsxs("p",{className:"text-center text-sm text-gray-400 mt-2",children:["Uploading: ",se,"%"]})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx(h,{variant:"ghost",type:"button",onClick:()=>a("/"),children:"Cancel"}),e.jsx(h,{variant:"primary",type:"submit",leftIcon:e.jsx(k,{size:18}),isLoading:te,disabled:te,children:"Upload Video"})]})]})]})},L=()=>{const a=P(),{user:r,isLoading:i,isApproved:l,profile:n}=s(),[o,c]=t.useState(!1);return t.useEffect((()=>{const e=setTimeout((()=>{c(!0)}),2e3);return()=>clearTimeout(e)}),[]),t.useEffect((()=>{!o||i||r||a("/")}),[r,i,a,o]),i||!o?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"flex flex-col justify-center items-center h-64",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"}),e.jsx("p",{className:"text-gray-400 text-center",children:i?"Loading your account...":"Checking authentication..."}),e.jsx("p",{className:"text-gray-500 text-sm text-center mt-2",children:"Please wait while we verify your login status"})]})}):r?l?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("h1",{className:"text-2xl md:text-3xl font-bold mb-6",children:"Upload Video"}),e.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:e.jsx(C,{})})]}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("h1",{className:"text-2xl md:text-3xl font-bold mb-6",children:"Upload Video"}),e.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:e.jsxs("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[e.jsx(f,{size:48,className:"text-yellow-500 mb-4"}),e.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Approval Required"}),e.jsx("p",{className:"text-gray-400 max-w-md mb-4",children:"Your account is pending approval. New users need to be approved before they can upload videos."}),e.jsx("p",{className:"text-gray-500 text-sm",children:"Please check back later or contact an administrator for assistance."})]})})]}):null};export{L as default};
