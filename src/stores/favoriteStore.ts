import { create } from 'zustand';
import { apiClient, Video as ApiVideo } from '../lib/api';
import { Video } from '../types';
import { useAuthStore } from './authStore';

interface FavoriteState {
  favorites: Video[];
  isLoading: boolean;
  error: string | null;

  // Fetch operations
  fetchFavorites: () => Promise<void>;

  // Favorite operations
  addToFavorites: (videoId: string) => Promise<boolean>;
  removeFromFavorites: (videoId: string) => Promise<boolean>;
  isFavorite: (videoId: string) => boolean;
}

// Helper function to convert API video to Video type
const convertApiVideoToVideo = (video: ApiVideo): Video => ({
  id: video.id,
  title: video.title,
  description: video.description || '',
  thumbnailUrl: video.thumbnail_url || 'https://placehold.co/640x360/gray/white?text=No+Thumbnail',
  videoUrl: video.video_url,
  duration: video.duration || 0,
  views: video.views || 0,
  likes: video.likes || 0,
  createdAt: video.created_at,
  updatedAt: video.updated_at || video.created_at,
  publishedAt: video.created_at, // Use created_at as published date
  scheduledFor: undefined,
  status: 'public',
  isHD: video.is_hd || false,
  isPremium: false,
  tags: video.tags ? video.tags.split(',').map(tag => tag.trim()) : [],
  category: video.category || 'uncategorized',
  creator: {
    id: video.user_id,
    email: '',
    avatar: video.user_avatar || 'https://placehold.co/150/gray/white?text=User',
    isVerified: false,
    isCreator: true,
    subscriberCount: 0
  }
});

export const useFavoriteStore = create<FavoriteState>((set, get) => ({
  favorites: [],
  isLoading: false,
  error: null,

  fetchFavorites: async () => {
    const { user } = useAuthStore.getState();

    if (!user) {
      set({ favorites: [], error: null });
      return;
    }

    set({ isLoading: true, error: null });

    try {
      const response = await apiClient.getFavorites();

      if (response.success && response.data.videos) {
        const favorites = response.data.videos.map(convertApiVideoToVideo);
        set({ favorites, isLoading: false });
      } else {
        throw new Error('Failed to fetch favorites');
      }
    } catch (error) {
      console.error('Error fetching favorites:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  addToFavorites: async (videoId: string) => {
    const { user } = useAuthStore.getState();

    if (!user) {
      set({ error: 'You must be logged in to add favorites' });
      return false;
    }

    try {
      const response = await apiClient.addToFavorites(videoId);

      if (response.success) {
        // Refresh favorites to get updated list
        await get().fetchFavorites();
        return true;
      } else {
        throw new Error('Failed to add to favorites');
      }
    } catch (error) {
      console.error('Error adding to favorites:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
      return false;
    }
  },

  removeFromFavorites: async (videoId: string) => {
    const { user } = useAuthStore.getState();

    if (!user) {
      set({ error: 'You must be logged in to remove favorites' });
      return false;
    }

    try {
      const response = await apiClient.removeFromFavorites(videoId);

      if (response.success) {
        // Update local state immediately for better UX
        set(state => ({
          favorites: state.favorites.filter(video => video.id !== videoId)
        }));
        return true;
      } else {
        throw new Error('Failed to remove from favorites');
      }
    } catch (error) {
      console.error('Error removing from favorites:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
      return false;
    }
  },

  isFavorite: (videoId: string) => {
    return get().favorites.some(video => video.id === videoId);
  }
}));
