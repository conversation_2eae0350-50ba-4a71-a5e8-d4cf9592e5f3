<?php
/**
 * Enhanced Database Configuration
 * Singleton pattern with connection pooling and error handling
 */

class Database {
    private static $instance = null;
    private $connection;
    private $host;
    private $database;
    private $username;
    private $password;
    private $charset = 'utf8mb4';
    
    private function __construct() {
        $this->loadConfig();
        $this->connect();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance(): self {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Get PDO connection
     */
    public function getConnection(): PDO {
        // Check if connection is still alive
        if (!$this->isConnectionAlive()) {
            $this->reconnect();
        }
        return $this->connection;
    }
    
    /**
     * Load database configuration
     */
    private function loadConfig(): void {
        // Try to load from environment variables first
        $this->host = $_ENV['DB_HOST'] ?? 'localhost';
        $this->database = $_ENV['DB_NAME'] ?? 'bluefilmx_db';
        $this->username = $_ENV['DB_USER'] ?? 'root';
        $this->password = $_ENV['DB_PASS'] ?? '';
        
        // Fallback to config file if environment variables not set
        if (!isset($_ENV['DB_HOST'])) {
            $configFile = __DIR__ . '/database.ini';
            if (file_exists($configFile)) {
                $config = parse_ini_file($configFile);
                $this->host = $config['host'] ?? $this->host;
                $this->database = $config['database'] ?? $this->database;
                $this->username = $config['username'] ?? $this->username;
                $this->password = $config['password'] ?? $this->password;
            }
        }
    }
    
    /**
     * Establish database connection
     */
    private function connect(): void {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->database};charset={$this->charset}";
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => true,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset} COLLATE utf8mb4_unicode_ci"
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
            // Set timezone
            $this->connection->exec("SET time_zone = '+00:00'");
            
        } catch (PDOException $e) {
            error_log('Database connection failed: ' . $e->getMessage());
            throw new Exception('Database connection failed');
        }
    }
    
    /**
     * Reconnect to database
     */
    private function reconnect(): void {
        $this->connection = null;
        $this->connect();
    }
    
    /**
     * Check if connection is alive
     */
    private function isConnectionAlive(): bool {
        try {
            $this->connection->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction(): bool {
        return $this->connection->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit(): bool {
        return $this->connection->commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback(): bool {
        return $this->connection->rollback();
    }
    
    /**
     * Execute a prepared statement with parameters
     */
    public function execute(string $sql, array $params = []): PDOStatement {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log('Database query failed: ' . $e->getMessage());
            error_log('SQL: ' . $sql);
            error_log('Params: ' . json_encode($params));
            throw $e;
        }
    }
    
    /**
     * Get last insert ID
     */
    public function lastInsertId(): string {
        return $this->connection->lastInsertId();
    }
    
    /**
     * Prevent cloning
     */
    private function __clone() {}
    
    /**
     * Prevent unserialization
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * Authentication helper functions
 */
class AuthHelper {
    private static $db;
    
    public static function init() {
        self::$db = Database::getInstance()->getConnection();
    }
    
    /**
     * Get current user from session or JWT token
     */
    public static function getCurrentUser(): ?array {
        // Try to get user from session first
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (isset($_SESSION['user_id'])) {
            return self::getUserById($_SESSION['user_id']);
        }
        
        // Try to get user from JWT token
        $token = self::getBearerToken();
        if ($token) {
            $jwtHelper = new JWTHelper();
            $payload = $jwtHelper->validateToken($token);
            
            if ($payload && isset($payload['user_id'])) {
                return self::getUserById($payload['user_id']);
            }
        }
        
        return null;
    }
    
    /**
     * Require authentication
     */
    public static function requireAuth(): array {
        $user = self::getCurrentUser();
        
        if (!$user) {
            http_response_code(401);
            echo json_encode(['error' => 'Authentication required']);
            exit;
        }
        
        return $user;
    }
    
    /**
     * Check if user has specific role
     */
    public static function hasRole(string $roleName, ?array $user = null): bool {
        if (!$user) {
            $user = self::getCurrentUser();
        }
        
        if (!$user) {
            return false;
        }
        
        $stmt = self::$db->prepare("
            SELECT r.name FROM user_roles ur 
            JOIN roles r ON ur.role_id = r.id 
            WHERE ur.user_id = ? AND r.name = ?
        ");
        $stmt->execute([$user['id'], $roleName]);
        
        return $stmt->fetch() !== false;
    }
    
    /**
     * Check if user has specific permission
     */
    public static function hasPermission(string $resource, string $action, ?array $user = null): bool {
        if (!$user) {
            $user = self::getCurrentUser();
        }
        
        if (!$user) {
            return false;
        }
        
        $stmt = self::$db->prepare("
            SELECT r.permissions FROM user_roles ur 
            JOIN roles r ON ur.role_id = r.id 
            WHERE ur.user_id = ?
        ");
        $stmt->execute([$user['id']]);
        
        while ($role = $stmt->fetch()) {
            $permissions = json_decode($role['permissions'], true);
            if (isset($permissions[$resource]) && in_array($action, $permissions[$resource])) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get user by ID
     */
    private static function getUserById(string $userId): ?array {
        $stmt = self::$db->prepare("SELECT * FROM users WHERE id = ? AND is_active = TRUE");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if ($user) {
            unset($user['password_hash']);
            return $user;
        }
        
        return null;
    }
    
    /**
     * Get Bearer token from Authorization header
     */
    private static function getBearerToken(): ?string {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? null;
        
        if ($authHeader && preg_match('/Bearer\s+(\S+)/', $authHeader, $matches)) {
            return $matches[1];
        }
        
        return null;
    }
}

// Initialize AuthHelper
AuthHelper::init();