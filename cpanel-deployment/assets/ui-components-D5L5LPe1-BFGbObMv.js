import{j as e,r as s}from"./react-core-B9nwsbCA.js";import{g as t,a as r,f as a,b as i,c as l,d as n,e as o,h as c}from"./utils-Dga8D_Ky.js";import{C as d,a as x,b as m,c as h,L as u,A as b,X as g,d as p}from"./icons-BWE0bDFO.js";const v=({variant:s="primary",size:t="md",isLoading:r=!1,leftIcon:a,rightIcon:i,fullWidth:l=!1,className:n="",disabled:o,children:c,...d})=>{const x=l?"w-full":"";return e.jsxs("button",{className:`inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-offset-gray-900 focus:ring-blue-700 disabled:opacity-50 disabled:pointer-events-none ${{primary:"bg-blue-700 hover:bg-blue-800 text-white",secondary:"bg-gray-700 hover:bg-gray-600 text-white",outline:"bg-transparent border border-blue-700 text-blue-700 hover:bg-blue-700/10",ghost:"bg-transparent hover:bg-blue-700/10 text-blue-700",danger:"bg-red-500 hover:bg-red-600 text-white",success:"bg-green-600 hover:bg-green-700 text-white"}[s]} ${{sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-6 py-3"}[t]} ${x} ${n}`,disabled:r||o,...d,children:[r?e.jsx("span",{className:"mr-2 inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em]"}):a?e.jsx("span",{className:"mr-2",children:a}):null,c,i&&!r&&e.jsx("span",{className:"ml-2",children:i})]})},f=({label:s,error:t,leftIcon:r,rightIcon:a,fullWidth:i=!1,className:l="",...n})=>e.jsxs("div",{className:(i?"w-full":"")+" space-y-1",children:[s&&e.jsx("label",{className:"block text-sm font-medium text-gray-200",children:s}),e.jsxs("div",{className:"relative",children:[r&&e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400",children:r}),e.jsx("input",{className:`\n            w-full rounded-md bg-gray-800 border border-gray-700 text-white focus:border-blue-700 focus:ring-2 focus:ring-blue-700 focus:ring-opacity-20\n            ${r?"pl-10":"pl-4"}\n            ${a?"pr-10":"pr-4"}\n            py-2 transition-colors\n            placeholder:text-gray-400\n            ${t?"border-red-500":""}\n            ${l}\n          `,...n}),a&&e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400",children:a})]}),t&&e.jsx("p",{className:"text-sm text-red-500",children:t})]}),j=s.memo((({src:n,alt:o,fallbackSrc:c,className:d="",width:x=640,height:m=360,sizes:h="100vw",priority:u=!1,onLoad:b,onError:g,blur:p=!1,objectFit:v="cover",fetchPriority:f="auto",decoding:j="async",progressive:w=!0,enableModernFormats:y=!0})=>{const N=l(x,m,"No Image"),k=y?t():"auto",C=r(a(n),x,k),$=w?i(a(n)):null,[z,M]=s.useState(u?C:$||""),[P,S]=s.useState(!1),[I,L]=s.useState(u),[D,F]=s.useState(!1),[V,E]=s.useState(!1),H=s.useRef(null),W=s.useRef(null);s.useEffect((()=>(!u&&H.current?(W.current=new IntersectionObserver((e=>{const[s]=e;if(s.isIntersecting){if(L(!0),w&&$&&!D){M($),F(!0);const e=new Image;e.onload=()=>{M(C),E(!0)},e.src=C}else M(C);W.current&&W.current.disconnect()}}),{rootMargin:"100px",threshold:.01}),W.current.observe(H.current)):u&&(M(C),E(!0)),()=>{W.current&&W.current.disconnect()})),[u,C,w,$,D]),s.useEffect((()=>{(u||I)&&(M(C),S(!1))}),[C,u,I]);const O=p||w&&!V?{filter:!P||w&&!V?"blur(10px)":"none",transition:"filter 0.4s ease-out, opacity 0.3s ease-in-out"}:{transition:"opacity 0.3s ease-in-out"};return e.jsx("img",{ref:H,src:z||(u?C:c||N),alt:o,className:`${d} ${P||u?"opacity-100":"opacity-50"}`,loading:u?"eager":"lazy",fetchPriority:u?"high":f,decoding:j,onLoad:()=>{S(!0),b&&b()},onError:()=>{const e=c||N;z!==e&&M(e),g&&g()},width:x,height:m,sizes:h,srcSet:u||I?(()=>{if(x&&n&&!n.startsWith("data:")&&!n.startsWith("blob:")&&n.includes("supabase.co")&&n.includes("storage/v1"))try{const e=n.split("?")[0];return[Math.round(.5*x),x,Math.round(1.5*x),Math.round(2*x)].map((s=>`${e}?width=${s} ${s}w`)).join(",")}catch(e){return}})():void 0,style:{...O,objectFit:v}})})),w=({video:s,onClick:t})=>{const r=s.isHD;return e.jsxs("div",{className:"relative overflow-hidden cursor-pointer mb-4",onClick:()=>{t&&t(s)},children:[e.jsxs("div",{className:"relative aspect-video overflow-hidden bg-gray-900",children:[e.jsx(j,{src:s.thumbnailUrl||n(),alt:s.title,fallbackSrc:n(),className:"w-full h-full object-cover",width:640,height:360,sizes:"(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw",progressive:!0,enableModernFormats:!0,fetchPriority:"low"}),s.duration>0&&e.jsx("div",{className:"absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded",children:o(s.duration)}),r&&e.jsx("div",{className:"absolute top-2 right-2 bg-blue-700 text-white text-xs px-2 py-1 rounded-sm font-medium",children:"HD"})]}),e.jsxs("div",{className:"py-2",children:[e.jsx("h3",{className:"text-white font-medium line-clamp-2 text-sm",children:s.title}),e.jsx("div",{className:"flex items-center text-xs text-gray-400 mt-1",children:e.jsxs("span",{children:[c(s.views||0)," Views"]})})]})]})},y=({video:s,onClick:t})=>e.jsxs("div",{className:"relative overflow-hidden cursor-pointer mb-4",onClick:()=>{t&&t(s)},children:[e.jsxs("div",{className:"relative aspect-video overflow-hidden bg-gray-900",children:[e.jsx(j,{src:s.thumbnailUrl||n(),alt:s.title,fallbackSrc:n(),className:"w-full h-full object-cover",width:640,height:360,sizes:"(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw",progressive:!0,enableModernFormats:!0,fetchPriority:"low"}),s.duration>0&&e.jsx("div",{className:"absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded",children:o(s.duration)}),s.isHD&&e.jsx("div",{className:"absolute top-2 right-2 bg-blue-700 text-white text-xs px-2 py-1 rounded-sm font-medium",children:"HD"}),s.progress&&e.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-1 bg-gray-700",children:e.jsx("div",{className:"h-full bg-blue-700",style:{width:`${s.progress.percent}%`}})})]}),e.jsxs("div",{className:"py-2",children:[e.jsx("h3",{className:"text-white font-medium line-clamp-2 text-sm",children:s.title}),e.jsx("div",{className:"flex items-center text-xs text-gray-400 mt-1",children:e.jsxs("span",{children:[c(s.views||0)," Views"]})}),s.progress&&!s.progress.completed&&e.jsxs("div",{className:"mt-1 text-xs text-blue-700",children:["Continue watching (",Math.floor(s.progress.percent),"% complete)"]})]})]}),N=({compact:s=!1})=>e.jsxs("div",{className:"bg-gray-800 rounded-lg overflow-hidden shadow-lg animate-pulse "+(s?"w-[280px]":"w-full"),children:[e.jsx("div",{className:"aspect-video bg-gray-700"}),e.jsx("div",{className:"p-3",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"w-9 h-9 rounded-full bg-gray-700 mr-3"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 bg-gray-700 rounded w-3/4 mb-2"}),e.jsx("div",{className:"h-3 bg-gray-700 rounded w-1/2"})]})]})})]}),k=({currentPage:s,totalPages:t,onPageChange:r,className:a=""})=>{if(t<=1)return null;const i=(()=>{const e=[];if(t<=5)for(let s=1;s<=t;s++)e.push(s);else{const r=1,a=t;for(let i=Math.max(2,s-1);i<=Math.min(t-1,s+1);i++)e.push(i);e.includes(r)||(e.unshift(r),e[1]>r+1&&e.splice(1,0,-1)),e.includes(a)||(e.push(a),e[e.length-2]<a-1&&e.splice(e.length-1,0,-1))}return e})();return e.jsxs("div",{className:`flex items-center justify-center space-x-1 ${a}`,children:[e.jsx("button",{onClick:()=>r(1),disabled:1===s,className:"p-2 rounded-md "+(1===s?"text-gray-500 cursor-not-allowed":"text-white hover:bg-gray-700"),"aria-label":"First page",children:e.jsx(d,{size:18})}),e.jsx("button",{onClick:()=>r(s-1),disabled:1===s,className:"p-2 rounded-md "+(1===s?"text-gray-500 cursor-not-allowed":"text-white hover:bg-gray-700"),"aria-label":"Previous page",children:e.jsx(x,{size:18})}),i.map(((t,a)=>-1===t?e.jsx("span",{className:"px-3 py-2 text-gray-500",children:"..."},`ellipsis-${a}`):e.jsx("button",{onClick:()=>r(t),className:"px-3 py-1 rounded-md "+(s===t?"bg-blue-700 text-white":"text-white hover:bg-gray-700"),"aria-label":`Page ${t}`,"aria-current":s===t?"page":void 0,children:t},t))),e.jsx("button",{onClick:()=>r(s+1),disabled:s===t,className:"p-2 rounded-md "+(s===t?"text-gray-500 cursor-not-allowed":"text-white hover:bg-gray-700"),"aria-label":"Next page",children:e.jsx(m,{size:18})}),e.jsx("button",{onClick:()=>r(t),disabled:s===t,className:"p-2 rounded-md "+(s===t?"text-gray-500 cursor-not-allowed":"text-white hover:bg-gray-700"),"aria-label":"Last page",children:e.jsx(h,{size:18})})]})},C=({series:s,onClick:t,compact:r=!1})=>{const a=s.videos[0],i=s.videos.reduce(((e,s)=>e+s.duration),0),l=s.videos.reduce(((e,s)=>e+s.views),0);return e.jsx("div",{className:""+(r?"w-[280px]":"w-full"),children:e.jsxs("div",{className:"relative overflow-hidden cursor-pointer mb-4",onClick:()=>{t&&a&&t(a.id)},children:[e.jsxs("div",{className:"relative aspect-video overflow-hidden bg-gray-900",children:[e.jsx(j,{src:a.thumbnailUrl||n(),alt:s.baseTitle,fallbackSrc:n(),className:"w-full h-full object-cover",width:640,height:360,sizes:"(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"}),i>0&&e.jsx("div",{className:"absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded",children:o(i)}),e.jsxs("div",{className:"absolute top-2 right-2 bg-blue-700 text-white text-xs px-2 py-1 rounded-sm font-medium flex items-center",children:[e.jsx(u,{size:12,className:"mr-1"}),s.totalVideos," videos"]})]}),e.jsxs("div",{className:"py-2",children:[e.jsx("h3",{className:"text-white font-medium line-clamp-2 text-sm",children:s.baseTitle}),e.jsx("div",{className:"flex items-center text-xs text-gray-400 mt-1",children:e.jsxs("span",{children:[c(l)," Views"]})})]})]})})},$=({isOpen:s,onClose:t,onConfirm:r,title:a,message:i,isLoading:l=!1,error:n=null})=>s?e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70",children:e.jsxs("div",{className:"bg-gray-800 rounded-lg w-full max-w-md",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[e.jsxs("h2",{className:"text-xl font-bold text-white flex items-center",children:[e.jsx(b,{size:20,className:"text-red-500 mr-2"}),a]}),e.jsx("button",{className:"p-1 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors",onClick:t,disabled:l,children:e.jsx(g,{size:20})})]}),e.jsxs("div",{className:"p-4",children:[e.jsx("p",{className:"text-gray-300 mb-4",children:i}),n&&e.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start mb-4",children:[e.jsx(p,{size:20,className:"text-red-500 mr-2 flex-shrink-0 mt-0.5"}),e.jsx("p",{className:"text-red-200",children:n})]}),e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx(v,{variant:"ghost",type:"button",onClick:t,disabled:l,children:"Cancel"}),e.jsx(v,{variant:"danger",type:"button",onClick:r,isLoading:l,disabled:l,children:"Delete"})]})]})]})}):null;export{v as B,$ as D,f as I,j as O,k as P,N as V,C as a,y as b,w as c};
