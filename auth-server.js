import express from 'express';
import cors from 'cors';
import mysql from 'mysql2/promise';

const app = express();
const PORT = 3001;

// Create MySQL database connection
const createConnection = async () => {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASS || '',
      database: process.env.DB_NAME || 'bluefilmx_db',
      charset: 'utf8mb4'
    });
    
    console.log('Connected to MySQL database');
    return connection;
  } catch (error) {
    console.error('Failed to connect to MySQL:', error.message);
    return null;
  }
};

// Initialize database tables
const initializeDatabase = async (connection) => {
  try {
    // Users table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user (
        id VARCHAR(255) PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        emailVerified BOOLEAN NOT NULL DEFAULT FALSE,
        name VA<PERSON><PERSON><PERSON>(255),
        createdAt BIGINT NOT NULL,
        updatedAt BIGINT NOT NULL,
        image TEXT
      )
    `);

    // Accounts table for OAuth providers
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS account (
        id VARCHAR(255) PRIMARY KEY,
        accountId VARCHAR(255) NOT NULL,
        providerId VARCHAR(255) NOT NULL,
        userId VARCHAR(255) NOT NULL,
        accessToken TEXT,
        refreshToken TEXT,
        idToken TEXT,
        accessTokenExpiresAt BIGINT,
        refreshTokenExpiresAt BIGINT,
        scope TEXT,
        password VARCHAR(255),
        createdAt BIGINT NOT NULL,
        updatedAt BIGINT NOT NULL,
        FOREIGN KEY (userId) REFERENCES user (id) ON DELETE CASCADE
      )
    `);

    // Sessions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS session (
        id VARCHAR(255) PRIMARY KEY,
        expiresAt BIGINT NOT NULL,
        token VARCHAR(255) UNIQUE NOT NULL,
        createdAt BIGINT NOT NULL,
        updatedAt BIGINT NOT NULL,
        ipAddress VARCHAR(45),
        userAgent TEXT,
        userId VARCHAR(255) NOT NULL,
        FOREIGN KEY (userId) REFERENCES user (id) ON DELETE CASCADE
      )
    `);

    // Verification table for email verification
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS verification (
        id VARCHAR(255) PRIMARY KEY,
        identifier VARCHAR(255) NOT NULL,
        value VARCHAR(255) NOT NULL,
        expiresAt BIGINT NOT NULL,
        createdAt BIGINT,
        updatedAt BIGINT
      )
    `);

    console.log('Database tables initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database tables:', error.message);
  }
};

// Create a simple auth API that works with MySQL
const createAuthAPI = (connection) => {
  return {
    // Sign up endpoint
    signUp: async (email, password, name) => {
      try {
        const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const now = Date.now();
        
        await connection.execute(
          'INSERT INTO user (id, email, name, emailVerified, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?)',
          [userId, email, name || '', false, now, now]
        );
        
        // Create account record with password
        const accountId = `account_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await connection.execute(
          'INSERT INTO account (id, accountId, providerId, userId, password, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?)',
          [accountId, accountId, 'credential', userId, password, now, now]
        );
        
        return { success: true, userId, email, name };
      } catch (error) {
        console.error('Sign up error:', error);
        return { success: false, error: error.message };
      }
    },
    
    // Sign in endpoint
    signIn: async (email, password) => {
      try {
        const [users] = await connection.execute(
          'SELECT u.*, a.password FROM user u JOIN account a ON u.id = a.userId WHERE u.email = ? AND a.providerId = "credential"',
          [email]
        );
        
        if (users.length === 0) {
          return { success: false, error: 'Invalid credentials' };
        }
        
        const user = users[0];
        // In a real app, you'd hash and compare passwords properly
        if (user.password !== password) {
          return { success: false, error: 'Invalid credentials' };
        }
        
        // Create session
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const token = `token_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`;
        const expiresAt = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7 days
        const now = Date.now();
        
        await connection.execute(
          'INSERT INTO session (id, token, userId, expiresAt, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?)',
          [sessionId, token, user.id, expiresAt, now, now]
        );
        
        return { 
          success: true, 
          user: { id: user.id, email: user.email, name: user.name },
          session: { token, expiresAt }
        };
      } catch (error) {
        console.error('Sign in error:', error);
        return { success: false, error: error.message };
      }
    },
    
    // Get session endpoint
    getSession: async (token) => {
      try {
        const [sessions] = await connection.execute(
          'SELECT s.*, u.id as userId, u.email, u.name FROM session s JOIN user u ON s.userId = u.id WHERE s.token = ? AND s.expiresAt > ?',
          [token, Date.now()]
        );
        
        if (sessions.length === 0) {
          return { success: false, error: 'Invalid or expired session' };
        }
        
        const session = sessions[0];
        return {
          success: true,
          user: { id: session.userId, email: session.email, name: session.name },
          session: { token: session.token, expiresAt: session.expiresAt }
        };
      } catch (error) {
        console.error('Get session error:', error);
        return { success: false, error: error.message };
      }
    },
    
    // Sign out endpoint
    signOut: async (token) => {
      try {
        await connection.execute('DELETE FROM session WHERE token = ?', [token]);
        return { success: true };
      } catch (error) {
        console.error('Sign out error:', error);
        return { success: false, error: error.message };
      }
    }
  };
};

// Enable CORS
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));

// Parse JSON bodies
app.use(express.json());

// Initialize everything
const startServer = async () => {
  // Initialize database connection
  const connection = await createConnection();
  if (!connection) {
    console.error('Failed to connect to database. Exiting.');
    process.exit(1);
  }

  await initializeDatabase(connection);
  
  // Create auth API
  const authAPI = createAuthAPI(connection);

  // Auth routes
  app.post('/api/auth/sign-up', async (req, res) => {
    const { email, password, name } = req.body;
    const result = await authAPI.signUp(email, password, name);
    res.json(result);
  });

  app.post('/api/auth/sign-in', async (req, res) => {
    const { email, password } = req.body;
    const result = await authAPI.signIn(email, password);
    res.json(result);
  });

  app.post('/api/auth/sign-out', async (req, res) => {
    const { token } = req.body;
    const result = await authAPI.signOut(token);
    res.json(result);
  });

  app.get('/api/auth/session', async (req, res) => {
    const token = req.headers.authorization?.replace('Bearer ', '') || req.query.token;
    if (!token) {
      return res.json({ success: false, error: 'No token provided' });
    }
    const result = await authAPI.getSession(token);
    res.json(result);
  });

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.json({ status: 'ok', database: 'mysql', timestamp: new Date().toISOString() });
  });

  app.listen(PORT, () => {
    console.log(`MySQL Auth API server running on http://localhost:${PORT}`);
    console.log('Available endpoints:');
    console.log('  POST /api/auth/sign-up');
    console.log('  POST /api/auth/sign-in');
    console.log('  POST /api/auth/sign-out');
    console.log('  GET  /api/auth/session');
    console.log('  GET  /health');
  });
};

startServer().catch(console.error);