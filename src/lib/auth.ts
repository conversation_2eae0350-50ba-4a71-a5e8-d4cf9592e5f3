/**
 * BetterAuth Configuration
 * Replaces the custom PHP authentication system
 */

import { betterAuth } from "better-auth";

export const auth = betterAuth({
  database: {
    provider: "mysql",
    url: process.env.DATABASE_URL || "mysql://root:@localhost:3306/bluefilmx_db"
  },
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
    minPasswordLength: 6,
    maxPasswordLength: 128
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
    cookieCache: {
      enabled: true,
      maxAge: 60 * 5 // 5 minutes
    }
  },
  user: {
    additionalFields: {
      firstName: {
        type: "string",
        required: false
      },
      lastName: {
        type: "string",
        required: false
      },
      avatarUrl: {
        type: "string",
        required: false
      },
      bio: {
        type: "string",
        required: false
      },
      isActive: {
        type: "boolean",
        defaultValue: true
      },
      isVerified: {
        type: "boolean",
        defaultValue: false
      },
      isApproved: {
        type: "boolean",
        defaultValue: true
      },
      profileVisibility: {
        type: "string",
        defaultValue: "public"
      },
      allowMessages: {
        type: "boolean",
        defaultValue: true
      }
    }
  },
  plugins: [],
  trustedOrigins: [
    "http://localhost:3000",
    "http://localhost:5173"
  ],
  secret: process.env.BETTER_AUTH_SECRET || "your-secret-key-change-in-production",
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3000"
});

export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.User;