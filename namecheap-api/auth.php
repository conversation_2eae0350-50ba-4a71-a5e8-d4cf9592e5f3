<?php
/**
 * Enhanced Authentication API Endpoint
 * Handles all authentication-related requests with improved security
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../auth-system/backend/AuthService.php';
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/utils/RateLimiter.php';
require_once __DIR__ . '/utils/SecurityHelper.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    $database = Database::getInstance();
    $authService = new AuthService();
    $rateLimiter = new RateLimiter($database->getConnection());
    $securityHelper = new SecurityHelper();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';
    $input = json_decode(file_get_contents('php://input'), true) ?? [];
    
    // Get client IP and user agent for security
    $clientIP = $securityHelper->getClientIP();
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    $deviceInfo = [
        'ip' => $clientIP,
        'user_agent' => $userAgent,
        'device_type' => $securityHelper->detectDeviceType($userAgent)
    ];
    
    // Rate limiting for sensitive operations
    $sensitiveActions = ['login', 'register', 'reset-password', 'verify-email'];
    if (in_array($action, $sensitiveActions)) {
        if (!$rateLimiter->checkLimit($clientIP, $action)) {
            http_response_code(429);
            echo json_encode(['error' => 'Too many requests. Please try again later.']);
            exit;
        }
    }
    
    switch ($method) {
        case 'POST':
            handlePostRequest($authService, $action, $input, $deviceInfo);
            break;
            
        case 'GET':
            handleGetRequest($authService, $action);
            break;
            
        case 'PUT':
            handlePutRequest($authService, $action, $input);
            break;
            
        case 'DELETE':
            handleDeleteRequest($authService, $action);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log('Auth API error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

/**
 * Handle POST requests
 */
function handlePostRequest($authService, $action, $input, $deviceInfo) {
    switch ($action) {
        case 'register':
            $result = $authService->register($input);
            break;
            
        case 'login':
            $identifier = $input['username'] ?? $input['email'] ?? '';
            $password = $input['password'] ?? '';
            $result = $authService->login($identifier, $password, $deviceInfo);
            
            // Set session for backward compatibility
            if ($result['success'] && isset($result['data']['user'])) {
                $_SESSION['user_id'] = $result['data']['user']['id'];
                $_SESSION['session_id'] = $result['data']['session_id'];
            }
            break;
            
        case 'logout':
            $sessionId = $_SESSION['session_id'] ?? $input['session_id'] ?? '';
            $result = $authService->logout($sessionId);
            
            // Clear session
            session_destroy();
            
            // Ensure we return the result
            if ($result['success']) {
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            return;
            break;
            
        case 'verify-email':
            $token = $input['token'] ?? '';
            $result = $authService->verifyEmail($token);
            break;
            
        case 'request-password-reset':
            $email = $input['email'] ?? '';
            $result = $authService->requestPasswordReset($email);
            break;
            
        case 'reset-password':
            $token = $input['token'] ?? '';
            $password = $input['password'] ?? '';
            $result = $authService->resetPassword($token, $password);
            break;
            
        case 'refresh-token':
            $result = handleTokenRefresh($authService);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            return;
    }
    
    if ($result['success']) {
        echo json_encode($result);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
}

/**
 * Handle GET requests
 */
function handleGetRequest($authService, $action) {
    switch ($action) {
        case 'user':
        case 'current-user':
            $user = AuthHelper::getCurrentUser();
            if ($user) {
                echo json_encode(['success' => true, 'data' => ['user' => $user]]);
            } else {
                http_response_code(401);
                echo json_encode(['error' => 'Not authenticated']);
            }
            break;
            
        case 'check-session':
            $sessionId = $_SESSION['session_id'] ?? $_GET['session_id'] ?? '';
            if ($sessionId) {
                $user = $authService->getCurrentUser($sessionId);
                if ($user) {
                    echo json_encode(['success' => true, 'data' => ['user' => $user, 'valid' => true]]);
                } else {
                    echo json_encode(['success' => true, 'data' => ['valid' => false]]);
                }
            } else {
                echo json_encode(['success' => true, 'data' => ['valid' => false]]);
            }
            break;
            
        case 'verify-email':
            $token = $_GET['token'] ?? '';
            $result = $authService->verifyEmail($token);
            
            if ($result['success']) {
                // Redirect to success page
                header('Location: /email-verified?success=true');
            } else {
                header('Location: /email-verified?error=' . urlencode($result['error']));
            }
            exit;
            
        default:
            // Default GET request returns current user (backward compatibility)
            $user = AuthHelper::getCurrentUser();
            if ($user) {
                echo json_encode(['success' => true, 'data' => ['user' => $user]]);
            } else {
                http_response_code(401);
                echo json_encode(['error' => 'Not authenticated']);
            }
            break;
    }
}

/**
 * Handle PUT requests
 */
function handlePutRequest($authService, $action, $input) {
    $user = AuthHelper::requireAuth();
    
    switch ($action) {
        case 'profile':
            $result = updateUserProfile($user['id'], $input);
            break;
            
        case 'password':
            $currentPassword = $input['current_password'] ?? '';
            $newPassword = $input['new_password'] ?? '';
            $result = changePassword($user['id'], $currentPassword, $newPassword);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            return;
    }
    
    if ($result['success']) {
        echo json_encode($result);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
}

/**
 * Handle DELETE requests
 */
function handleDeleteRequest($authService, $action) {
    $user = AuthHelper::requireAuth();
    
    switch ($action) {
        case 'account':
            $result = deleteUserAccount($user['id']);
            if ($result['success']) {
                session_destroy();
            }
            break;
            
        case 'session':
            $sessionId = $_GET['session_id'] ?? $_SESSION['session_id'] ?? '';
            $result = $authService->logout($sessionId);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            return;
    }
    
    if ($result['success']) {
        echo json_encode($result);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
}

/**
 * Handle token refresh
 */
function handleTokenRefresh($authService) {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (!preg_match('/Bearer\s+(\S+)/', $authHeader, $matches)) {
        return ['success' => false, 'error' => 'No token provided'];
    }
    
    $token = $matches[1];
    $jwtHelper = new JWTHelper();
    $newToken = $jwtHelper->refreshToken($token);
    
    if ($newToken) {
        return ['success' => true, 'data' => ['token' => $newToken]];
    } else {
        return ['success' => false, 'error' => 'Token refresh failed'];
    }
}

/**
 * Update user profile
 */
function updateUserProfile($userId, $data) {
    try {
        $db = Database::getInstance()->getConnection();
        
        $allowedFields = ['first_name', 'last_name', 'bio', 'avatar_url', 'profile_visibility', 'allow_messages'];
        $updateFields = [];
        $params = [];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateFields[] = "$field = ?";
                $params[] = $data[$field];
            }
        }
        
        if (empty($updateFields)) {
            return ['success' => false, 'error' => 'No valid fields to update'];
        }
        
        $params[] = $userId;
        $sql = "UPDATE users SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = ?";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        
        return ['success' => true, 'message' => 'Profile updated successfully'];
        
    } catch (Exception $e) {
        error_log('Profile update error: ' . $e->getMessage());
        return ['success' => false, 'error' => 'Profile update failed'];
    }
}

/**
 * Change user password
 */
function changePassword($userId, $currentPassword, $newPassword) {
    try {
        $db = Database::getInstance()->getConnection();
        
        // Get current password hash
        $stmt = $db->prepare("SELECT password_hash FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if (!$user || !password_verify($currentPassword, $user['password_hash'])) {
            return ['success' => false, 'error' => 'Current password is incorrect'];
        }
        
        // Validate new password
        if (strlen($newPassword) < 8) {
            return ['success' => false, 'error' => 'New password must be at least 8 characters long'];
        }
        
        $newPasswordHash = password_hash($newPassword, PASSWORD_ARGON2ID);
        
        $stmt = $db->prepare("UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$newPasswordHash, $userId]);
        
        return ['success' => true, 'message' => 'Password changed successfully'];
        
    } catch (Exception $e) {
        error_log('Password change error: ' . $e->getMessage());
        return ['success' => false, 'error' => 'Password change failed'];
    }
}

/**
 * Delete user account
 */
function deleteUserAccount($userId) {
    try {
        $db = Database::getInstance()->getConnection();
        
        $db->beginTransaction();
        
        // Soft delete - mark as deleted instead of actually deleting
        $stmt = $db->prepare("UPDATE users SET is_active = FALSE, deleted_at = NOW() WHERE id = ?");
        $stmt->execute([$userId]);
        
        // Invalidate all sessions
        $stmt = $db->prepare("UPDATE user_sessions SET is_active = FALSE WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        $db->commit();
        
        return ['success' => true, 'message' => 'Account deleted successfully'];
        
    } catch (Exception $e) {
        $db->rollBack();
        error_log('Account deletion error: ' . $e->getMessage());
        return ['success' => false, 'error' => 'Account deletion failed'];
    }
}