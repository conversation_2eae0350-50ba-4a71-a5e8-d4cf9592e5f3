const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/HomePage-C4sUsitP.js","assets/react-core-B9nwsbCA.js","assets/router-BDggJ1ol.js","assets/VideoGrid-_iQm6QDa.js","assets/ui-components-Ct00E1u3.js","assets/utils-D7twGpBa.js","assets/state-4gBW_4Nx.js","assets/icons-BWE0bDFO.js","assets/useVideos-63LnTHn1.js","assets/VideoPage-kJpKkFka.js","assets/NativeVideoPlayer-DjQWyfFt.js","assets/CategoryPage-xTbsAtSJ.js","assets/AllVideosPage-CF7d77OI.js","assets/UploadPage-N8FDo36c.js","assets/ManageVideosPage-CNw7anmk.js","assets/SearchPage-WmhySmoL.js","assets/FavoritesPage-Dkj8XoPT.js","assets/TestVideoPage-Brls4pMW.js","assets/TestVideoIndexPage-CVOUSkL9.js","assets/AdminPage-CPypz21G.js"])))=>i.map(i=>d[i]);
import{r as e,a as s,j as t,c as r}from"./react-core-B9nwsbCA.js";import{i as a,j as n,u as i,k as l,l as o,m as c,n as d,o as x,p as u,q as m,s as h,S as g}from"./utils-D7twGpBa.js";import{u as p,B as f,R as j,a as b}from"./router-BDggJ1ol.js";import{B as y,I as v}from"./ui-components-Ct00E1u3.js";import{c as w}from"./state-4gBW_4Nx.js";import{S as N,X as C,T as E,e as S,B as k,U as L,f as _,g as z,M as A,H as I,V as T,h as P,i as O,F as R,j as D,k as F,l as V,m as U}from"./icons-BWE0bDFO.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))s(e);new MutationObserver((e=>{for(const t of e)if("childList"===t.type)for(const e of t.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&s(e)})).observe(document,{childList:!0,subtree:!0})}function s(e){if(e.ep)return;e.ep=!0;const s=function(e){const s={};return e.integrity&&(s.integrity=e.integrity),e.referrerPolicy&&(s.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?s.credentials="include":"anonymous"===e.crossOrigin?s.credentials="omit":s.credentials="same-origin",s}(e);fetch(e.href,s)}}();const M={},B=function(e,s,t){let r=Promise.resolve();if(s&&s.length>0){document.getElementsByTagName("link");const e=document.querySelector("meta[property=csp-nonce]"),t=e?.nonce||e?.getAttribute("nonce");r=Promise.allSettled(s.map((e=>{if((e=function(e){return"/"+e}(e))in M)return;M[e]=!0;const s=e.endsWith(".css"),r=s?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${r}`))return;const a=document.createElement("link");return a.rel=s?"stylesheet":"modulepreload",s||(a.as="script"),a.crossOrigin="",a.href=e,t&&a.setAttribute("nonce",t),document.head.appendChild(a),s?new Promise(((s,t)=>{a.addEventListener("load",s),a.addEventListener("error",(()=>t(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function a(e){const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=e,window.dispatchEvent(s),!s.defaultPrevented)throw e}return r.then((s=>{for(const e of s||[])"rejected"===e.status&&a(e.reason);return e().catch(a)}))},$=w(((e,s)=>({searchQuery:"",searchResults:[],recentSearches:JSON.parse(localStorage.getItem("recentSearches")||"[]"),isLoading:!1,error:null,setSearchQuery:s=>{e({searchQuery:s})},search:async t=>{const r=t||s().searchQuery;if(r.trim()){e({isLoading:!0,error:null});try{const{data:i,error:l}=await getVideoQuery(supabase.from("videos")).or(`title.ilike.%${r}%,description.ilike.%${r}%`).order("views",{ascending:!1}).limit(50);if(l)throw new Error(l.message);const o=i.map((e=>({id:e.id,title:e.title,description:e.description||"",thumbnailUrl:a(e.thumbnail_url||""),videoUrl:n(e.video_url||""),duration:e.duration||0,views:e.views||0,likes:e.likes||0,createdAt:e.created_at,updatedAt:e.updated_at||e.created_at,publishedAt:e.created_at,scheduledFor:void 0,status:"public",isHD:e.is_hd||!1,isPremium:!1,tags:Array.isArray(e.tags)?e.tags:[],category:e.category||"uncategorized",creator:{id:e.creator?.id||"",email:"",avatar:e.creator?.avatar_url||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0}})));e({searchResults:o,isLoading:!1}),r.trim()&&t&&s().addRecentSearch(r)}catch(i){e({error:i instanceof Error?i.message:"An unknown error occurred",isLoading:!1})}}else e({searchResults:[],error:null})},clearSearch:()=>{e({searchQuery:"",searchResults:[]})},addRecentSearch:t=>{const r=t.trim();if(!r)return;const a=s().recentSearches.filter((e=>e.toLowerCase()!==r.toLowerCase())),n=[r,...a].slice(0,5);e({recentSearches:n}),localStorage.setItem("recentSearches",JSON.stringify(n))},clearRecentSearches:()=>{e({recentSearches:[]}),localStorage.removeItem("recentSearches")}}))),q=({className:r="",onClose:a})=>{const n=p(),{searchQuery:i,recentSearches:l,setSearchQuery:o,search:c,clearSearch:d,addRecentSearch:x,clearRecentSearches:u}=$(),[m,h]=e.useState(!1),g=e.useRef(null),f=e.useRef(null);s.useEffect((()=>{const e=e=>{f.current&&!f.current.contains(e.target)&&h(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[]);return t.jsx("div",{ref:f,className:`relative ${r}`,children:t.jsxs("form",{onSubmit:e=>{e.preventDefault(),i.trim()&&(x(i),h(!1),c(i),n(`/search?q=${encodeURIComponent(i)}`),a&&a())},className:"relative",children:[t.jsxs("div",{className:"relative flex items-center",children:[t.jsx("input",{ref:g,type:"text",placeholder:"Search videos",value:i,onChange:e=>{o(e.target.value),h(""===e.target.value)},onFocus:()=>{h(""===i)},className:"w-full bg-gray-800 text-white rounded-full pl-10 pr-10 py-2.5 focus:outline-none focus:ring-1 focus:ring-blue-700 border border-gray-700 hover:border-blue-700 transition-colors shadow-sm text-sm md:text-base mobile-search-input"}),t.jsx(N,{className:"absolute left-3 text-blue-500 search-icon",size:16}),i&&t.jsx("button",{type:"button",onClick:()=>{d(),g.current&&g.current.focus(),h(!0)},className:"absolute right-8 text-gray-400 hover:text-white p-1 search-button-mobile",children:t.jsx(C,{size:16})}),t.jsx("button",{type:"submit",className:"absolute right-2 text-blue-700 hover:text-blue-600 p-1 search-button-mobile",disabled:!i.trim(),children:t.jsx(N,{size:16})})]}),m&&l.length>0&&t.jsxs("div",{className:"absolute top-full left-0 right-0 mt-1 bg-gray-800 rounded-md shadow-lg z-50 overflow-hidden max-h-60 overflow-y-auto mobile-search-dropdown",children:[t.jsxs("div",{className:"flex items-center justify-between p-2 border-b border-gray-700",children:[t.jsx("h3",{className:"text-white font-medium text-sm",children:"Recent Searches"}),t.jsxs("button",{onClick:u,className:"text-gray-400 hover:text-white flex items-center text-xs",children:[t.jsx(E,{size:12,className:"mr-1"}),"Clear"]})]}),t.jsx("ul",{children:l.map(((e,s)=>t.jsx("li",{children:t.jsxs("button",{onClick:()=>(e=>{o(e),h(!1),c(e),n(`/search?q=${encodeURIComponent(e)}`),a&&a()})(e),className:"w-full text-left px-3 py-2 hover:bg-gray-700 flex items-center text-gray-300 hover:text-white text-sm truncate",children:[t.jsx(S,{size:14,className:"mr-2 text-gray-500 flex-shrink-0"}),t.jsx("span",{className:"truncate",children:e})]})},s)))})]})]})})},H=({onOpenSidebar:s,isAuthenticated:r=!1,onLoginClick:a,onSignUpClick:n})=>{const c=p(),{user:d,signOut:x}=i(),[u,m]=e.useState(!1),[h,g]=e.useState(!1),f=e.useRef(null),[j,b]=e.useState(!1),v=e.useRef(null),w=l(d?d.id:"guest");e.useEffect((()=>{const e=e=>{v.current&&!v.current.contains(e.target)&&b(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)}),[]);const N=async()=>{try{b(!1),await x(),c("/")}catch(e){b(!1),c("/")}};return e.useEffect((()=>{const e=()=>{m(window.scrollY>10)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)}),[]),e.useEffect((()=>{if(h&&f.current){const e=f.current.querySelector("input");e&&setTimeout((()=>{e.focus()}),100)}}),[h]),t.jsx("header",{className:`\n        fixed top-0 left-0 right-0 z-40 transition-all duration-300\n        ${u?"bg-gray-900/95 backdrop-blur-sm shadow-md":"bg-gradient-to-b from-gray-900 to-transparent"}\n      `,children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"hidden md:flex items-center justify-between h-16",children:[t.jsx("div",{className:"flex-shrink-0 flex items-center lg:ml-0",children:t.jsxs("button",{className:"font-bold text-xl text-white hover:opacity-80 transition-opacity",onClick:()=>c("/",{replace:!0}),children:[t.jsx("span",{className:"text-blue-500",children:"Blue"}),"FilmX"]})}),t.jsx("div",{className:"flex-1 max-w-xl mx-6",children:t.jsxs("div",{className:"relative search-bar-container",children:[t.jsx("div",{className:"absolute -top-6 left-4 text-blue-500 font-medium text-sm",children:"Search Videos"}),t.jsx(q,{className:"w-full"})]})}),t.jsx("div",{className:"flex items-center space-x-3",children:r?t.jsxs(t.Fragment,{children:[t.jsx("button",{className:"p-2 rounded-full hover:bg-gray-800 text-gray-300 hover:text-white",children:t.jsx(k,{size:22})}),t.jsx("button",{className:"p-2 rounded-full hover:bg-gray-800 text-gray-300 hover:text-white",onClick:()=>c("/upload"),title:"Upload Video",children:t.jsx(L,{size:22})}),t.jsxs("div",{className:"relative ml-2",ref:v,children:[t.jsx("button",{className:"h-9 w-9 rounded-full border-2 border-blue-700 overflow-hidden focus:outline-none focus:ring-2 focus:ring-blue-700 focus:ring-offset-1 focus:ring-offset-gray-900 bg-blue-100",onClick:()=>b(!j),children:t.jsx("img",{src:w,alt:"Profile",className:"h-full w-full object-contain",onError:e=>{e.currentTarget.src=o()}})}),j&&t.jsxs("div",{className:"absolute right-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg py-1 z-60 border border-gray-700",children:[t.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors",onClick:()=>c("/manage"),children:"My Videos"}),t.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors",onClick:()=>c("/favorites"),children:"Favorites"}),t.jsx("div",{className:"border-t border-gray-700 my-1"}),t.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-700 hover:text-red-300 transition-colors flex items-center space-x-2",onClick:N,children:[t.jsx(_,{size:16}),t.jsx("span",{children:"Log Out"})]})]})]})]}):t.jsxs(t.Fragment,{children:[t.jsx(y,{variant:"ghost",size:"sm",className:"hidden md:inline-flex",onClick:a,children:"Log in"}),t.jsx(y,{variant:"primary",size:"sm",leftIcon:t.jsx(z,{size:16}),onClick:n,children:"Sign Up"})]})})]}),t.jsx("div",{className:"md:hidden",children:t.jsxs("div",{className:"flex items-center justify-between h-16",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsxs("button",{onClick:s,className:"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-700","aria-expanded":"false",children:[t.jsx("span",{className:"sr-only",children:"Open main menu"}),t.jsx(A,{className:"block h-6 w-6","aria-hidden":"true"})]}),t.jsx("div",{className:"flex-shrink-0 flex items-center ml-2",children:t.jsxs("button",{className:"font-bold text-lg text-white hover:opacity-80 transition-opacity",onClick:()=>c("/",{replace:!0}),children:[t.jsx("span",{className:"text-blue-500",children:"Blue"}),"FilmX"]})})]}),t.jsx("div",{className:"flex items-center",children:r?t.jsxs(t.Fragment,{children:[t.jsx("button",{className:"p-1.5 rounded-full hover:bg-gray-800 text-gray-300 hover:text-white",onClick:()=>c("/upload"),title:"Upload Video",children:t.jsx(L,{size:18})}),t.jsxs("div",{className:"relative ml-1",ref:v,children:[t.jsx("button",{className:"h-7 w-7 rounded-full border-2 border-blue-700 overflow-hidden focus:outline-none focus:ring-2 focus:ring-blue-700 focus:ring-offset-1 focus:ring-offset-gray-900 bg-blue-100",onClick:()=>b(!j),children:t.jsx("img",{src:w,alt:"Profile",className:"h-full w-full object-contain",onError:e=>{e.currentTarget.src=o()}})}),j&&t.jsxs("div",{className:"absolute right-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg py-1 z-60 border border-gray-700",children:[t.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors",onClick:()=>c("/manage"),children:"My Videos"}),t.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors",onClick:()=>c("/favorites"),children:"Favorites"}),t.jsx("div",{className:"border-t border-gray-700 my-1"}),t.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-700 hover:text-red-300 transition-colors flex items-center space-x-2",onClick:N,children:[t.jsx(_,{size:16}),t.jsx("span",{children:"Log Out"})]})]})]})]}):t.jsx(y,{variant:"primary",size:"xs",leftIcon:t.jsx(z,{size:14}),onClick:n,children:"Sign Up"})})]})})]})})},J=w(((e,s)=>({categories:[],featuredCategories:[],topCategories:[],isLoading:!1,error:null,fetchCategories:async()=>{e({isLoading:!0,error:null});try{const{data:s,error:t}=await supabase.from("categories").select("*").order("name");if(t)throw new Error(t.message);const r=s.map((e=>({id:e.id,name:e.name,slug:e.slug})));e({categories:r,isLoading:!1})}catch(s){e({error:s instanceof Error?s.message:"An unknown error occurred",isLoading:!1})}},fetchFeaturedCategories:async()=>{e({isLoading:!0,error:null});try{const{data:s,error:t}=await supabase.from("videos").select("category").not("category","is",null);if(t)throw new Error(t.message);const r={};s.forEach((e=>{e.category&&(r[e.category]=(r[e.category]||0)+1)}));const a=Object.entries(r).sort((([,e],[,s])=>s-e)).slice(0,6).map((([e])=>e)),{data:n,error:i}=await supabase.from("categories").select("*").in("slug",a);if(i)throw new Error(i.message);const l=n.map((e=>({id:e.id,name:e.name,slug:e.slug})));e({featuredCategories:l,isLoading:!1})}catch(s){e({error:s instanceof Error?s.message:"An unknown error occurred",isLoading:!1})}},fetchTopCategories:async(s=6)=>{e({isLoading:!0,error:null});try{const t=["hot","trending","new"],{data:r,error:a}=await supabase.from("videos").select("category").not("category","is",null);if(a)throw new Error(a.message);const n={};r.forEach((e=>{e.category&&(n[e.category]=(n[e.category]||0)+1)}));let i=Object.entries(n).map((([e,s])=>({id:e,name:Q(e),slug:e.toLowerCase().replace(/\s+/g,"-"),count:s,isSpecial:t.includes(e.toLowerCase())})));i.sort(((e,s)=>s.count-e.count));const l=[],o=[];i.forEach((e=>{e.isSpecial?l.push(e):o.push(e)})),l.sort(((e,s)=>t.indexOf(e.id.toLowerCase())-t.indexOf(s.id.toLowerCase()))),t.forEach((e=>{l.some((s=>s.id.toLowerCase()===e))||l.push({id:e,name:Q(e),slug:e,count:0,isSpecial:!0})}));const c=[...l,...o].slice(0,s);e({topCategories:c,isLoading:!1})}catch(t){e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},getCategoryBySlug:e=>{const t=s().categories.find((s=>s.slug===e));if(t)return t;if(["hot","trending","new"].includes(e.toLowerCase()))return{id:e,name:Q(e),slug:e.toLowerCase()};return s().topCategories.find((s=>s.slug===e))}})));function Q(e){return e.split(/[_\s]/).map((e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase())).join(" ")}const W=({icon:e,label:s,isActive:r=!1,onClick:a})=>t.jsxs("button",{className:`\n        w-full flex items-center px-4 py-2.5 text-sm rounded-lg\n        ${r?"bg-blue-700/20 text-blue-700":"text-gray-300 hover:bg-gray-800 hover:text-white"}\n        transition-colors duration-200\n      `,onClick:a,children:[t.jsx("span",{className:"flex-shrink-0 mr-3",children:e}),t.jsx("span",{children:s})]}),Y=e=>{const s=e.toLowerCase();return"hot"===s?t.jsx(R,{size:16,className:"mr-3 text-blue-700"}):"trending"===s?t.jsx(D,{size:16,className:"mr-3 text-blue-500"}):"new"===s?t.jsx(F,{size:16,className:"mr-3 text-yellow-500"}):t.jsx(V,{size:16,className:"mr-3 text-gray-400"})},G=({isOpen:s,onClose:r})=>{const a=p(),{user:n}=i(),{topCategories:l,fetchTopCategories:o,isLoading:c,error:d}=J(),[x,u]=e.useState(!1);return e.useEffect((()=>{(async()=>{if(n)try{const{data:e,error:s}=await supabase.from("user_roles").select("role").eq("user_id",n.id).single();u(!s&&"admin"===e?.role)}catch(e){u(!1)}else u(!1)})()}),[n]),e.useEffect((()=>{o(6)}),[o]),t.jsxs(t.Fragment,{children:[s&&t.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden",onClick:r}),t.jsxs("aside",{className:`\n          fixed top-0 left-0 h-full w-64 bg-gray-900 border-r border-gray-800 z-40\n          transform transition-transform duration-300 ease-in-out\n          ${s?"translate-x-0":"-translate-x-full lg:translate-x-0"}\n        `,children:[t.jsxs("div",{className:"px-4 h-16 flex items-center justify-between",children:[t.jsxs("div",{className:"font-bold text-xl text-white",children:[t.jsx("span",{className:"text-blue-500",children:"Blue"}),"Film"]}),t.jsxs("button",{className:"p-1 rounded-full text-gray-400 hover:text-white hover:bg-gray-800 lg:hidden",onClick:r,children:[t.jsx("span",{className:"sr-only",children:"Close sidebar"}),t.jsx(C,{size:20})]})]}),t.jsxs("nav",{className:"mt-2 px-2",children:[t.jsxs("div",{className:"space-y-1",children:[t.jsx(W,{icon:t.jsx(I,{size:18}),label:"Home",isActive:!0,onClick:()=>a("/",{replace:!0})}),n&&t.jsxs(t.Fragment,{children:[t.jsx(W,{icon:t.jsx(L,{size:18}),label:"Upload",onClick:()=>a("/upload")}),t.jsx(W,{icon:t.jsx(T,{size:18}),label:"My Videos",onClick:()=>a("/manage")}),x&&t.jsx(W,{icon:t.jsx(P,{size:18}),label:"Admin",onClick:()=>a("/admin")})]}),t.jsx(W,{icon:t.jsx(O,{size:18}),label:"Favorites",onClick:()=>a("/favorites")})]}),t.jsxs("div",{className:"mt-6",children:[t.jsx("h3",{className:"px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider",children:"Top Categories"}),t.jsxs("div",{className:"mt-2 space-y-1",children:[t.jsx("div",{className:"flex items-center justify-between px-4 py-2 text-sm text-blue-700 hover:bg-gray-800 hover:text-white rounded-lg transition-colors duration-200 cursor-pointer font-medium",onClick:()=>a("/category/hot"),children:t.jsxs("div",{className:"flex items-center",children:[t.jsx(R,{size:16,className:"mr-3 text-blue-700"}),t.jsx("span",{children:"Hot"})]})}),t.jsx("div",{className:"flex items-center justify-between px-4 py-2 text-sm text-blue-400 hover:bg-gray-800 hover:text-white rounded-lg transition-colors duration-200 cursor-pointer font-medium",onClick:()=>a("/category/trending"),children:t.jsxs("div",{className:"flex items-center",children:[t.jsx(D,{size:16,className:"mr-3 text-blue-500"}),t.jsx("span",{children:"Trending"})]})}),t.jsx("div",{className:"flex items-center justify-between px-4 py-2 text-sm text-yellow-400 hover:bg-gray-800 hover:text-white rounded-lg transition-colors duration-200 cursor-pointer font-medium",onClick:()=>a("/category/new"),children:t.jsxs("div",{className:"flex items-center",children:[t.jsx(F,{size:16,className:"mr-3 text-yellow-500"}),t.jsx("span",{children:"New"})]})})]})]}),!c&&!d&&l.filter((e=>!["hot","trending","new"].includes(e.id.toLowerCase()))).length>0&&t.jsxs("div",{className:"mt-4",children:[t.jsx("h3",{className:"px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider",children:"Categories"}),t.jsx("div",{className:"mt-2 space-y-1",children:c?t.jsx("div",{className:"px-4 py-2 text-sm text-gray-500",children:"Loading categories..."}):d?t.jsx("div",{className:"px-4 py-2 text-sm text-red-400",children:"Error loading categories"}):l.filter((e=>!["hot","trending","new"].includes(e.id.toLowerCase()))).map((e=>t.jsxs("div",{className:"flex items-center justify-between px-4 py-2 text-sm text-gray-300 hover:bg-gray-800 hover:text-white rounded-lg transition-colors duration-200 cursor-pointer",onClick:()=>a(`/category/${e.slug}`),children:[t.jsxs("div",{className:"flex items-center",children:[Y(e.name),t.jsx("span",{children:e.name})]}),t.jsx("span",{className:"text-gray-500 text-xs",children:e.count||0})]},e.id)))})]})]}),t.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-4",children:t.jsx("div",{className:"border-t border-gray-800 pt-4",children:t.jsx("div",{className:"text-xs text-gray-500",children:"© 2025 BlueFilm. All rights reserved."})})})]})]})},X=({activeTab:e="home",onTabChange:s})=>{const r=p(),{user:a}=i(),n=e=>{s&&s(e),"home"===e?r("/",{replace:!0}):"search"===e?r("/search"):"upload"===e?r("/upload"):"profile"===e&&a?r("/manage"):"profile"===e&&r("/search")};return t.jsx("div",{className:"fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-800 lg:hidden z-40",children:t.jsxs("nav",{className:"flex justify-around",children:[t.jsxs("button",{onClick:()=>n("home"),className:"flex flex-col items-center justify-center py-2 flex-1 transition-colors\n            "+("home"===e?"text-blue-700":"text-gray-400 hover:text-white"),children:[t.jsx(I,{size:20}),t.jsx("span",{className:"text-xs mt-1",children:"Home"})]}),t.jsxs("button",{onClick:()=>n("search"),className:"flex flex-col items-center justify-center py-2 flex-1 transition-colors\n            "+("search"===e?"text-blue-700":"text-gray-400 hover:text-white"),children:[t.jsx(N,{size:20}),t.jsx("span",{className:"text-xs mt-1",children:"Search"})]}),a&&t.jsxs("button",{onClick:()=>n("upload"),className:"flex flex-col items-center justify-center py-2 flex-1 transition-colors\n              "+("upload"===e?"text-blue-700":"text-gray-400 hover:text-white"),children:[t.jsx(L,{size:20}),t.jsx("span",{className:"text-xs mt-1",children:"Upload"})]}),t.jsx("button",{onClick:()=>n("profile"),className:"flex flex-col items-center justify-center py-2 flex-1 transition-colors\n            "+("profile"===e?"text-blue-700":"text-gray-400 hover:text-white"),children:a?t.jsxs(t.Fragment,{children:[t.jsx(T,{size:20}),t.jsx("span",{className:"text-xs mt-1",children:"My Videos"})]}):t.jsxs(t.Fragment,{children:[t.jsx(U,{size:20}),t.jsx("span",{className:"text-xs mt-1",children:"Profile"})]})})]})})},K=({isOpen:s,onClose:r,initialMode:a="login"})=>{const[n,l]=e.useState(a),[o,c]=e.useState(""),[d,x]=e.useState(""),[u,m]=e.useState(null),[h,g]=e.useState(!1),{signIn:p,signUp:f}=i();e.useEffect((()=>{const e=e=>{"Escape"===e.key&&s&&r()};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)}),[s,r]);return s?t.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:r,children:t.jsxs("div",{className:"bg-gray-900 rounded-lg max-w-md w-full p-6",onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"flex justify-between items-center mb-6",children:[t.jsx("h2",{className:"text-2xl font-bold",children:"login"===n?"Sign In":"Create Account"}),t.jsx("button",{onClick:r,className:"text-gray-400 hover:text-white p-1 rounded-full hover:bg-gray-800",children:t.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[t.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),t.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),t.jsxs("form",{onSubmit:async e=>{e.preventDefault(),m(null),g(!0);try{"login"===n?await p(o,d):await f(o,d),r()}catch(s){m(s instanceof Error?s.message:"An error occurred")}finally{g(!1)}},className:"space-y-4",children:[t.jsx(v,{label:"Email",type:"email",value:o,onChange:e=>c(e.target.value),required:!0,fullWidth:!0}),t.jsx(v,{label:"Password",type:"password",value:d,onChange:e=>x(e.target.value),required:!0,fullWidth:!0}),u&&t.jsx("p",{className:"text-red-500 text-sm",children:u}),t.jsxs("div",{className:"space-y-3",children:[t.jsx(y,{type:"submit",variant:"primary",fullWidth:!0,isLoading:h,children:"login"===n?"Sign In":"Create Account"}),t.jsx(y,{type:"button",variant:"ghost",fullWidth:!0,onClick:r,disabled:h,children:"Cancel"})]}),t.jsx("p",{className:"text-center text-gray-400 text-sm",children:"login"===n?t.jsxs(t.Fragment,{children:["Don't have an account?"," ",t.jsx("button",{type:"button",className:"text-orange-500 hover:text-orange-400",onClick:()=>l("signup"),children:"Sign Up"})]}):t.jsxs(t.Fragment,{children:["Already have an account?"," ",t.jsx("button",{type:"button",className:"text-orange-500 hover:text-orange-400",onClick:()=>l("login"),children:"Sign In"})]})})]})]})}):null},Z=({children:s,requireAuth:r=!0,requireApproval:a=!1,redirectTo:n="/"})=>{const l=p(),{user:o,isApproved:c,isLoading:d}=i();return e.useEffect((()=>{d||(!r||o?a&&o&&!c&&l(n):l(n))}),[o,c,d,r,a,n,l]),d?t.jsx("div",{className:"min-h-screen bg-gray-900 flex items-center justify-center",children:t.jsx("div",{className:"text-white",children:"Loading..."})}):r&&!o||a&&o&&!c?null:t.jsx(t.Fragment,{children:s})},ee=({isOpen:s,onAccept:r})=>{p();const[a,n]=e.useState(s);e.useEffect((()=>(n(s),document.body.style.overflow=s?"hidden":"auto",()=>{document.body.style.overflow="auto"})),[s]);return a?t.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4",children:t.jsx("div",{className:"bg-gray-900 rounded-lg max-w-md w-full border border-gray-700 shadow-2xl",children:t.jsxs("div",{className:"p-6",children:[t.jsx("h2",{className:"text-2xl font-bold text-white mb-4 text-center",children:"Adult Content Warning"}),t.jsx("div",{className:"h-px bg-gray-700 mb-4"}),t.jsx("p",{className:"text-gray-200 mb-6 text-center",children:"This site contains explicit adult material. You may only proceed if you are 18 years of age or older (or the legal age of majority in your jurisdiction). If you do not meet these requirements, you are not permitted to access this site."}),t.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[t.jsx("button",{onClick:()=>{n(!1),r()},className:"px-6 py-2 bg-blue-700 hover:bg-blue-600 text-white rounded-md font-medium transition-colors",children:"Enter"}),t.jsx("button",{onClick:()=>{window.location.href="https://www.google.com"},className:"px-6 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-md font-medium transition-colors",children:"Leave"})]})]})})}):null},se=w((e=>({hasAcceptedDisclaimer:!1,acceptDisclaimer:()=>{const s={accepted:!0,timestamp:(new Date).getTime()};localStorage.setItem("disclaimerAccepted",JSON.stringify(s)),e({hasAcceptedDisclaimer:!0})},checkDisclaimerStatus:()=>{try{const s=localStorage.getItem("disclaimerAccepted");if(!s)return void e({hasAcceptedDisclaimer:!1});const t=JSON.parse(s);if("boolean"==typeof t||"true"===t)return void e({hasAcceptedDisclaimer:!0});t&&t.accepted?e({hasAcceptedDisclaimer:!0}):e({hasAcceptedDisclaimer:!1})}catch(s){e({hasAcceptedDisclaimer:!1})}}}))),te=e.memo((({visible:s=!1,position:r="bottom-right"})=>{const[a,n]=e.useState({fcp:null,lcp:null,cls:null,fid:null,ttfb:null,jsHeapSize:null,domNodes:null,resourceCount:null,resourceSize:null}),[i,l]=e.useState(!1),o=e=>{if(null===e)return"N/A";if(0===e)return"0 Bytes";const s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]},c=e=>null===e?"N/A":`${e.toFixed(2)} ms`,d=e.useCallback((()=>{if(!window.performance||!window.performance.getEntriesByType)return;const e=performance.getEntriesByType("paint").find((e=>"first-contentful-paint"===e.name));e&&n((s=>({...s,fcp:e.startTime})))}),[]),x=e.useCallback((()=>{if(window.PerformanceObserver)try{const e=new PerformanceObserver((e=>{const s=e.getEntries(),t=s[s.length-1];t&&n((e=>({...e,lcp:t.startTime})))}));return e.observe({type:"largest-contentful-paint",buffered:!0}),()=>{e.disconnect()}}catch(e){}}),[]),u=e.useCallback((()=>{if(window.PerformanceObserver)try{let e=0;const s=new PerformanceObserver((s=>{for(const t of s.getEntries())t.hadRecentInput||(e+=t.value,n((s=>({...s,cls:e}))))}));return s.observe({type:"layout-shift",buffered:!0}),()=>{s.disconnect()}}catch(e){}}),[]),m=e.useCallback((()=>{if(window.PerformanceObserver)try{const e=new PerformanceObserver((e=>{const s=e.getEntries()[0];if(s){const e=s.processingStart-s.startTime;n((s=>({...s,fid:e})))}}));return e.observe({type:"first-input",buffered:!0}),()=>{e.disconnect()}}catch(e){}}),[]),h=e.useCallback((()=>{if(!window.performance||!window.performance.getEntriesByType)return;const e=performance.getEntriesByType("navigation")[0];e&&n((s=>({...s,ttfb:e.responseStart})))}),[]),g=e.useCallback((()=>{performance.memory&&n((e=>({...e,jsHeapSize:performance.memory.usedJSHeapSize})))}),[]),p=e.useCallback((()=>{const e=document.querySelectorAll("*").length;n((s=>({...s,domNodes:e})))}),[]),f=e.useCallback((()=>{if(!window.performance||!window.performance.getEntriesByType)return;const e=performance.getEntriesByType("resource");let s=0;e.forEach((e=>{e.transferSize&&(s+=e.transferSize)})),n((t=>({...t,resourceCount:e.length,resourceSize:s})))}),[]);return e.useEffect((()=>{if(!s)return;d(),h(),p();const e=x(),t=u(),r=m(),a=setInterval((()=>{g(),p(),f()}),2e3);return()=>{e&&e(),t&&t(),r&&r(),clearInterval(a)}}),[s,d,x,u,m,h,g,p,f]),s?t.jsxs("div",{className:`fixed ${(()=>{switch(r){case"top-right":return"top-4 right-4";case"top-left":return"top-4 left-4";case"bottom-left":return"bottom-4 left-4";default:return"bottom-4 right-4"}})()} z-50 bg-gray-900 border border-blue-700 rounded-lg shadow-lg overflow-hidden transition-all duration-300 ${i?"w-80":"w-10"}`,children:[t.jsx("button",{className:"absolute top-2 right-2 text-blue-500 hover:text-blue-400 focus:outline-none",onClick:()=>l(!i),children:i?"×":"≡"}),i&&t.jsxs("div",{className:"p-4",children:[t.jsx("h3",{className:"text-white text-sm font-bold mb-2",children:"Performance Metrics"}),t.jsxs("div",{className:"space-y-2 text-xs",children:[t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"FCP:"}),t.jsx("span",{className:"font-mono "+(a.fcp&&a.fcp<1e3?"text-green-400":"text-yellow-400"),children:c(a.fcp)})]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"LCP:"}),t.jsx("span",{className:"font-mono "+(a.lcp&&a.lcp<2500?"text-green-400":"text-yellow-400"),children:c(a.lcp)})]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"CLS:"}),t.jsx("span",{className:"font-mono "+(a.cls&&a.cls<.1?"text-green-400":"text-yellow-400"),children:null!==a.cls?a.cls.toFixed(3):"N/A"})]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"FID:"}),t.jsx("span",{className:"font-mono "+(a.fid&&a.fid<100?"text-green-400":"text-yellow-400"),children:c(a.fid)})]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"TTFB:"}),t.jsx("span",{className:"font-mono "+(a.ttfb&&a.ttfb<200?"text-green-400":"text-yellow-400"),children:c(a.ttfb)})]}),t.jsx("div",{className:"border-t border-gray-700 my-2"}),t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"JS Heap:"}),t.jsx("span",{className:"font-mono text-blue-400",children:o(a.jsHeapSize)})]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"DOM Nodes:"}),t.jsx("span",{className:"font-mono "+(a.domNodes&&a.domNodes<1500?"text-green-400":"text-yellow-400"),children:null!==a.domNodes?a.domNodes:"N/A"})]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Resources:"}),t.jsx("span",{className:"font-mono text-blue-400",children:null!==a.resourceCount?`${a.resourceCount} (${o(a.resourceSize)})`:"N/A"})]})]})]})]}):null})),re=({visible:s=!1,onMetricsUpdate:r})=>{const[a,n]=e.useState({imagesLoaded:0,imagesError:0,averageLoadTime:0,totalDataTransferred:0,cacheHitRate:0,slowLoadingImages:0}),[i,l]=e.useState([]),[o,d]=e.useState(null),x=e.useCallback((()=>{if(!window.PerformanceObserver)return;const e=new PerformanceObserver((e=>{e.getEntries().forEach((e=>{const s=e;if("img"===s.initiatorType||s.name.match(/\.(jpg|jpeg|png|gif|webp|avif|svg)(\?|$)/i)){const e=s.responseEnd-s.requestStart,t=s.transferSize||0;l((s=>[...s,e].slice(-100))),n((s=>{const r=s.imagesLoaded+1,a=s.totalDataTransferred+t,n=e>3e3?s.slowLoadingImages+1:s.slowLoadingImages;return{...s,imagesLoaded:r,totalDataTransferred:a,slowLoadingImages:n}}))}}))}));return e.observe({entryTypes:["resource"]}),d(e),()=>{e.disconnect()}}),[]);if(e.useEffect((()=>{if(i.length>0){const e=i.reduce(((e,s)=>e+s),0)/i.length;n((s=>({...s,averageLoadTime:e})))}}),[i]),e.useEffect((()=>{const e=setInterval((()=>{const e=c(),s=e.size>0?e.size/(a.imagesLoaded||1)*100:0;n((e=>({...e,cacheHitRate:Math.min(s,100)})))}),5e3);return()=>clearInterval(e)}),[a.imagesLoaded]),e.useEffect((()=>{if(!s)return;const e=x(),t=()=>{n((e=>({...e,imagesError:e.imagesError+1})))};return document.addEventListener("error",(e=>{e.target instanceof HTMLImageElement&&t()}),!0),()=>{e&&e(),o&&o.disconnect(),document.removeEventListener("error",t,!0)}}),[s,x,o]),e.useEffect((()=>{r&&r(a)}),[a,r]),!s)return null;const u=(e,s)=>e<=s.good?"text-green-400":e<=s.poor?"text-yellow-400":"text-red-400";return t.jsxs("div",{className:"fixed bottom-4 right-4 bg-black/90 text-white p-4 rounded-lg shadow-lg text-xs font-mono z-50 max-w-sm",children:[t.jsxs("div",{className:"flex items-center justify-between mb-2",children:[t.jsx("h3",{className:"font-bold text-sm",children:"Media Performance"}),t.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"})]}),t.jsxs("div",{className:"space-y-1",children:[t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Images Loaded:"}),t.jsx("span",{className:"text-blue-400",children:a.imagesLoaded})]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Load Errors:"}),t.jsx("span",{className:a.imagesError>0?"text-red-400":"text-green-400",children:a.imagesError})]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Avg Load Time:"}),t.jsx("span",{className:u(a.averageLoadTime,{good:1e3,poor:3e3}),children:(m=a.averageLoadTime,m<1e3?`${Math.round(m)}ms`:`${(m/1e3).toFixed(2)}s`)})]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Data Transfer:"}),t.jsx("span",{className:"text-purple-400",children:(e=>{if(0===e)return"0 B";const s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["B","KB","MB","GB"][s]})(a.totalDataTransferred)})]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Cache Hit Rate:"}),t.jsxs("span",{className:u(100-a.cacheHitRate,{good:20,poor:50}),children:[a.cacheHitRate.toFixed(1),"%"]})]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Slow Images:"}),t.jsx("span",{className:a.slowLoadingImages>0?"text-red-400":"text-green-400",children:a.slowLoadingImages})]})]}),t.jsx("div",{className:"mt-3 pt-2 border-t border-gray-600",children:t.jsxs("div",{className:"flex items-center space-x-2 text-xs",children:[t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx("div",{className:"w-2 h-2 rounded-full "+(a.averageLoadTime<1e3?"bg-green-400":a.averageLoadTime<3e3?"bg-yellow-400":"bg-red-400")}),t.jsx("span",{children:"Speed"})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx("div",{className:"w-2 h-2 rounded-full "+(0===a.imagesError?"bg-green-400":"bg-red-400")}),t.jsx("span",{children:"Reliability"})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx("div",{className:"w-2 h-2 rounded-full "+(a.cacheHitRate>80?"bg-green-400":a.cacheHitRate>50?"bg-yellow-400":"bg-red-400")}),t.jsx("span",{children:"Cache"})]})]})}),navigator.connection&&t.jsx("div",{className:"mt-2 pt-2 border-t border-gray-600 text-xs",children:t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Connection:"}),t.jsx("span",{className:"text-cyan-400",children:navigator.connection.effectiveType?.toUpperCase()||"Unknown"})]})})]});var m},ae=({visible:e=!1})=>{if(!e)return null;return t.jsxs("div",{className:"fixed top-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs max-w-md z-50",children:[t.jsx("h3",{className:"font-bold mb-2",children:"Environment Variables"}),t.jsx("div",{className:"space-y-1",children:Object.entries({VITE_SUPABASE_URL:"https://vsnsglgyapexhwyfylic.supabase.co",VITE_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzbnNnbGd5YXBleGh3eWZ5bGljIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExOTEsImV4cCI6MjA2MDgzNzE5MX0.6CQWpMT14h2kaIOk1_LMECuJrfRdmiGRo3vGyEDW9tM",MODE:"production",DEV:!1,PROD:!0}).map((([e,s])=>t.jsxs("div",{className:"flex justify-between",children:[t.jsxs("span",{className:"text-gray-300",children:[e,":"]}),t.jsx("span",{className:s?"text-green-400":"text-red-400",children:e.includes("KEY")?s?"✓ Set":"✗ Missing":String(s)})]},e)))}),t.jsx("div",{className:"mt-2 pt-2 border-t border-gray-600",children:t.jsxs("div",{className:"text-gray-300",children:["URL: ",window.location.href]})})]})},ne=e=>({id:e.id,title:e.title,description:e.description||"",thumbnail:e.thumbnail_url||"https://placehold.co/400x225/gray/white?text=No+Thumbnail",videoUrl:e.video_url,duration:e.duration||0,views:e.views||0,likes:e.likes||0,isHD:e.is_hd||!1,category:e.category||"uncategorized",uploadDate:e.created_at,creator:{id:e.user_id,name:e.username||"Unknown User",avatar:e.user_avatar||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0}}),ie=w(((e,s)=>({videos:[],pagination:{currentPage:1,totalPages:1,totalCount:0,pageSize:20},isLoading:!1,error:null,currentCategory:"all",searchQuery:"",fetchVideos:async(s="all",t=1)=>{try{e({isLoading:!0,error:null});const r=await d.getVideos({page:t,limit:20,category:"all"===s?void 0:s,sort:"created_at",order:"DESC"});if(!r.success||!r.data.videos)throw new Error("Failed to fetch videos");{const t=r.data.videos.map(ne);e({videos:t,pagination:{currentPage:r.data.pagination.page,totalPages:r.data.pagination.pages,totalCount:r.data.pagination.total,pageSize:r.data.pagination.limit},currentCategory:s,isLoading:!1})}}catch(r){e({error:r instanceof Error?r.message:"Failed to fetch videos",isLoading:!1})}},searchVideos:async(s,t=1)=>{try{e({isLoading:!0,error:null,searchQuery:s});const r=await d.getVideos({page:t,limit:20,search:s,sort:"created_at",order:"DESC"});if(!r.success||!r.data.videos)throw new Error("Failed to search videos");{const s=r.data.videos.map(ne);e({videos:s,pagination:{currentPage:r.data.pagination.page,totalPages:r.data.pagination.pages,totalCount:r.data.pagination.total,pageSize:r.data.pagination.limit},isLoading:!1})}}catch(r){e({error:r instanceof Error?r.message:"Failed to search videos",isLoading:!1})}},setCategory:t=>{const{fetchVideos:r}=s();e({currentCategory:t,searchQuery:""}),r(t,1)},setPage:async e=>{const{currentCategory:t,searchQuery:r,fetchVideos:a,searchVideos:n}=s();r?await n(r,e):await a(t,e)},clearError:()=>{e({error:null})}}))),le=({visible:s=!1})=>{const[r,a]=e.useState(!1),[n,i]=e.useState(null),[l,o]=e.useState(!1),{videos:c,isLoading:d,error:x}=ie();if(!s)return null;return t.jsxs("div",{className:"fixed bottom-4 left-4 bg-black bg-opacity-90 text-white p-4 rounded-lg text-xs max-w-md z-50 max-h-96 overflow-y-auto",children:[t.jsxs("div",{className:"flex justify-between items-center mb-2",children:[t.jsx("h3",{className:"font-bold",children:"Video Debug Panel"}),t.jsx("button",{onClick:()=>a(!r),className:"text-blue-400 hover:text-blue-300",children:r?"Collapse":"Expand"})]}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-300",children:"Videos in store:"}),t.jsx("span",{className:c.length>0?"text-green-400":"text-red-400",children:c.length})]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-300",children:"Store loading:"}),t.jsx("span",{className:d?"text-yellow-400":"text-green-400",children:d?"Yes":"No"})]}),x&&t.jsxs("div",{className:"text-red-400",children:[t.jsx("span",{className:"text-gray-300",children:"Store error:"})," ",x]}),t.jsx("button",{onClick:async()=>{o(!0),i(null);try{const{data:e,error:s}=await supabase.from("videos").select("\n          id,\n          title,\n          description,\n          thumbnail_url,\n          video_url,\n          duration,\n          views,\n          likes,\n          is_hd,\n          user_id,\n          created_at,\n          updated_at,\n          category,\n          tags,\n          creator:profiles(id, username, avatar_url)\n        ").limit(3);i(s?{success:!1,error:s.message,data:null}:{success:!0,error:null,data:e,count:e?.length||0})}catch(e){i({success:!1,error:e instanceof Error?e.message:"Unknown error",data:null})}finally{o(!1)}},disabled:l,className:"w-full bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs disabled:opacity-50",children:l?"Testing...":"Test Video Fetch"}),n&&t.jsxs("div",{className:"mt-2 p-2 bg-gray-800 rounded",children:[t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Fetch result:"}),t.jsx("span",{className:n.success?"text-green-400":"text-red-400",children:n.success?"Success":"Failed"})]}),n.error&&t.jsxs("div",{className:"text-red-400 mt-1",children:["Error: ",n.error]}),n.success&&t.jsxs("div",{className:"text-green-400 mt-1",children:["Found ",n.count," videos"]})]}),r&&t.jsxs("div",{className:"mt-4 space-y-2",children:[t.jsx("h4",{className:"font-semibold",children:"Sample Videos:"}),c.slice(0,3).map(((e,s)=>t.jsxs("div",{className:"p-2 bg-gray-800 rounded",children:[t.jsx("div",{className:"font-medium truncate",children:e.title}),t.jsxs("div",{className:"text-gray-400 text-xs",children:[t.jsxs("div",{children:["ID: ",e.id]}),t.jsxs("div",{children:["Thumbnail: ",e.thumbnailUrl?"✓":"✗"]}),t.jsxs("div",{children:["Video URL: ",e.videoUrl?"✓":"✗"]}),e.thumbnailUrl&&t.jsx("div",{className:"mt-1",children:t.jsx("button",{onClick:async()=>{var s;await(s=e.thumbnailUrl,new Promise((e=>{const t=new Image;t.onload=()=>e({success:!0,url:s}),t.onerror=()=>e({success:!1,url:s}),t.src=s})))},className:"text-blue-400 hover:text-blue-300",children:"Test Thumbnail"})}),e.videoUrl&&t.jsx("div",{className:"mt-1",children:t.jsx("button",{onClick:async()=>{await(s=e.videoUrl,new Promise((e=>{const t=document.createElement("video");t.onloadedmetadata=()=>e({success:!0,url:s,duration:t.duration}),t.onerror=()=>e({success:!1,url:s}),t.src=s})));var s},className:"text-blue-400 hover:text-blue-300",children:"Test Video"})})]})]},e.id)))]})]})]})},oe=({visible:s=!1,position:r="bottom-left"})=>{const[a,n]=e.useState(!1),[l,o]=e.useState(!1),{user:c,profile:d,isLoading:x,isApproved:u,loadUser:m}=i();if(!s)return null;return t.jsx("div",{className:`fixed ${{"top-left":"top-4 left-4","top-right":"top-4 right-4","bottom-left":"bottom-4 left-4","bottom-right":"bottom-4 right-4"}[r]} z-50`,children:t.jsxs("div",{className:"bg-gray-800 border border-gray-600 rounded-lg shadow-lg max-w-sm",children:[t.jsxs("div",{className:"flex items-center justify-between p-3 cursor-pointer bg-gray-700 rounded-t-lg",onClick:()=>n(!a),children:[t.jsx("span",{className:"text-white text-sm font-medium",children:"Auth Debug"}),t.jsx("span",{className:"text-white text-xs",children:a?"▼":"▶"})]}),a&&t.jsxs("div",{className:"p-3 space-y-3",children:[t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-2 h-2 rounded-full "+(x?"bg-yellow-500":"bg-gray-500")}),t.jsxs("span",{className:"text-white text-xs",children:["Loading: ",x?"Yes":"No"]})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-2 h-2 rounded-full "+(c?"bg-green-500":"bg-red-500")}),t.jsxs("span",{className:"text-white text-xs",children:["User: ",c?"Signed In":"Not Signed In"]})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-2 h-2 rounded-full "+(d?"bg-green-500":"bg-red-500")}),t.jsxs("span",{className:"text-white text-xs",children:["Profile: ",d?"Exists":"Missing"]})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-2 h-2 rounded-full "+(u?"bg-green-500":"bg-orange-500")}),t.jsxs("span",{className:"text-white text-xs",children:["Approved: ",u?"Yes":"No"]})]})]}),c&&t.jsx("div",{className:"border-t border-gray-600 pt-2",children:t.jsxs("div",{className:"text-white text-xs space-y-1",children:[t.jsxs("div",{children:[t.jsx("strong",{children:"ID:"})," ",c.id]}),t.jsxs("div",{children:[t.jsx("strong",{children:"Email:"})," ",c.email]}),d&&t.jsxs(t.Fragment,{children:[t.jsxs("div",{children:[t.jsx("strong",{children:"Username:"})," ",d.username]}),t.jsxs("div",{children:[t.jsx("strong",{children:"Approved:"})," ",d.is_approved?"Yes":"No"]})]})]})}),t.jsxs("div",{className:"border-t border-gray-600 pt-2 space-y-2",children:[t.jsx("button",{onClick:async()=>{o(!0);try{await m()}catch(e){}finally{o(!1)}},disabled:l,className:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white text-xs py-1 px-2 rounded",children:l?"Refreshing...":"Refresh Auth"}),t.jsx("button",{onClick:async()=>{const{data:{session:e}}=await supabase.auth.getSession()},className:"w-full bg-gray-600 hover:bg-gray-700 text-white text-xs py-1 px-2 rounded",children:"Check Session"}),t.jsx("button",{onClick:async()=>{if(!c?.id)return;const{data:e,error:s}=await supabase.from("profiles").select("*").eq("id",c.id).single()},disabled:!c?.id,className:"w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-800 text-white text-xs py-1 px-2 rounded",children:"Check Profile"})]})]})]})})},ce=e.lazy((()=>B((()=>import("./HomePage-C4sUsitP.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8])))),de=e.lazy((()=>B((()=>import("./VideoPage-kJpKkFka.js")),__vite__mapDeps([9,1,2,5,6,4,7,8,10])))),xe=e.lazy((()=>B((()=>import("./CategoryPage-xTbsAtSJ.js")),__vite__mapDeps([11,1,2,8,5,6,3,4,7])))),ue=e.lazy((()=>B((()=>import("./AllVideosPage-CF7d77OI.js")),__vite__mapDeps([12,1,4,5,6,7,8,2])))),me=e.lazy((()=>B((()=>import("./UploadPage-N8FDo36c.js")),__vite__mapDeps([13,1,5,6,4,7,2])))),he=e.lazy((()=>B((()=>import("./ManageVideosPage-CNw7anmk.js")),__vite__mapDeps([14,1,4,5,6,7,2])))),ge=e.lazy((()=>B((()=>import("./SearchPage-WmhySmoL.js")),__vite__mapDeps([15,1,3,4,5,6,7,2])))),pe=e.lazy((()=>B((()=>import("./FavoritesPage-Dkj8XoPT.js")),__vite__mapDeps([16,1,3,4,5,6,7,2])))),fe=e.lazy((()=>B((()=>import("./TestVideoPage-Brls4pMW.js")),__vite__mapDeps([17,1,2,8,5,6,10,4,7])))),je=e.lazy((()=>B((()=>import("./TestVideoIndexPage-CVOUSkL9.js")),__vite__mapDeps([18,1,2])))),be=e.lazy((()=>B((()=>import("./AdminPage-CPypz21G.js")),__vite__mapDeps([19,1,5,6,4,7,2]))));function ye(){const[s,r]=e.useState(!1),[a,n]=e.useState("home"),[l,o]=e.useState(!1),[c,d]=e.useState("login"),[h,g]=e.useState(!0),[p,y]=e.useState(null),[v,w]=e.useState(!1);e.useState(!1);const[N,C]=e.useState(!1);e.useState([]);const{user:E,loadUser:S}=i(),{hasAcceptedDisclaimer:k,acceptDisclaimer:L,checkDisclaimerStatus:_}=se();e.useEffect((()=>{(async()=>{try{const e=await x();C(e);const s=u();if(s.hasConflicts){s.conflicts.some((e=>e.includes("Expired")||e.includes("Corrupted")))&&w(!0)}return m((()=>{confirm("A new version is available. Refresh to update?")&&window.location.reload()}))}catch(e){y("Failed to initialize application. Please refresh the page.")}})()}),[]),e.useEffect((()=>{_(),g(!k)}),[k,_]),e.useEffect((()=>{_()}),[_]),e.useEffect((()=>{S().catch((e=>{y(`Failed to initialize authentication: ${e.message}`)}))}),[S]),e.useEffect((()=>(s?document.body.classList.add("sidebar-open"):document.body.classList.remove("sidebar-open"),()=>{document.body.classList.remove("sidebar-open")})),[s]);return e.useEffect((()=>{const e=window.location.pathname;"/"===e?n("home"):e.includes("/search")?n("search"):e.includes("/upload")?n("upload"):(e.includes("/manage")||e.includes("/profile"))&&n("profile")}),[]),p?t.jsx("div",{className:"min-h-screen bg-gray-900 text-white flex items-center justify-center",children:t.jsxs("div",{className:"text-center p-8",children:[t.jsx("h1",{className:"text-2xl font-bold text-red-400 mb-4",children:"Application Error"}),t.jsx("p",{className:"text-gray-300 mb-4",children:p}),t.jsx("button",{onClick:()=>window.location.reload(),className:"bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded",children:"Reload Page"})]})}):t.jsx(f,{children:t.jsxs("div",{className:"min-h-screen bg-gray-900 text-white "+(s?"sidebar-open":""),children:[t.jsx(ee,{isOpen:h,onAccept:()=>{L(),g(!1)}}),t.jsx(H,{onOpenSidebar:()=>r(!0),isAuthenticated:!!E,onLoginClick:()=>{d("login"),o(!0)},onSignUpClick:()=>{d("signup"),o(!0)}}),t.jsx(G,{isOpen:s,onClose:()=>r(!1)}),t.jsx("main",{className:"pt-16 pb-20 lg:pl-64 transition-all duration-300",children:t.jsx(e.Suspense,{fallback:t.jsx("div",{className:"flex justify-center items-center h-[70vh]",children:t.jsxs("div",{className:"animate-pulse flex flex-col items-center",children:[t.jsx("div",{className:"w-16 h-16 bg-blue-700 rounded-full mb-4"}),t.jsx("div",{className:"h-4 w-32 bg-gray-700 rounded mb-2"}),t.jsx("div",{className:"h-3 w-24 bg-gray-700 rounded"})]})}),children:t.jsxs(j,{children:[t.jsx(b,{path:"/",element:t.jsx(ce,{})}),t.jsx(b,{path:"/video/:id",element:t.jsx(de,{})}),t.jsx(b,{path:"/test-video",element:t.jsx(je,{})}),t.jsx(b,{path:"/test-video/:id",element:t.jsx(fe,{})}),t.jsx(b,{path:"/category/:slug",element:t.jsx(xe,{})}),t.jsx(b,{path:"/all-videos",element:t.jsx(ue,{})}),t.jsx(b,{path:"/upload",element:t.jsx(Z,{requireAuth:!0,requireApproval:!0,children:t.jsx(me,{})})}),t.jsx(b,{path:"/manage",element:t.jsx(Z,{requireAuth:!0,children:t.jsx(he,{})})}),t.jsx(b,{path:"/search",element:t.jsx(ge,{})}),t.jsx(b,{path:"/favorites",element:t.jsx(pe,{})}),t.jsx(b,{path:"/admin",element:t.jsx(Z,{requireAuth:!0,children:t.jsx(be,{})})})]})})}),t.jsx(X,{activeTab:a,onTabChange:e=>{if(n(e),("profile"===e||"upload"===e)&&!E)return d("login"),void o(!0)}}),t.jsx(K,{isOpen:l,onClose:()=>o(!1),initialMode:c}),t.jsx(te,{visible:!1,position:"bottom-right"}),t.jsx(re,{visible:!1}),t.jsx(oe,{visible:!1,position:"bottom-left"}),t.jsx(ae,{visible:!1}),t.jsx(le,{visible:!1}),!1,!1,!1,!1,!1,!1]})})}class ve extends e.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,s){this.setState({error:e,errorInfo:s})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:t.jsx("div",{className:"min-h-screen bg-gray-900 text-white flex items-center justify-center",children:t.jsxs("div",{className:"text-center p-8 max-w-md",children:[t.jsx("h1",{className:"text-2xl font-bold text-red-400 mb-4",children:"Something went wrong"}),t.jsx("p",{className:"text-gray-300 mb-4",children:"We encountered an unexpected error. Please try refreshing the page."}),!1,t.jsx("button",{onClick:()=>window.location.reload(),className:"mt-4 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors",children:"Reload Page"})]})}):this.props.children}}h();const we=document.getElementById("root");if(!we)throw new Error("Root element not found");r(we).render(t.jsx(e.StrictMode,{children:t.jsx(ve,{children:t.jsx(g,{value:{errorRetryCount:3,errorRetryInterval:1e3,dedupingInterval:5e3,focusThrottleInterval:1e4,revalidateOnFocus:!1,revalidateIfStale:!0,revalidateOnReconnect:!0,onError:e=>{},onSuccess:(e,s)=>{}},children:t.jsx(ye,{})})})}));export{q as S,J as a,$ as b,ie as u};
