<?php
/**
 * Logout Debug Test
 * Test logout functionality and session handling
 */

require_once 'namecheap-api/config/database.php';

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>BlueFilmX - Logout Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .test-section { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .logout-btn { background: #dc3545; }
        .logout-btn:hover { background: #c82333; }
        pre { background: #e9ecef; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 BlueFilmX Logout Debug Test</h1>
        
        <?php
        // Check if this is a logout request
        if (isset($_POST['action']) && $_POST['action'] === 'logout') {
            echo '<h2>🚪 Logout Test Results</h2>';
            
            echo '<div class="info">Session before logout:</div>';
            echo '<pre>' . print_r($_SESSION ?? [], true) . '</pre>';
            
            // Perform logout
            try {
                logoutUser();
                echo '<div class="success">✅ Logout function executed successfully</div>';
            } catch (Exception $e) {
                echo '<div class="error">❌ Logout function failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            
            echo '<div class="info">Session after logout:</div>';
            echo '<pre>' . print_r($_SESSION ?? [], true) . '</pre>';
            
            echo '<div class="info">Session status: ' . session_status() . ' (1=disabled, 2=active, 3=none)</div>';
            
            // Test if user is still authenticated
            $currentUser = getCurrentUser();
            if ($currentUser) {
                echo '<div class="error">❌ User still appears to be logged in: ' . htmlspecialchars($currentUser) . '</div>';
            } else {
                echo '<div class="success">✅ User successfully logged out</div>';
            }
            
            echo '<hr><a href="?" style="color: #007bff;">← Back to test page</a>';
            exit;
        }
        
        // Check if this is a login request
        if (isset($_POST['action']) && $_POST['action'] === 'login') {
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            
            if ($username && $password) {
                echo '<h2>🔑 Login Test Results</h2>';
                
                try {
                    $database = new Database();
                    $db = $database->getConnection();
                    
                    $query = "SELECT id, username, password_hash, is_approved FROM profiles WHERE username = :username";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':username', $username);
                    $stmt->execute();
                    $user = $stmt->fetch();
                    
                    if ($user && password_verify($password, $user['password_hash'])) {
                        loginUser($user['id']);
                        echo '<div class="success">✅ Login successful for: ' . htmlspecialchars($username) . '</div>';
                        echo '<div class="info">User ID: ' . htmlspecialchars($user['id']) . '</div>';
                        echo '<div class="info">Session ID: ' . session_id() . '</div>';
                    } else {
                        echo '<div class="error">❌ Invalid credentials</div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="error">❌ Login error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
                
                echo '<hr><a href="?" style="color: #007bff;">← Back to test page</a>';
                exit;
            }
        }
        ?>
        
        <h2>📊 Current Session Status</h2>
        <?php
        session_start();
        
        echo '<div class="test-section">';
        echo '<strong>Session Status:</strong> ' . session_status() . ' (1=disabled, 2=active, 3=none)<br>';
        echo '<strong>Session ID:</strong> ' . session_id() . '<br>';
        echo '<strong>Session Name:</strong> ' . session_name() . '<br>';
        
        $currentUser = getCurrentUser();
        if ($currentUser) {
            echo '<div class="success">✅ User is logged in: ' . htmlspecialchars($currentUser) . '</div>';
        } else {
            echo '<div class="info">ℹ️ No user is currently logged in</div>';
        }
        
        echo '<strong>Session Data:</strong><br>';
        echo '<pre>' . print_r($_SESSION, true) . '</pre>';
        echo '</div>';
        ?>
        
        <h2>🧪 Test Actions</h2>
        
        <?php if (!getCurrentUser()): ?>
        <div class="test-section">
            <h3>1. Login Test</h3>
            <form method="POST">
                <input type="hidden" name="action" value="login">
                <label>Username: <input type="text" name="username" value="username" required></label><br><br>
                <label>Password: <input type="password" name="password" value="password123" required></label><br><br>
                <button type="submit">🔑 Test Login</button>
            </form>
        </div>
        <?php else: ?>
        <div class="test-section">
            <h3>2. Logout Test</h3>
            <p>You are currently logged in. Test the logout functionality:</p>
            <form method="POST">
                <input type="hidden" name="action" value="logout">
                <button type="submit" class="logout-btn">🚪 Test Logout</button>
            </form>
        </div>
        <?php endif; ?>
        
        <h2>🔧 Session Configuration</h2>
        <div class="test-section">
            <?php
            echo '<strong>PHP Session Settings:</strong><br>';
            echo 'session.use_cookies: ' . ini_get('session.use_cookies') . '<br>';
            echo 'session.use_only_cookies: ' . ini_get('session.use_only_cookies') . '<br>';
            echo 'session.cookie_lifetime: ' . ini_get('session.cookie_lifetime') . '<br>';
            echo 'session.cookie_path: ' . ini_get('session.cookie_path') . '<br>';
            echo 'session.cookie_domain: ' . ini_get('session.cookie_domain') . '<br>';
            echo 'session.cookie_secure: ' . ini_get('session.cookie_secure') . '<br>';
            echo 'session.cookie_httponly: ' . ini_get('session.cookie_httponly') . '<br>';
            echo 'session.cookie_samesite: ' . ini_get('session.cookie_samesite') . '<br>';
            ?>
        </div>
        
        <h2>🌐 API Test</h2>
        <div class="test-section">
            <p>Test the logout API endpoint directly:</p>
            <button onclick="testLogoutAPI()">🔌 Test Logout API</button>
            <div id="api-result"></div>
        </div>
        
        <script>
        async function testLogoutAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<div class="info">Testing logout API...</div>';
            
            try {
                const response = await fetch('/api/auth.php?action=logout', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="success">✅ API logout successful: ' + JSON.stringify(data) + '</div>';
                    setTimeout(() => location.reload(), 1000);
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ API logout failed: ' + JSON.stringify(data) + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ API request failed: ' + error.message + '</div>';
            }
        }
        </script>
        
        <hr>
        <p><small>🔒 This debug tool should be removed after testing for security reasons.</small></p>
    </div>
</body>
</html>