# .htaccess for /media/ directory
# Optimized for video and image serving

# Enable CORS for media files
<IfModule mod_headers.c>
    # Allow cross-origin requests for media files
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, HEAD, OPTIONS"
    Header always set Access-Control-Allow-Headers "Range, Content-Range, Content-Type"
    Header always set Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges"
    
    # Enable range requests for video streaming
    Header always set Accept-Ranges "bytes"
    
    # Cache control for media files
    <FilesMatch "\.(mp4|webm|mov|avi|mkv|flv|wmv|3gp)$">
        Header set Cache-Control "public, max-age=31536000, immutable"
        Header set Expires "access plus 1 year"
    </FilesMatch>
    
    <FilesMatch "\.(jpg|jpeg|png|gif|webp|avif)$">
        Header set Cache-Control "public, max-age=31536000, immutable"
        Header set Expires "access plus 1 year"
    </FilesMatch>
</IfModule>

# Proper MIME types for video files
<IfModule mod_mime.c>
    AddType video/mp4 .mp4
    AddType video/webm .webm
    AddType video/quicktime .mov
    AddType video/x-msvideo .avi
    AddType video/x-matroska .mkv
    AddType video/x-flv .flv
    AddType video/x-ms-wmv .wmv
    AddType video/3gpp .3gp
    
    # Image types
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/webp .webp
    AddType image/avif .avif
</IfModule>

# Enable compression for text-based files (not for media)
<IfModule mod_deflate.c>
    # Don't compress media files (they're already compressed)
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|webp|avif|mp4|webm|mov|avi|mkv|flv|wmv|3gp)$ no-gzip dont-vary
</IfModule>

# Security headers
<IfModule mod_headers.c>
    # Prevent hotlinking while allowing legitimate access
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # Allow embedding videos in iframes from same origin
    <FilesMatch "\.(mp4|webm|mov|avi|mkv|flv|wmv|3gp)$">
        Header always set X-Frame-Options "SAMEORIGIN"
    </FilesMatch>
</IfModule>

# Prevent directory browsing
Options -Indexes

# Allow range requests (important for video streaming)
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle range requests properly
    RewriteCond %{HTTP:Range} ^bytes=
    RewriteRule ^(.*)$ - [E=HTTP_RANGE:%{HTTP:Range}]
</IfModule>

# File size limits (adjust as needed)
<IfModule mod_php.c>
    php_value upload_max_filesize 100M
    php_value post_max_size 100M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 256M
</IfModule>

# Error pages
ErrorDocument 403 "Access Forbidden"
ErrorDocument 404 "File Not Found"
