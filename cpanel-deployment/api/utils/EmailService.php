<?php
/**
 * Email Service
 * Handles sending emails for authentication purposes
 */

class EmailService {
    private $config;
    private $templates;
    
    public function __construct() {
        $this->config = [
            'smtp_host' => $_ENV['SMTP_HOST'] ?? 'localhost',
            'smtp_port' => $_ENV['SMTP_PORT'] ?? 587,
            'smtp_username' => $_ENV['SMTP_USERNAME'] ?? '',
            'smtp_password' => $_ENV['SMTP_PASSWORD'] ?? '',
            'smtp_encryption' => $_ENV['SMTP_ENCRYPTION'] ?? 'tls',
            'from_email' => $_ENV['FROM_EMAIL'] ?? '<EMAIL>',
            'from_name' => $_ENV['FROM_NAME'] ?? 'BlueFilmX',
            'base_url' => $_ENV['BASE_URL'] ?? 'https://www.bluefilmx.com'
        ];
        
        $this->loadTemplates();
    }
    
    /**
     * Load email templates
     */
    private function loadTemplates() {
        $this->templates = [
            'email_verification' => [
                'subject' => 'Verify Your Email Address',
                'html' => $this->getEmailVerificationTemplate(),
                'text' => $this->getEmailVerificationTextTemplate()
            ],
            'password_reset' => [
                'subject' => 'Reset Your Password',
                'html' => $this->getPasswordResetTemplate(),
                'text' => $this->getPasswordResetTextTemplate()
            ],
            'welcome' => [
                'subject' => 'Welcome to BlueFilmX!',
                'html' => $this->getWelcomeTemplate(),
                'text' => $this->getWelcomeTextTemplate()
            ],
            'password_changed' => [
                'subject' => 'Password Changed Successfully',
                'html' => $this->getPasswordChangedTemplate(),
                'text' => $this->getPasswordChangedTextTemplate()
            ]
        ];
    }
    
    /**
     * Send email verification
     */
    public function sendEmailVerification($email, $username, $token) {
        $verificationUrl = $this->config['base_url'] . '/verify-email?token=' . $token;
        
        $variables = [
            'username' => $username,
            'verification_url' => $verificationUrl,
            'base_url' => $this->config['base_url']
        ];
        
        return $this->sendEmail(
            $email,
            $username,
            'email_verification',
            $variables
        );
    }
    
    /**
     * Send password reset email
     */
    public function sendPasswordReset($email, $username, $token) {
        $resetUrl = $this->config['base_url'] . '/reset-password?token=' . $token;
        
        $variables = [
            'username' => $username,
            'reset_url' => $resetUrl,
            'base_url' => $this->config['base_url']
        ];
        
        return $this->sendEmail(
            $email,
            $username,
            'password_reset',
            $variables
        );
    }
    
    /**
     * Send welcome email
     */
    public function sendWelcomeEmail($email, $username) {
        $variables = [
            'username' => $username,
            'base_url' => $this->config['base_url']
        ];
        
        return $this->sendEmail(
            $email,
            $username,
            'welcome',
            $variables
        );
    }
    
    /**
     * Send password changed notification
     */
    public function sendPasswordChangedNotification($email, $username) {
        $variables = [
            'username' => $username,
            'base_url' => $this->config['base_url'],
            'timestamp' => date('Y-m-d H:i:s T')
        ];
        
        return $this->sendEmail(
            $email,
            $username,
            'password_changed',
            $variables
        );
    }
    
    /**
     * Send email using configured method
     */
    private function sendEmail($to, $toName, $template, $variables = []) {
        if (!isset($this->templates[$template])) {
            throw new Exception('Email template not found: ' . $template);
        }
        
        $templateData = $this->templates[$template];
        
        // Replace variables in template
        $subject = $this->replaceVariables($templateData['subject'], $variables);
        $htmlBody = $this->replaceVariables($templateData['html'], $variables);
        $textBody = $this->replaceVariables($templateData['text'], $variables);
        
        // Try to send using PHPMailer if available, otherwise use mail()
        if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
            return $this->sendWithPHPMailer($to, $toName, $subject, $htmlBody, $textBody);
        } else {
            return $this->sendWithMail($to, $subject, $htmlBody, $textBody);
        }
    }
    
    /**
     * Send email using PHPMailer
     */
    private function sendWithPHPMailer($to, $toName, $subject, $htmlBody, $textBody) {
        try {
            $mail = new PHPMailer\PHPMailer\PHPMailer(true);
            
            // Server settings
            $mail->isSMTP();
            $mail->Host = $this->config['smtp_host'];
            $mail->SMTPAuth = !empty($this->config['smtp_username']);
            $mail->Username = $this->config['smtp_username'];
            $mail->Password = $this->config['smtp_password'];
            $mail->SMTPSecure = $this->config['smtp_encryption'];
            $mail->Port = $this->config['smtp_port'];
            
            // Recipients
            $mail->setFrom($this->config['from_email'], $this->config['from_name']);
            $mail->addAddress($to, $toName);
            
            // Content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $htmlBody;
            $mail->AltBody = $textBody;
            
            $mail->send();
            return true;
            
        } catch (Exception $e) {
            error_log('Email sending failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send email using PHP mail() function
     */
    private function sendWithMail($to, $subject, $htmlBody, $textBody) {
        $headers = [
            'MIME-Version: 1.0',
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . $this->config['from_name'] . ' <' . $this->config['from_email'] . '>',
            'Reply-To: ' . $this->config['from_email'],
            'X-Mailer: PHP/' . phpversion()
        ];
        
        return mail($to, $subject, $htmlBody, implode("\r\n", $headers));
    }
    
    /**
     * Replace variables in template
     */
    private function replaceVariables($template, $variables) {
        foreach ($variables as $key => $value) {
            $template = str_replace('{{' . $key . '}}', $value, $template);
        }
        return $template;
    }
    
    /**
     * Email verification HTML template
     */
    private function getEmailVerificationTemplate() {
        return '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Verify Your Email</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #007bff; color: white; padding: 20px; text-align: center; }
                .content { padding: 30px 20px; }
                .button { display: inline-block; background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>BlueFilmX</h1>
                </div>
                <div class="content">
                    <h2>Verify Your Email Address</h2>
                    <p>Hello {{username}},</p>
                    <p>Thank you for signing up for BlueFilmX! To complete your registration, please verify your email address by clicking the button below:</p>
                    <p style="text-align: center;">
                        <a href="{{verification_url}}" class="button">Verify Email Address</a>
                    </p>
                    <p>If the button doesn\'t work, you can copy and paste this link into your browser:</p>
                    <p style="word-break: break-all;">{{verification_url}}</p>
                    <p>This verification link will expire in 24 hours for security reasons.</p>
                    <p>If you didn\'t create an account with us, please ignore this email.</p>
                </div>
                <div class="footer">
                    <p>&copy; 2024 BlueFilmX. All rights reserved.</p>
                    <p><a href="{{base_url}}">Visit our website</a></p>
                </div>
            </div>
        </body>
        </html>
        ';
    }
    
    /**
     * Email verification text template
     */
    private function getEmailVerificationTextTemplate() {
        return '
Hello {{username}},

Thank you for signing up for BlueFilmX! To complete your registration, please verify your email address by visiting this link:

{{verification_url}}

This verification link will expire in 24 hours for security reasons.

If you didn\'t create an account with us, please ignore this email.

Best regards,
The BlueFilmX Team

© 2024 BlueFilmX. All rights reserved.
        ';
    }
    
    /**
     * Password reset HTML template
     */
    private function getPasswordResetTemplate() {
        return '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reset Your Password</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
                .content { padding: 30px 20px; }
                .button { display: inline-block; background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>BlueFilmX</h1>
                </div>
                <div class="content">
                    <h2>Reset Your Password</h2>
                    <p>Hello {{username}},</p>
                    <p>We received a request to reset your password. Click the button below to create a new password:</p>
                    <p style="text-align: center;">
                        <a href="{{reset_url}}" class="button">Reset Password</a>
                    </p>
                    <p>If the button doesn\'t work, you can copy and paste this link into your browser:</p>
                    <p style="word-break: break-all;">{{reset_url}}</p>
                    <p>This password reset link will expire in 1 hour for security reasons.</p>
                    <p>If you didn\'t request a password reset, please ignore this email. Your password will remain unchanged.</p>
                </div>
                <div class="footer">
                    <p>&copy; 2024 BlueFilmX. All rights reserved.</p>
                    <p><a href="{{base_url}}">Visit our website</a></p>
                </div>
            </div>
        </body>
        </html>
        ';
    }
    
    /**
     * Password reset text template
     */
    private function getPasswordResetTextTemplate() {
        return '
Hello {{username}},

We received a request to reset your password. Visit this link to create a new password:

{{reset_url}}

This password reset link will expire in 1 hour for security reasons.

If you didn\'t request a password reset, please ignore this email. Your password will remain unchanged.

Best regards,
The BlueFilmX Team

© 2024 BlueFilmX. All rights reserved.
        ';
    }
    
    /**
     * Welcome email HTML template
     */
    private function getWelcomeTemplate() {
        return '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to BlueFilmX</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #28a745; color: white; padding: 20px; text-align: center; }
                .content { padding: 30px 20px; }
                .button { display: inline-block; background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Welcome to BlueFilmX!</h1>
                </div>
                <div class="content">
                    <h2>Your account is ready!</h2>
                    <p>Hello {{username}},</p>
                    <p>Welcome to BlueFilmX! Your email has been verified and your account is now active.</p>
                    <p>You can now:</p>
                    <ul>
                        <li>Upload and share your videos</li>
                        <li>Discover amazing content from other creators</li>
                        <li>Connect with the community</li>
                        <li>Customize your profile</li>
                    </ul>
                    <p style="text-align: center;">
                        <a href="{{base_url}}" class="button">Start Exploring</a>
                    </p>
                    <p>If you have any questions, feel free to contact our support team.</p>
                </div>
                <div class="footer">
                    <p>&copy; 2024 BlueFilmX. All rights reserved.</p>
                    <p><a href="{{base_url}}">Visit our website</a></p>
                </div>
            </div>
        </body>
        </html>
        ';
    }
    
    /**
     * Welcome email text template
     */
    private function getWelcomeTextTemplate() {
        return '
Hello {{username}},

Welcome to BlueFilmX! Your email has been verified and your account is now active.

You can now:
- Upload and share your videos
- Discover amazing content from other creators
- Connect with the community
- Customize your profile

Visit our website: {{base_url}}

If you have any questions, feel free to contact our support team.

Best regards,
The BlueFilmX Team

© 2024 BlueFilmX. All rights reserved.
        ';
    }
    
    /**
     * Password changed HTML template
     */
    private function getPasswordChangedTemplate() {
        return '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Password Changed</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #ffc107; color: #212529; padding: 20px; text-align: center; }
                .content { padding: 30px 20px; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>BlueFilmX</h1>
                </div>
                <div class="content">
                    <h2>Password Changed Successfully</h2>
                    <p>Hello {{username}},</p>
                    <p>This is a confirmation that your password was successfully changed on {{timestamp}}.</p>
                    <p>If you made this change, no further action is required.</p>
                    <p>If you did not change your password, please contact our support team immediately as your account may be compromised.</p>
                </div>
                <div class="footer">
                    <p>&copy; 2024 BlueFilmX. All rights reserved.</p>
                    <p><a href="{{base_url}}">Visit our website</a></p>
                </div>
            </div>
        </body>
        </html>
        ';
    }
    
    /**
     * Password changed text template
     */
    private function getPasswordChangedTextTemplate() {
        return '
Hello {{username}},

This is a confirmation that your password was successfully changed on {{timestamp}}.

If you made this change, no further action is required.

If you did not change your password, please contact our support team immediately as your account may be compromised.

Best regards,
The BlueFilmX Team

© 2024 BlueFilmX. All rights reserved.
        ';
    }
}
?>