<?php
/**
 * Database Setup Script
 * Creates the necessary tables for authentication
 */

// Include database configuration
require_once 'config.php';

// Get database connection
$pdo = getDbConnection();
if (!$pdo) {
    echo "Database connection failed.\n";
    exit(1);
}

echo "Connected to database successfully.\n";
    
    // Create users table
    $createUsersTable = "
        CREATE TABLE IF NOT EXISTS users (
            id VARCHAR(255) PRIMARY KEY,
            email VARCHAR(255) UNIQUE NOT NULL,
            name VARCHAR(255),
            password_hash VARCHAR(255) NOT NULL,
            email_verified BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createUsersTable);
    echo "Users table created successfully.\n";
    
    // Create user_sessions table
    $createSessionsTable = "
        CREATE TABLE IF NOT EXISTS user_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            token VARCHAR(255) UNIQUE NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_token (token),
            INDEX idx_user_id (user_id),
            INDEX idx_expires_at (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createSessionsTable);
    echo "User sessions table created successfully.\n";
    
    // Create a cleanup procedure for expired sessions
    $createCleanupProcedure = "
        CREATE EVENT IF NOT EXISTS cleanup_expired_sessions
        ON SCHEDULE EVERY 1 HOUR
        DO
        DELETE FROM user_sessions WHERE expires_at < NOW()
    ";
    
    $pdo->exec($createCleanupProcedure);
    echo "Session cleanup procedure created successfully.\n";
    
    echo "Database setup completed successfully!\n";
?>
