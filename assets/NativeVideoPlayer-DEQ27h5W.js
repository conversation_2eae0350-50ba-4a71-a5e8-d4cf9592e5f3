import{r as e,j as t}from"./react-core-B9nwsbCA.js";import{c as r,p as s}from"./state-4gBW_4Nx.js";import{u as a}from"./main-c4q8g3R4.js";import{D as i,E as n}from"./utils-Dga8D_Ky.js";const o=r()(s(((e,t)=>({watchHistory:{},likedVideos:[],watchLater:[],viewedCategories:{},isLoading:!1,error:null,updateWatchProgress:(t,r,s)=>{const a=Math.min(Math.round(r/s*100),100),i=a>=90;e((e=>({watchHistory:{...e.watchHistory,[t]:{currentTime:r,duration:s,percent:a,lastWatched:(new Date).toISOString(),completed:i}}})));(async()=>{const{data:{user:e}}=await supabase.auth.getUser();if(e){const{error:n}=await supabase.from("watch_history").upsert({user_id:e.id,video_id:t,playback_position:r,duration:s,percent:a,last_watched:(new Date).toISOString(),completed:i},{onConflict:"user_id,video_id"})}})()},getWatchProgress:e=>t().watchHistory[e]||null,getContinueWatchingVideos:async()=>{e({isLoading:!0,error:null});try{const{data:{user:r}}=await supabase.auth.getUser();if(!r){const r=t().watchHistory,s=new Date;s.setDate(s.getDate()-30);const a=Object.entries(r).filter((([e,t])=>{const r=new Date(t.lastWatched);return!t.completed&&r>s})).sort((([e,t],[r,s])=>new Date(s.lastWatched).getTime()-new Date(t.lastWatched).getTime())).map((([e,t])=>e));return e({isLoading:!1}),a}const{data:s,error:a}=await supabase.from("watch_history").select("video_id, last_watched, completed, percent").eq("user_id",r.id).eq("completed",!1).gt("percent",5).lt("percent",90).order("last_watched",{ascending:!1}).limit(10);if(a)throw new Error(a.message);const i={...t().watchHistory};return s.forEach((e=>{i[e.video_id]={currentTime:0,duration:0,percent:e.percent,lastWatched:e.last_watched,completed:e.completed}})),e({watchHistory:i,isLoading:!1}),s.map((e=>e.video_id))}catch(r){return e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1}),[]}},likeVideo:async t=>{e({isLoading:!0,error:null});try{const{data:{user:r}}=await supabase.auth.getUser();if(!r)return e((e=>({likedVideos:[...e.likedVideos,t],isLoading:!1}))),!0;const{error:s}=await supabase.from("video_likes").insert({user_id:r.id,video_id:t,liked_at:(new Date).toISOString()});if(s)throw new Error(s.message);return await supabase.rpc("increment_video_likes",{video_id:t}),e((e=>({likedVideos:[...e.likedVideos,t],isLoading:!1}))),!0}catch(r){return e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1}),!1}},unlikeVideo:async t=>{e({isLoading:!0,error:null});try{const{data:{user:r}}=await supabase.auth.getUser();if(!r)return e((e=>({likedVideos:e.likedVideos.filter((e=>e!==t)),isLoading:!1}))),!0;const{error:s}=await supabase.from("video_likes").delete().eq("user_id",r.id).eq("video_id",t);if(s)throw new Error(s.message);return await supabase.rpc("decrement_video_likes",{video_id:t}),e((e=>({likedVideos:e.likedVideos.filter((e=>e!==t)),isLoading:!1}))),!0}catch(r){return e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1}),!1}},isVideoLiked:e=>t().likedVideos.includes(e),addToWatchLater:t=>{e((e=>({watchLater:[...e.watchLater,t]})));(async()=>{const{data:{user:e}}=await supabase.auth.getUser();if(e){const{error:r}=await supabase.from("watch_later").insert({user_id:e.id,video_id:t,added_at:(new Date).toISOString()})}})()},removeFromWatchLater:t=>{e((e=>({watchLater:e.watchLater.filter((e=>e!==t))})));(async()=>{const{data:{user:e}}=await supabase.auth.getUser();if(e){const{error:r}=await supabase.from("watch_later").delete().eq("user_id",e.id).eq("video_id",t)}})()},isInWatchLater:e=>t().watchLater.includes(e),trackCategoryView:t=>{t&&e((e=>{const r={...e.viewedCategories};return r[t]=(r[t]||0)+1,{viewedCategories:r}}))},getMostViewedCategories:(e=5)=>{const r=t().viewedCategories;return Object.entries(r).sort((([e,t],[r,s])=>s-t)).slice(0,e).map((([e,t])=>e))}})),{name:"user-preferences-storage",partialize:e=>({watchHistory:e.watchHistory,likedVideos:e.likedVideos,watchLater:e.watchLater,viewedCategories:e.viewedCategories})})),d=e.memo((({video:r,onBack:s})=>{const[d,c]=e.useState(!1),[l,u]=e.useState(0),[m,h]=e.useState(r.duration||0),[v,w]=e.useState(i()),[g,p]=e.useState(!1),[f,b]=e.useState(!1),[x,L]=e.useState(null),[k,E]=e.useState(0);e.useState(!1),e.useEffect((()=>{}),[r]);const y=e.useRef(null),_=e.useRef(null),j=e.useRef(!1),{updateWatchProgress:V,getWatchProgress:N}=o(),{incrementVideoViews:S}=a();return e.useEffect((()=>{const e=navigator.userAgent||navigator.vendor||window.opera;b(/iPad|iPhone|iPod/.test(e)&&!window.MSStream),p(/Android/i.test(e))}),[]),e.useEffect((()=>{const e=()=>{w(i())};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[]),e.useEffect((()=>{const e=()=>{y.current&&!isInFullscreen()&&y.current.paused};return document.addEventListener("fullscreenchange",e),document.addEventListener("webkitfullscreenchange",e),document.addEventListener("mozfullscreenchange",e),document.addEventListener("MSFullscreenChange",e),()=>{document.removeEventListener("fullscreenchange",e),document.removeEventListener("webkitfullscreenchange",e),document.removeEventListener("mozfullscreenchange",e),document.removeEventListener("MSFullscreenChange",e)}}),[]),e.useEffect((()=>{if(!y.current)return;const e=()=>{if(y.current){const e=y.current.currentTime,t=y.current.duration,s=e/t*100;u(e),h(t),Math.floor(e)%5==0&&V(r.id,e,t),s>=10&&!j.current&&(S(r.id),j.current=!0)}},t=()=>c(!0),s=()=>c(!1),a=()=>{c(!1),m>0&&V(r.id,m,m)},i=()=>{if(y.current){h(y.current.duration);const e=N(r.id);e&&!e.completed&&(y.current.currentTime=e.currentTime,u(e.currentTime))}},n=e=>{const t=e.target.error;if(t){let e="Unknown error";switch(t.code){case t.MEDIA_ERR_ABORTED:e="Video loading was aborted";break;case t.MEDIA_ERR_NETWORK:e="Network error while loading video";break;case t.MEDIA_ERR_DECODE:e="Video decoding error - format may not be supported";break;case t.MEDIA_ERR_SRC_NOT_SUPPORTED:e="Video format not supported by this browser"}L(`Video failed to load: ${e}`)}},o=()=>{g&&y.current&&(isInFullscreen()?exitFullscreen():requestFullscreen(y.current))},d=y.current;return d.addEventListener("timeupdate",e),d.addEventListener("play",t),d.addEventListener("pause",s),d.addEventListener("ended",a),d.addEventListener("loadedmetadata",i),d.addEventListener("error",n),g&&d.addEventListener("dblclick",o),f&&(d.addEventListener("webkitbeginfullscreen",(()=>{})),d.addEventListener("webkitendfullscreen",(()=>{}))),()=>{d.removeEventListener("timeupdate",e),d.removeEventListener("play",t),d.removeEventListener("pause",s),d.removeEventListener("ended",a),d.removeEventListener("loadedmetadata",i),d.removeEventListener("error",n),g&&d.removeEventListener("dblclick",o),f&&(d.removeEventListener("webkitbeginfullscreen",(()=>{})),d.removeEventListener("webkitendfullscreen",(()=>{}))),m>0&&V(r.id,l,m)}}),[r.id,V,S,N,l,m,f,g]),t.jsxs("div",{className:"bg-black video-player-container relative z-10",children:[t.jsx("div",{ref:_,className:"relative aspect-video max-h-[70vh] bg-black video-container",children:x?t.jsx("div",{className:"w-full h-full flex items-center justify-center bg-gray-800",children:t.jsxs("div",{className:"text-center text-white p-4",children:[t.jsx("h3",{className:"text-lg font-bold mb-2",children:"Video Unavailable"}),t.jsx("p",{className:"text-gray-300 text-sm",children:x}),t.jsx("p",{className:"text-gray-400 text-xs mt-2",children:"This video may have been moved or is temporarily unavailable."}),t.jsxs("div",{className:"mt-4 space-x-2",children:[t.jsx("button",{onClick:()=>{L(null),E(0),y.current&&y.current.load()},className:"inline-block bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm transition-colors",children:"Retry Video"}),t.jsx("a",{href:n(r.videoUrl),target:"_blank",rel:"noopener noreferrer",className:"inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors",children:"Download Video File"})]}),t.jsxs("details",{className:"mt-4 text-left",children:[t.jsx("summary",{className:"cursor-pointer text-gray-400 text-xs",children:"Technical Details"}),t.jsxs("div",{className:"mt-2 text-xs text-gray-500 font-mono",children:[t.jsxs("p",{children:["Video URL: ",n(r.videoUrl)]}),t.jsxs("p",{children:["Original URL: ",r.videoUrl]}),t.jsxs("p",{children:["Error: ",x]}),t.jsxs("p",{children:["Browser: ",navigator.userAgent]})]})]})]})}):t.jsxs("video",{ref:y,className:"w-full h-full object-contain bg-black native-video-player",poster:n(r.thumbnailUrl),controls:!0,playsInline:!0,"webkit-playsinline":"true","x-webkit-airplay":"allow",preload:"metadata",autoPlay:!0,controlsList:"nodownload",disablePictureInPicture:v,children:[t.jsx("source",{src:n(r.videoUrl),type:"video/mp4"}),t.jsx("source",{src:n(r.videoUrl),type:"video/webm"}),t.jsx("source",{src:n(r.videoUrl),type:"video/ogg"}),t.jsxs("p",{className:"text-white p-4",children:["Your browser does not support the video tag.",t.jsx("a",{href:n(r.videoUrl),className:"text-blue-400 underline ml-1",children:"Download the video file"})]})]})}),s&&t.jsx("div",{className:"mt-4 flex justify-start",children:t.jsxs("button",{className:"bg-blue-700 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors flex items-center",onClick:s,"aria-label":"Back to previous page",children:[t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-2",children:t.jsx("path",{d:"M19 12H5M12 19l-7-7 7-7"})}),"Back"]})})]})}));export{d as N};
