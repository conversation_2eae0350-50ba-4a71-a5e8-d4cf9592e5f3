<?php
/**
 * Simple API Test
 */

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? 'none';
$input = json_decode(file_get_contents('php://input'), true) ?? [];

echo json_encode([
    'success' => true,
    'message' => 'API is working!',
    'method' => $method,
    'action' => $action,
    'input' => $input,
    'timestamp' => date('Y-m-d H:i:s')
]);
?>
