<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Video Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        video {
            width: 100%;
            max-width: 600px;
            height: auto;
            background-color: #000;
            border-radius: 5px;
            margin: 20px 0;
        }
        .error-display {
            background-color: #2e1a1a;
            border: 1px solid #ef4444;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success-display {
            background-color: #1a2e1a;
            border: 1px solid #4ade80;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #2563eb; }
        .log {
            background-color: #111;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎬 Minimal Video Test</h1>
    <p>Testing the exact video that's failing with minimal setup</p>

    <div>
        <h3>Direct Video Test</h3>
        <p>This is the exact video URL that's failing in the main application:</p>
        <p><strong>URL:</strong> <code>https://www.bluefilmx.com/media/videos/1749736915_79c6e6c523b72d5e_2025-05-18_23_11_46.mp4</code></p>
        
        <video 
            id="testVideo" 
            controls 
            preload="metadata"
            poster="https://www.bluefilmx.com/media/thumbnails/1749736916_10bbc56e45340192_thumbnail-1749736885235.jpg"
        >
            <source src="https://www.bluefilmx.com/media/videos/1749736915_79c6e6c523b72d5e_2025-05-18_23_11_46.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        
        <div id="status"></div>
        
        <button onclick="testAlternativeVideo()">Test Working Video (Big Buck Bunny)</button>
        <button onclick="reloadVideo()">Reload Failed Video</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="log" id="log"></div>

    <script>
        const video = document.getElementById('testVideo');
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#ef4444' : type === 'success' ? '#4ade80' : '#60a5fa';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            logDiv.innerHTML = '';
        }

        function updateStatus(message, type = 'info') {
            statusDiv.className = type === 'error' ? 'error-display' : type === 'success' ? 'success-display' : '';
            statusDiv.innerHTML = `<strong>${type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️'} ${message}</strong>`;
        }

        // Set up comprehensive video event listeners
        video.addEventListener('loadstart', () => {
            log('Video load started', 'info');
            updateStatus('Loading video...', 'info');
        });

        video.addEventListener('durationchange', () => {
            log(`Video duration: ${video.duration} seconds`, 'info');
        });

        video.addEventListener('loadedmetadata', () => {
            log(`Video metadata loaded - Duration: ${video.duration}s, Size: ${video.videoWidth}x${video.videoHeight}`, 'success');
            updateStatus(`Video loaded successfully! Duration: ${video.duration.toFixed(2)}s, Size: ${video.videoWidth}x${video.videoHeight}`, 'success');
        });

        video.addEventListener('loadeddata', () => {
            log('Video data loaded (first frame)', 'success');
        });

        video.addEventListener('canplay', () => {
            log('Video can start playing', 'success');
        });

        video.addEventListener('canplaythrough', () => {
            log('Video can play through without buffering', 'success');
        });

        video.addEventListener('play', () => {
            log('Video started playing', 'info');
        });

        video.addEventListener('pause', () => {
            log('Video paused', 'info');
        });

        video.addEventListener('ended', () => {
            log('Video playback ended', 'info');
        });

        video.addEventListener('error', (e) => {
            const error = video.error;
            let errorMessage = 'Unknown video error';
            let technicalDetails = '';
            
            if (error) {
                switch (error.code) {
                    case error.MEDIA_ERR_ABORTED:
                        errorMessage = 'Video playback was aborted by user';
                        technicalDetails = 'MEDIA_ERR_ABORTED (1)';
                        break;
                    case error.MEDIA_ERR_NETWORK:
                        errorMessage = 'Network error occurred while loading video';
                        technicalDetails = 'MEDIA_ERR_NETWORK (2)';
                        break;
                    case error.MEDIA_ERR_DECODE:
                        errorMessage = 'Video format not supported by this browser (decode error)';
                        technicalDetails = 'MEDIA_ERR_DECODE (3) - This is the exact error users are seeing!';
                        break;
                    case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                        errorMessage = 'Video source format not supported';
                        technicalDetails = 'MEDIA_ERR_SRC_NOT_SUPPORTED (4)';
                        break;
                    default:
                        errorMessage = `Unknown error (code: ${error.code})`;
                        technicalDetails = `Error code: ${error.code}`;
                }
                
                if (error.message) {
                    technicalDetails += ` - ${error.message}`;
                }
            }
            
            log(`VIDEO ERROR: ${errorMessage} - ${technicalDetails}`, 'error');
            updateStatus(`${errorMessage}<br><small>${technicalDetails}</small>`, 'error');
        });

        video.addEventListener('stalled', () => {
            log('Video download stalled', 'error');
        });

        video.addEventListener('suspend', () => {
            log('Video download suspended', 'info');
        });

        video.addEventListener('abort', () => {
            log('Video loading aborted', 'error');
        });

        video.addEventListener('emptied', () => {
            log('Video element emptied', 'info');
        });

        video.addEventListener('waiting', () => {
            log('Video waiting for data', 'info');
        });

        video.addEventListener('seeking', () => {
            log('Video seeking', 'info');
        });

        video.addEventListener('seeked', () => {
            log('Video seek completed', 'info');
        });

        video.addEventListener('progress', () => {
            if (video.buffered.length > 0) {
                const buffered = video.buffered.end(video.buffered.length - 1);
                const duration = video.duration || 0;
                const percent = duration > 0 ? (buffered / duration * 100).toFixed(1) : 0;
                log(`Video buffering: ${percent}% (${buffered.toFixed(1)}s of ${duration.toFixed(1)}s)`, 'info');
            }
        });

        function testAlternativeVideo() {
            log('Testing alternative video (Big Buck Bunny)', 'info');
            video.src = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
            video.load();
            updateStatus('Testing alternative video...', 'info');
        }

        function reloadVideo() {
            log('Reloading original video', 'info');
            video.src = 'https://www.bluefilmx.com/media/videos/1749736915_79c6e6c523b72d5e_2025-05-18_23_11_46.mp4';
            video.load();
            updateStatus('Reloading original video...', 'info');
        }

        // Test browser video format support
        function testBrowserSupport() {
            log('=== Browser Video Format Support ===', 'info');
            
            const testVideo = document.createElement('video');
            const formats = [
                'video/mp4; codecs="avc1.42E01E"',
                'video/mp4; codecs="avc1.4D401E"', 
                'video/mp4; codecs="avc1.64001E"',
                'video/mp4',
                'video/webm; codecs="vp8"',
                'video/webm; codecs="vp9"',
                'video/webm',
                'video/ogg; codecs="theora"'
            ];
            
            formats.forEach(format => {
                const support = testVideo.canPlayType(format);
                const supportText = support === 'probably' ? 'PROBABLY' : 
                                  support === 'maybe' ? 'MAYBE' : 'NO';
                log(`${format}: ${supportText}`, support === 'probably' ? 'success' : 
                    support === 'maybe' ? 'info' : 'error');
            });
            
            log(`User Agent: ${navigator.userAgent}`, 'info');
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            log('🧪 Minimal Video Test Page Loaded', 'info');
            updateStatus('Page loaded, testing video...', 'info');
            testBrowserSupport();
            
            // Give the video a chance to load
            setTimeout(() => {
                if (video.readyState === 0) {
                    log('Video has not started loading after 5 seconds', 'error');
                    updateStatus('Video failed to start loading', 'error');
                }
            }, 5000);
        });
    </script>
</body>
</html>
