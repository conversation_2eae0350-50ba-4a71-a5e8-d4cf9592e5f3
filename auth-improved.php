<?php
/**
 * Improved Authentication API
 * Enhanced session handling and logout functionality
 */

require_once 'config/database.php';

// Enhanced session configuration
if (session_status() === PHP_SESSION_NONE) {
    // Configure session settings for better security and compatibility
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_samesite', 'Lax');
    
    // Start session with proper configuration
    session_start();
}

setCORSHeaders();

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

switch ($method) {
    case 'POST':
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'login':
                login($db, $input);
                break;
            case 'register':
                register($db, $input);
                break;
            case 'logout':
                logout();
                break;
            default:
                errorResponse('Invalid action');
        }
        break;
    
    case 'GET':
        getCurrentUserInfo($db);
        break;
    
    default:
        errorResponse('Method not allowed', 405);
}

function login($db, $input) {
    if (!isset($input['username']) || !isset($input['password'])) {
        errorResponse('Username and password are required');
    }
    
    try {
        $query = "SELECT id, username, password_hash, is_approved FROM profiles WHERE username = :username";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':username', $input['username']);
        $stmt->execute();
        $user = $stmt->fetch();
        
        if (!$user) {
            errorResponse('Invalid credentials', 401);
        }
        
        if (!password_verify($input['password'], $user['password_hash'])) {
            errorResponse('Invalid credentials', 401);
        }
        
        // Regenerate session ID for security
        session_regenerate_id(true);
        
        // Login user
        loginUser($user['id']);
        
        $userData = [
            'id' => $user['id'],
            'username' => $user['username'],
            'is_approved' => (bool)$user['is_approved']
        ];
        
        successResponse([
            'message' => 'Login successful',
            'user' => $userData
        ]);
        
    } catch (Exception $e) {
        errorResponse('Login failed: ' . $e->getMessage());
    }
}

function register($db, $input) {
    if (!isset($input['username']) || !isset($input['password'])) {
        errorResponse('Username and password are required');
    }
    
    try {
        // Check if username already exists
        $checkQuery = "SELECT id FROM profiles WHERE username = :username";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->bindParam(':username', $input['username']);
        $checkStmt->execute();
        
        if ($checkStmt->fetch()) {
            errorResponse('Username already exists');
        }
        
        // Create new user
        $userId = generateUUID();
        $passwordHash = password_hash($input['password'], PASSWORD_DEFAULT);
        
        $query = "INSERT INTO profiles (id, username, password_hash, is_approved, created_at) VALUES (:id, :username, :password_hash, false, NOW())";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $userId);
        $stmt->bindParam(':username', $input['username']);
        $stmt->bindParam(':password_hash', $passwordHash);
        
        if ($stmt->execute()) {
            // Regenerate session ID for security
            session_regenerate_id(true);
            
            // Login user immediately after registration
            loginUser($userId);
            
            $userData = [
                'id' => $userId,
                'username' => $input['username'],
                'is_approved' => false
            ];
            
            successResponse([
                'message' => 'Registration successful',
                'user' => $userData
            ]);
        } else {
            errorResponse('Registration failed');
        }
        
    } catch (Exception $e) {
        errorResponse('Registration failed: ' . $e->getMessage());
    }
}

function logout() {
    try {
        // Enhanced logout with better session cleanup
        if (session_status() === PHP_SESSION_ACTIVE) {
            // Clear all session variables
            $_SESSION = array();
            
            // Delete the session cookie
            if (ini_get("session.use_cookies")) {
                $params = session_get_cookie_params();
                setcookie(session_name(), '', time() - 42000,
                    $params["path"], $params["domain"],
                    $params["secure"], $params["httponly"]
                );
            }
            
            // Destroy the session
            session_destroy();
        }
        
        // Send success response
        successResponse(['message' => 'Logout successful']);
        
    } catch (Exception $e) {
        // Even if there's an error, send success to ensure client-side logout
        successResponse(['message' => 'Logout completed']);
    }
}

function getCurrentUserInfo($db) {
    try {
        $currentUserId = getCurrentUser();
        
        if (!$currentUserId) {
            errorResponse('Not authenticated', 401);
        }
        
        $query = "SELECT id, username, is_approved FROM profiles WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $currentUserId);
        $stmt->execute();
        $user = $stmt->fetch();
        
        if (!$user) {
            errorResponse('User not found', 401);
        }
        
        $userData = [
            'id' => $user['id'],
            'username' => $user['username'],
            'is_approved' => (bool)$user['is_approved']
        ];
        
        successResponse([
            'user' => $userData
        ]);
        
    } catch (Exception $e) {
        errorResponse('Failed to get user info: ' . $e->getMessage(), 401);
    }
}
?>