<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#3b82f6" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <base href="/" />
    <title>BlueFilmX - Premium Video Streaming</title>
    <meta name="description" content="BlueFilmX - Your ultimate destination for premium video streaming. Discover, watch, and enjoy high-quality videos across multiple categories with seamless streaming experience." />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="BlueFilmX - Premium Video Streaming" />
    <meta property="og:description" content="Your ultimate destination for premium video streaming. Discover, watch, and enjoy high-quality videos." />
    <meta property="og:image" content="/favicon.svg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="BlueFilmX - Premium Video Streaming" />
    <meta property="twitter:description" content="Your ultimate destination for premium video streaming. Discover, watch, and enjoy high-quality videos." />
    <meta property="twitter:image" content="/favicon.svg" />

    <!-- Preload critical assets -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://vsnsglgyapexhwyfylic.supabase.co" crossorigin />

    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="https://avatars.dicebear.com" />

    <!-- CSS will be loaded by Vite automatically -->
    <link rel="modulepreload" crossorigin href="/assets/react-core-B9nwsbCA-B2a2-D8-.js">
    <link rel="modulepreload" crossorigin href="/assets/state-4gBW_4Nx-D-CYgclY.js">
    <link rel="modulepreload" crossorigin href="/assets/utils-Dga8D_Ky-Bm28thii.js">
    <link rel="modulepreload" crossorigin href="/assets/router-BDggJ1ol-MCuAOmYU.js">
    <link rel="modulepreload" crossorigin href="/assets/icons-BWE0bDFO-B48Nkbxd.js">
    <link rel="modulepreload" crossorigin href="/assets/ui-components-D5L5LPe1-BFGbObMv.js">
    <script type="module" crossorigin src="/assets/main-Dmn9Lr65.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/main-BOKVtrb5.css">
  </head>
  <body>
    <div id="root"></div>
    <!-- Clear any cached service workers that might be causing issues -->
    <script src="/sw-unregister.js"></script>
  </body>
</html>
