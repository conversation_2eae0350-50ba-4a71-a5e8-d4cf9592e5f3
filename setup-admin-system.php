<?php
/**
 * Admin System Setup Script
 * Clears all existing accounts and sets up admin system
 * First two signups become admins, others require approval
 */

require_once __DIR__ . '/namecheap-api/config/database.php';

class AdminSystemSetup {
    private $db;
    
    public function __construct() {
        $database = Database::getInstance();
        $this->db = $database->getConnection();
    }
    
    public function setupAdminSystem() {
        try {
            echo "🚀 Setting up Admin System...\n\n";
            
            // Step 1: Add admin fields to profiles table
            $this->addAdminFields();
            
            // Step 2: Clear all existing accounts
            $this->clearAllAccounts();
            
            // Step 3: Reset admin counter
            $this->resetAdminCounter();
            
            echo "✅ Admin system setup completed successfully!\n\n";
            echo "📋 System Configuration:\n";
            echo "   • First 2 signups will automatically become admins\n";
            echo "   • All subsequent signups require admin approval\n";
            echo "   • Existing accounts have been cleared\n\n";
            
        } catch (Exception $e) {
            echo "❌ Error setting up admin system: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    private function addAdminFields() {
        echo "📝 Adding admin fields to database...\n";
        
        // Add is_admin field to profiles table
        try {
            $this->db->exec("ALTER TABLE profiles ADD COLUMN is_admin BOOLEAN DEFAULT FALSE");
            echo "   ✅ Added is_admin field\n";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "   ℹ️  is_admin field already exists\n";
            } else {
                throw $e;
            }
        }
        
        // Add is_approved field to profiles table
        try {
            $this->db->exec("ALTER TABLE profiles ADD COLUMN is_approved BOOLEAN DEFAULT FALSE");
            echo "   ✅ Added is_approved field\n";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "   ℹ️  is_approved field already exists\n";
            } else {
                throw $e;
            }
        }
        
        // Create admin_counter table to track admin count
        try {
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS admin_counter (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    admin_count INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "   ✅ Created admin_counter table\n";
        } catch (Exception $e) {
            echo "   ℹ️  admin_counter table already exists\n";
        }
    }
    
    private function clearAllAccounts() {
        echo "🗑️  Clearing all existing accounts...\n";
        
        // Clear all tables in correct order (respecting foreign keys)
        $tables = [
            'comment_replies',
            'comments', 
            'collection_videos',
            'collections',
            'video_tags',
            'videos',
            'session',
            'account',
            'user',
            'profiles'
        ];
        
        foreach ($tables as $table) {
            try {
                $stmt = $this->db->prepare("DELETE FROM $table");
                $stmt->execute();
                $count = $stmt->rowCount();
                echo "   ✅ Cleared $table table ($count records)\n";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), "doesn't exist") === false) {
                    echo "   ⚠️  Warning clearing $table: " . $e->getMessage() . "\n";
                }
            }
        }
    }
    
    private function resetAdminCounter() {
        echo "🔄 Resetting admin counter...\n";
        
        // Clear and reset admin counter
        $this->db->exec("DELETE FROM admin_counter");
        $this->db->exec("INSERT INTO admin_counter (admin_count) VALUES (0)");
        
        echo "   ✅ Admin counter reset to 0\n";
    }
    
    public function getAdminCount() {
        $stmt = $this->db->query("SELECT admin_count FROM admin_counter LIMIT 1");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['admin_count'] : 0;
    }
    
    public function incrementAdminCount() {
        $this->db->exec("UPDATE admin_counter SET admin_count = admin_count + 1, updated_at = CURRENT_TIMESTAMP");
    }
    
    public function showStatus() {
        echo "📊 Current System Status:\n";
        
        // Count users
        $stmt = $this->db->query("SELECT COUNT(*) as count FROM profiles");
        $userCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // Count admins
        $stmt = $this->db->query("SELECT COUNT(*) as count FROM profiles WHERE is_admin = TRUE");
        $adminCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // Count pending approvals
        $stmt = $this->db->query("SELECT COUNT(*) as count FROM profiles WHERE is_approved = FALSE AND is_admin = FALSE");
        $pendingCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // Get admin counter
        $adminCounterValue = $this->getAdminCount();
        
        echo "   👥 Total Users: $userCount\n";
        echo "   👑 Admins: $adminCount\n";
        echo "   ⏳ Pending Approval: $pendingCount\n";
        echo "   🔢 Admin Counter: $adminCounterValue\n";
    }
}

// Run the setup if called directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    try {
        $setup = new AdminSystemSetup();
        $setup->setupAdminSystem();
        $setup->showStatus();
    } catch (Exception $e) {
        echo "❌ Setup failed: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>