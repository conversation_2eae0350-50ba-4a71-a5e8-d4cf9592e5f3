<?php
/**
 * Simple File Upload API
 * Handles video and thumbnail uploads without database dependency
 */

// Set CORS headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Start session
if (!isset($_SESSION)) {
    session_start();
}

// Helper functions
function getCurrentUser() {
    return $_SESSION['user_id'] ?? null;
}

function requireAuth() {
    $userId = getCurrentUser();
    if (!$userId) {
        http_response_code(401);
        echo json_encode(['success' => false, 'error' => 'Authentication required']);
        exit();
    }
    return $userId;
}

function successResponse($data) {
    echo json_encode(['success' => true, 'data' => $data]);
    exit();
}

function errorResponse($message, $code = 400) {
    http_response_code($code);
    echo json_encode(['success' => false, 'error' => $message]);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

// For testing, allow GET requests
if ($method === 'GET') {
    echo json_encode([
        'success' => true,
        'message' => 'Upload API is working',
        'method' => $method,
        'authenticated' => !empty(getCurrentUser())
    ]);
    exit();
}

if ($method !== 'POST') {
    errorResponse('Method not allowed', 405);
}

$userId = requireAuth();

$uploadType = $_POST['type'] ?? 'video'; // 'video' or 'thumbnail'

// Configuration
$maxFileSize = 100 * 1024 * 1024; // 100MB
$allowedVideoTypes = ['mp4', 'mov', 'avi', 'mkv', 'webm'];
$allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp', 'gif'];

// Upload directories
$baseUploadDir = '../media/';
$videoDir = $baseUploadDir . 'videos/';
$thumbnailDir = $baseUploadDir . 'thumbnails/';

// Ensure directories exist
if (!is_dir($videoDir)) {
    mkdir($videoDir, 0755, true);
}
if (!is_dir($thumbnailDir)) {
    mkdir($thumbnailDir, 0755, true);
}

if (!isset($_FILES['file'])) {
    errorResponse('No file uploaded');
}

$file = $_FILES['file'];

// Check for upload errors
if ($file['error'] !== UPLOAD_ERR_OK) {
    errorResponse('File upload error: ' . $file['error']);
}

// Check file size
if ($file['size'] > $maxFileSize) {
    errorResponse('File too large. Maximum size is 100MB');
}

// Get file extension
$pathInfo = pathinfo($file['name']);
$extension = strtolower($pathInfo['extension']);

// Validate file type
if ($uploadType === 'video') {
    if (!in_array($extension, $allowedVideoTypes)) {
        errorResponse('Invalid video format. Allowed: ' . implode(', ', $allowedVideoTypes));
    }
    $uploadDir = $videoDir;
} else {
    if (!in_array($extension, $allowedImageTypes)) {
        errorResponse('Invalid image format. Allowed: ' . implode(', ', $allowedImageTypes));
    }
    $uploadDir = $thumbnailDir;
}

try {
    // Generate unique filename
    $timestamp = time();
    $randomString = bin2hex(random_bytes(8));
    $safeFilename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $pathInfo['filename']);
    $newFilename = $timestamp . '_' . $randomString . '_' . $safeFilename . '.' . $extension;
    
    $targetPath = $uploadDir . $newFilename;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
        errorResponse('Failed to save file', 500);
    }
    
    // Generate URL
    $baseUrl = 'https://www.bluefilmx.com/media/';
    $fileUrl = $baseUrl . ($uploadType === 'video' ? 'videos/' : 'thumbnails/') . $newFilename;
    
    successResponse([
        'url' => $fileUrl,
        'filename' => $newFilename,
        'size' => $file['size'],
        'type' => $uploadType
    ]);
    
} catch (Exception $e) {
    error_log("Upload error: " . $e->getMessage());
    errorResponse('Upload failed: ' . $e->getMessage(), 500);
}
?>
