# Admin System Deployment Guide

## Overview
This guide will help you deploy the new admin system that:
- Clears all existing accounts
- Makes the first 2 signups automatic admins
- Requires admin approval for all subsequent signups

## Files to Upload

You need to upload these files to your server:

### 1. Updated Authentication System
- `namecheap-api/auth-mysql.php` (updated with admin logic)
- `namecheap-api/admin-api.php` (new admin management API)

### 2. Deployment Script
- `deploy-admin-system.php` (run this on server to setup the system)

## Step-by-Step Deployment

### Step 1: Upload Files

1. **Upload the updated auth file:**
   ```bash
   # Upload to your server's api directory
   scp namecheap-api/auth-mysql.php <EMAIL>:~/public_html/api/
   ```

2. **Upload the new admin API:**
   ```bash
   # Upload to your server's api directory
   scp namecheap-api/admin-api.php <EMAIL>:~/public_html/api/
   ```

3. **Upload the deployment script:**
   ```bash
   # Upload to your server's root directory
   scp deploy-admin-system.php <EMAIL>:~/public_html/
   ```

### Step 2: Run the Deployment Script

1. **SSH into your server:**
   ```bash
   ssh <EMAIL>
   ```

2. **Navigate to the public_html directory:**
   ```bash
   cd ~/public_html
   ```

3. **Run the deployment script:**
   ```bash
   php deploy-admin-system.php
   ```

   This script will:
   - Add admin fields to the database
   - Clear all existing accounts
   - Reset the admin counter
   - Show the current system status

### Step 3: Verify the Setup

1. **Check the script output** - you should see:
   ```
   ✅ Admin system setup completed successfully!
   
   📋 System Configuration:
      • First 2 signups will automatically become admins
      • All subsequent signups require admin approval
      • All existing accounts have been cleared
   ```

2. **Test the system:**
   - Try registering a new account - it should become an admin
   - Register a second account - it should also become an admin
   - Register a third account - it should require approval

## How the New System Works

### For Users:
- **First 2 signups:** Automatically become admins with full access
- **Subsequent signups:** Account created but requires admin approval before login
- **Login attempts:** Non-approved users get "Your account is pending admin approval" message

### For Admins:
- **Admin Dashboard:** Access via `/admin` route (existing)
- **User Management:** Can approve/reject pending users
- **System Stats:** View user counts and system status

## API Endpoints

### Authentication API (`/api/auth-mysql.php`)
- `POST ?action=signup` - Register new user
- `POST ?action=signin` - Login user
- `POST ?action=signout` - Logout user
- `GET ?action=session` - Validate session

### Admin API (`/api/admin-api.php`)
- `GET ?action=pending-users` - Get users awaiting approval
- `GET ?action=stats` - Get system statistics
- `GET ?action=all-users` - Get all users
- `POST ?action=approve-user` - Approve a user
- `POST ?action=reject-user` - Reject and delete a user

## Database Changes

The deployment script adds these fields to the `profiles` table:
- `is_admin` (BOOLEAN) - Whether user is an admin
- `is_approved` (BOOLEAN) - Whether user is approved for login

It also creates an `admin_counter` table to track how many admins have been created.

## Troubleshooting

### If the deployment script fails:
1. Check database credentials in `deploy-admin-system.php`
2. Ensure you have proper database permissions
3. Check PHP error logs

### If users can't login after deployment:
- This is expected! All accounts were cleared
- Have the first 2 people register new accounts
- They will automatically become admins

### If the admin dashboard doesn't work:
- Ensure the frontend is updated to use the new user structure
- Check that the admin API endpoints are accessible

## Security Notes

- The deployment script clears ALL existing data (users, videos, comments, etc.)
- Make sure you have backups if you need to preserve any data
- The first 2 signups are critical - make sure they are trusted users
- Admin tokens are required for all admin API operations

## Next Steps

After deployment:
1. Have your 2 main administrators register accounts
2. Test the approval workflow with a third account
3. Update any frontend code that relies on user structure
4. Consider adding additional admin features as needed

---

**Important:** This deployment will clear all existing accounts and data. Make sure this is what you want before proceeding!