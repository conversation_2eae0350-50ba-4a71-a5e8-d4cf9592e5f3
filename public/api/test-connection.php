<?php
/**
 * Database Connection Test
 * Use this to test your database configuration
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html>
<head>
    <title>Database Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        .form { background: #f8f9fa; padding: 20px; border-radius: 4px; margin: 20px 0; }
        input, button { padding: 10px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔧 Database Connection Test</h1>";

if ($_POST) {
    $host = $_POST['host'] ?? 'localhost';
    $dbname = $_POST['dbname'] ?? '';
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (empty($dbname) || empty($username)) {
        echo "<p class='error'>❌ Please fill in all required fields.</p>";
    } else {
        try {
            $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo "<p class='success'>✅ Database connection successful!</p>";
            echo "<p class='info'>Connection details:</p>";
            echo "<ul>";
            echo "<li>Host: " . htmlspecialchars($host) . "</li>";
            echo "<li>Database: " . htmlspecialchars($dbname) . "</li>";
            echo "<li>Username: " . htmlspecialchars($username) . "</li>";
            echo "</ul>";
            
            echo "<p><strong>Next step:</strong> Update your <code>config.php</code> file with these settings:</p>";
            echo "<pre>";
            echo htmlspecialchars("<?php
\$DB_CONFIG = [
    'host' => '$host',
    'dbname' => '$dbname',
    'username' => '$username',
    'password' => '$password'
];
?>");
            echo "</pre>";
            
            echo "<p>Then run the <a href='setup-db.php'>database setup</a>.</p>";
            
        } catch (PDOException $e) {
            echo "<p class='error'>❌ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p>Common issues:</p>";
            echo "<ul>";
            echo "<li>Check if the database name is correct</li>";
            echo "<li>Verify the username and password</li>";
            echo "<li>Make sure the database user has proper permissions</li>";
            echo "<li>Confirm the database exists in cPanel</li>";
            echo "</ul>";
        }
    }
} else {
    echo "<p>Use this tool to test your database connection before setting up authentication.</p>";
}

echo "
        <div class='form'>
            <h2>Test Database Connection</h2>
            <form method='POST'>
                <p>
                    <label>Database Host:</label><br>
                    <input type='text' name='host' value='" . ($_POST['host'] ?? 'localhost') . "' placeholder='localhost'>
                    <small>(Usually 'localhost' on shared hosting)</small>
                </p>
                <p>
                    <label>Database Name:</label><br>
                    <input type='text' name='dbname' value='" . ($_POST['dbname'] ?? '') . "' placeholder='your_database_name' required>
                    <small>(From your cPanel MySQL Databases)</small>
                </p>
                <p>
                    <label>Database Username:</label><br>
                    <input type='text' name='username' value='" . ($_POST['username'] ?? '') . "' placeholder='your_db_username' required>
                    <small>(Database user you created)</small>
                </p>
                <p>
                    <label>Database Password:</label><br>
                    <input type='password' name='password' placeholder='your_db_password' required>
                    <small>(Password for the database user)</small>
                </p>
                <p>
                    <button type='submit'>Test Connection</button>
                </p>
            </form>
        </div>
        
        <h3>📋 Setup Instructions</h3>
        <ol>
            <li><strong>Create Database in cPanel:</strong>
                <ul>
                    <li>Go to MySQL Databases in cPanel</li>
                    <li>Create a new database (e.g., 'bluefilmx')</li>
                    <li>Create a database user with a strong password</li>
                    <li>Add the user to the database with 'All Privileges'</li>
                </ul>
            </li>
            <li><strong>Test Connection:</strong> Use the form above</li>
            <li><strong>Update Config:</strong> Copy the generated config to your config.php file</li>
            <li><strong>Run Setup:</strong> Visit <a href='setup-db.php'>setup-db.php</a> to create tables</li>
        </ol>
    </div>
</body>
</html>";
?>
