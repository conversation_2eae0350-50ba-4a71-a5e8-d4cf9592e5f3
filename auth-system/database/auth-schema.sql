-- Enhanced Authentication System Database Schema
-- This replaces the basic profiles table with a comprehensive user management system

-- Drop existing auth-related tables if they exist
DROP TABLE IF EXISTS password_reset_tokens;
DROP TABLE IF EXISTS email_verification_tokens;
DROP TABLE IF EXISTS user_sessions;
DROP TABLE IF EXISTS user_roles;
DROP TABLE IF EXISTS roles;
DROP TABLE IF EXISTS users;

-- Create roles table for role-based access control
CREATE TABLE roles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) UNIQUE NOT NULL,
  description TEXT,
  permissions JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create enhanced users table
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY,
  username VARCHAR(255) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  avatar_url TEXT,
  bio TEXT,
  date_of_birth DATE,
  phone VARCHAR(20),
  
  -- Account status fields
  is_active BOOLEAN DEFAULT TRUE,
  is_verified BOOLEAN DEFAULT FALSE,
  is_approved BOOLEAN DEFAULT FALSE,
  email_verified_at TIMESTAMP NULL,
  
  -- Security fields
  failed_login_attempts INT DEFAULT 0,
  locked_until TIMESTAMP NULL,
  last_login_at TIMESTAMP NULL,
  last_login_ip VARCHAR(45),
  
  -- Privacy settings
  profile_visibility ENUM('public', 'private', 'friends') DEFAULT 'public',
  allow_messages BOOLEAN DEFAULT TRUE,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,
  
  -- Indexes
  INDEX idx_users_username (username),
  INDEX idx_users_email (email),
  INDEX idx_users_is_active (is_active),
  INDEX idx_users_is_verified (is_verified),
  INDEX idx_users_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create user_roles junction table
CREATE TABLE user_roles (
  user_id VARCHAR(36) NOT NULL,
  role_id INT NOT NULL,
  assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  assigned_by VARCHAR(36),
  PRIMARY KEY (user_id, role_id),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create user sessions table for session management
CREATE TABLE user_sessions (
  id VARCHAR(128) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  ip_address VARCHAR(45),
  user_agent TEXT,
  device_info JSON,
  is_active BOOLEAN DEFAULT TRUE,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_sessions_user_id (user_id),
  INDEX idx_sessions_expires_at (expires_at),
  INDEX idx_sessions_is_active (is_active),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create email verification tokens table
CREATE TABLE email_verification_tokens (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  token VARCHAR(255) UNIQUE NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  used_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_verification_tokens_user_id (user_id),
  INDEX idx_verification_tokens_token (token),
  INDEX idx_verification_tokens_expires_at (expires_at),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create password reset tokens table
CREATE TABLE password_reset_tokens (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  token VARCHAR(255) UNIQUE NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  used_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_reset_tokens_user_id (user_id),
  INDEX idx_reset_tokens_token (token),
  INDEX idx_reset_tokens_expires_at (expires_at),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default roles
INSERT INTO roles (name, description, permissions) VALUES
('admin', 'System Administrator', JSON_OBJECT(
  'users', JSON_ARRAY('create', 'read', 'update', 'delete'),
  'videos', JSON_ARRAY('create', 'read', 'update', 'delete', 'moderate'),
  'comments', JSON_ARRAY('create', 'read', 'update', 'delete', 'moderate'),
  'system', JSON_ARRAY('manage_settings', 'view_analytics')
)),
('moderator', 'Content Moderator', JSON_OBJECT(
  'videos', JSON_ARRAY('read', 'update', 'moderate'),
  'comments', JSON_ARRAY('read', 'update', 'delete', 'moderate'),
  'users', JSON_ARRAY('read', 'update')
)),
('creator', 'Content Creator', JSON_OBJECT(
  'videos', JSON_ARRAY('create', 'read', 'update', 'delete'),
  'comments', JSON_ARRAY('create', 'read', 'update', 'delete'),
  'collections', JSON_ARRAY('create', 'read', 'update', 'delete')
)),
('user', 'Regular User', JSON_OBJECT(
  'videos', JSON_ARRAY('read'),
  'comments', JSON_ARRAY('create', 'read', 'update', 'delete'),
  'profile', JSON_ARRAY('read', 'update')
));

-- Create a default admin user (change password immediately after setup)
SET @admin_id = UUID();
INSERT INTO users (id, username, email, password_hash, is_active, is_verified, is_approved) VALUES
(@admin_id, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', TRUE, TRUE, TRUE);

-- Assign admin role to default admin user
INSERT INTO user_roles (user_id, role_id) VALUES
(@admin_id, (SELECT id FROM roles WHERE name = 'admin'));

COMMIT;