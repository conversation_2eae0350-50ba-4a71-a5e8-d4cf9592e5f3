<?php
/**
 * Videos API Endpoint
 * Handles video CRUD operations
 */

// Enable CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once 'config.php';

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];
$pathParts = explode('/', trim(parse_url($path, PHP_URL_PATH), '/'));

// Extract video ID if present
$videoId = null;
if (count($pathParts) > 2 && $pathParts[2] !== '') {
    $videoId = $pathParts[2];
}

// Connect to database
try {
    $pdo = new PDO($dsn, $username, $password, $options);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit();
}

// Route requests
switch ($method) {
    case 'GET':
        if ($videoId) {
            getVideo($pdo, $videoId);
        } else {
            getVideos($pdo);
        }
        break;
    
    case 'POST':
        createVideo($pdo);
        break;
    
    case 'PUT':
        if ($videoId) {
            updateVideo($pdo, $videoId);
        } else {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Video ID required for update']);
        }
        break;
    
    case 'DELETE':
        if ($videoId) {
            deleteVideo($pdo, $videoId);
        } else {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Video ID required for delete']);
        }
        break;
    
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
}

/**
 * Get videos with pagination and filtering
 */
function getVideos($pdo) {
    try {
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
        $category = isset($_GET['category']) ? $_GET['category'] : null;
        $search = isset($_GET['search']) ? $_GET['search'] : null;

        // Load real video data from migration export
        $allVideos = loadVideoData();

        // Filter by category if specified
        $filteredVideos = $allVideos;
        if ($category && $category !== 'all') {
            $filteredVideos = array_filter($allVideos, function($video) use ($category) {
                return $video['category'] === $category;
            });
        }

        // Filter by search if specified
        if ($search) {
            $filteredVideos = array_filter($filteredVideos, function($video) use ($search) {
                return stripos($video['title'], $search) !== false ||
                       stripos($video['description'], $search) !== false ||
                       stripos($video['tags'], $search) !== false;
            });
        }

        $total = count($filteredVideos);
        $offset = ($page - 1) * $limit;
        $videos = array_slice($filteredVideos, $offset, $limit);

        // Convert boolean and numeric fields
        foreach ($videos as &$video) {
            $video['is_hd'] = (bool)$video['is_hd'];
            $video['views'] = (int)$video['views'];
            $video['likes'] = (int)$video['likes'];
            $video['duration'] = (int)$video['duration'];
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'videos' => array_values($videos),
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);

    } catch (Exception $e) {
        error_log("Error fetching videos: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to fetch videos']);
    }
}

/**
 * Get single video by ID
 */
function getVideo($pdo, $videoId) {
    try {
        // Mock video data (same as in getVideos)
        $allVideos = [
            [
                'id' => '1',
                'title' => 'Hot Summer Night Passion',
                'description' => 'Watch this steamy video featuring an intimate encounter on a hot summer night. High definition quality guaranteed to provide the ultimate viewing experience.',
                'thumbnail_url' => 'https://images.pexels.com/photos/3771824/pexels-photo-3771824.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
                'video_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                'duration' => 842,
                'views' => 1245789,
                'likes' => 24680,
                'is_hd' => true,
                'category' => 'hot',
                'tags' => 'amateur, couple, summer',
                'user_id' => '1',
                'created_at' => '2025-05-12T00:00:00Z',
                'updated_at' => '2025-05-12T00:00:00Z',
                'username' => 'JessicaDreams',
                'user_avatar' => 'https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=150'
            ],
            [
                'id' => '2',
                'title' => 'Midnight Desires Unleashed',
                'description' => 'Experience the raw passion and desire in this captivating midnight encounter. Premium content with crystal clear HD quality.',
                'thumbnail_url' => 'https://images.pexels.com/photos/3771824/pexels-photo-3771824.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
                'video_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
                'duration' => 1256,
                'views' => 987654,
                'likes' => 18945,
                'is_hd' => true,
                'category' => 'trending',
                'tags' => 'passionate, midnight, premium',
                'user_id' => '2',
                'created_at' => '2025-05-10T00:00:00Z',
                'updated_at' => '2025-05-10T00:00:00Z',
                'username' => 'RyanHot',
                'user_avatar' => 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=150'
            ],
            [
                'id' => '3',
                'title' => 'Cosplay Fantasy Adventure',
                'description' => 'Dive into a world of fantasy and roleplay with this amazing cosplay adventure. High quality production with stunning visuals.',
                'thumbnail_url' => 'https://images.pexels.com/photos/3771824/pexels-photo-3771824.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
                'video_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
                'duration' => 1845,
                'views' => 756432,
                'likes' => 15678,
                'is_hd' => true,
                'category' => 'new',
                'tags' => 'cosplay, fantasy, roleplay',
                'user_id' => '3',
                'created_at' => '2025-05-08T00:00:00Z',
                'updated_at' => '2025-05-08T00:00:00Z',
                'username' => 'CosplayQueen',
                'user_avatar' => 'https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=150'
            ]
        ];

        // Find video by ID
        $video = null;
        foreach ($allVideos as $v) {
            if ($v['id'] === $videoId) {
                $video = $v;
                break;
            }
        }

        if (!$video) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Video not found']);
            return;
        }

        // Convert boolean and numeric fields
        $video['is_hd'] = (bool)$video['is_hd'];
        $video['views'] = (int)$video['views'];
        $video['likes'] = (int)$video['likes'];
        $video['duration'] = (int)$video['duration'];

        echo json_encode([
            'success' => true,
            'data' => $video
        ]);

    } catch (Exception $e) {
        error_log("Error fetching video: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to fetch video']);
    }
}

/**
 * Create new video (placeholder)
 */
function createVideo($pdo) {
    http_response_code(501);
    echo json_encode(['success' => false, 'error' => 'Video creation not implemented yet']);
}

/**
 * Update video (placeholder)
 */
function updateVideo($pdo, $videoId) {
    http_response_code(501);
    echo json_encode(['success' => false, 'error' => 'Video update not implemented yet']);
}

/**
 * Delete video (placeholder)
 */
function deleteVideo($pdo, $videoId) {
    http_response_code(501);
    echo json_encode(['success' => false, 'error' => 'Video deletion not implemented yet']);
}
?>
