<?php
/**
 * Authentication System Integration Script
 * This script integrates the new auth system with the existing application
 */

class AuthIntegration {
    private $projectRoot;
    
    public function __construct() {
        $this->projectRoot = __DIR__;
    }
    
    public function backupOldFiles() {
        echo "=== Backing Up Old Authentication Files ===\n";
        
        $backup_dir = $this->projectRoot . '/auth-backup-' . date('Y-m-d-H-i-s');
        if (!mkdir($backup_dir, 0755, true)) {
            echo "❌ Failed to create backup directory\n";
            return false;
        }
        
        $files_to_backup = [
            'namecheap-api/auth.php',
            'namecheap-api/config/database.php',
            'src/stores/authStore.ts',
            'src/lib/api.ts',
            'src/components/auth/AuthModal.tsx'
        ];
        
        foreach ($files_to_backup as $file) {
            $source = $this->projectRoot . '/' . $file;
            if (file_exists($source)) {
                $dest = $backup_dir . '/' . basename($file);
                if (copy($source, $dest)) {
                    echo "✅ Backed up: $file\n";
                } else {
                    echo "⚠️  Failed to backup: $file\n";
                }
            }
        }
        
        echo "📁 Backup created in: $backup_dir\n";
        return true;
    }
    
    public function copyNewAuthFiles() {
        echo "\n=== Copying New Authentication Files ===\n";
        
        $file_mappings = [
            // Backend files
            'auth-system/backend/api/auth.php' => 'namecheap-api/auth.php',
            'auth-system/backend/config/Database.php' => 'namecheap-api/config/database.php',
            
            // Frontend files
            'auth-system/frontend/stores/authStore.ts' => 'src/stores/authStore.ts',
            'auth-system/frontend/lib/api.ts' => 'src/lib/api.ts',
            'auth-system/frontend/components/AuthModal.tsx' => 'src/components/auth/AuthModal.tsx'
        ];
        
        foreach ($file_mappings as $source => $dest) {
            $source_path = $this->projectRoot . '/' . $source;
            $dest_path = $this->projectRoot . '/' . $dest;
            
            if (!file_exists($source_path)) {
                echo "⚠️  Source file not found: $source\n";
                continue;
            }
            
            // Create destination directory if it doesn't exist
            $dest_dir = dirname($dest_path);
            if (!is_dir($dest_dir)) {
                mkdir($dest_dir, 0755, true);
            }
            
            if (copy($source_path, $dest_path)) {
                echo "✅ Copied: $source -> $dest\n";
            } else {
                echo "❌ Failed to copy: $source\n";
            }
        }
        
        return true;
    }
    
    public function copyUtilityFiles() {
        echo "\n=== Copying Utility Files ===\n";
        
        $utility_files = [
            'auth-system/backend/utils/JWTHelper.php' => 'namecheap-api/utils/JWTHelper.php',
            'auth-system/backend/utils/RateLimiter.php' => 'namecheap-api/utils/RateLimiter.php',
            'auth-system/backend/utils/SecurityHelper.php' => 'namecheap-api/utils/SecurityHelper.php',
            'auth-system/backend/utils/EmailService.php' => 'namecheap-api/utils/EmailService.php',
            'auth-system/backend/services/AuthService.php' => 'namecheap-api/services/AuthService.php'
        ];
        
        foreach ($utility_files as $source => $dest) {
            $source_path = $this->projectRoot . '/' . $source;
            $dest_path = $this->projectRoot . '/' . $dest;
            
            if (!file_exists($source_path)) {
                echo "⚠️  Source file not found: $source\n";
                continue;
            }
            
            // Create destination directory if it doesn't exist
            $dest_dir = dirname($dest_path);
            if (!is_dir($dest_dir)) {
                mkdir($dest_dir, 0755, true);
            }
            
            if (copy($source_path, $dest_path)) {
                echo "✅ Copied utility: $source -> $dest\n";
            } else {
                echo "❌ Failed to copy utility: $source\n";
            }
        }
        
        return true;
    }
    
    public function updateImportPaths() {
        echo "\n=== Updating Import Paths ===\n";
        
        // Update frontend import paths
        $frontend_files = [
            'src/stores/authStore.ts',
            'src/lib/api.ts',
            'src/components/auth/AuthModal.tsx'
        ];
        
        foreach ($frontend_files as $file) {
            $file_path = $this->projectRoot . '/' . $file;
            if (file_exists($file_path)) {
                $content = file_get_contents($file_path);
                
                // Update any auth-system paths to relative paths
                $content = str_replace(
                    ['./auth-system/frontend/', '../auth-system/frontend/'],
                    ['./src/', '../'],
                    $content
                );
                
                file_put_contents($file_path, $content);
                echo "✅ Updated imports in: $file\n";
            }
        }
        
        return true;
    }
    
    public function createLogDirectories() {
        echo "\n=== Creating Log Directories ===\n";
        
        $log_dirs = [
            'namecheap-api/logs',
            'logs'
        ];
        
        foreach ($log_dirs as $dir) {
            $dir_path = $this->projectRoot . '/' . $dir;
            if (!is_dir($dir_path)) {
                if (mkdir($dir_path, 0755, true)) {
                    echo "✅ Created log directory: $dir\n";
                } else {
                    echo "❌ Failed to create log directory: $dir\n";
                }
            } else {
                echo "ℹ️  Log directory already exists: $dir\n";
            }
        }
        
        return true;
    }
    
    public function updatePackageJson() {
        echo "\n=== Updating Package Dependencies ===\n";
        
        $package_json_path = $this->projectRoot . '/package.json';
        if (!file_exists($package_json_path)) {
            echo "⚠️  package.json not found\n";
            return false;
        }
        
        $package_data = json_decode(file_get_contents($package_json_path), true);
        
        // Add required dependencies if not present
        $required_deps = [
            'zustand' => '^4.4.0',
            'lucide-react' => '^0.263.0'
        ];
        
        $added = [];
        foreach ($required_deps as $dep => $version) {
            if (!isset($package_data['dependencies'][$dep])) {
                $package_data['dependencies'][$dep] = $version;
                $added[] = $dep;
            }
        }
        
        if (!empty($added)) {
            file_put_contents($package_json_path, json_encode($package_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
            echo "✅ Added dependencies: " . implode(', ', $added) . "\n";
            echo "ℹ️  Run 'npm install' to install new dependencies\n";
        } else {
            echo "ℹ️  All required dependencies already present\n";
        }
        
        return true;
    }
    
    public function generateIntegrationGuide() {
        echo "\n=== Generating Integration Guide ===\n";
        
        $guide_content = "# Authentication System Integration Guide\n\n";
        $guide_content .= "## Files Updated\n\n";
        $guide_content .= "### Backend Files\n";
        $guide_content .= "- `namecheap-api/auth.php` - New authentication API endpoint\n";
        $guide_content .= "- `namecheap-api/config/database.php` - Enhanced database configuration\n";
        $guide_content .= "- `namecheap-api/utils/` - New utility classes\n";
        $guide_content .= "- `namecheap-api/services/` - Authentication service\n\n";
        
        $guide_content .= "### Frontend Files\n";
        $guide_content .= "- `src/stores/authStore.ts` - Enhanced authentication store\n";
        $guide_content .= "- `src/lib/api.ts` - Updated API client\n";
        $guide_content .= "- `src/components/auth/AuthModal.tsx` - New authentication modal\n\n";
        
        $guide_content .= "## Next Steps\n\n";
        $guide_content .= "1. **Install Dependencies**: Run `npm install` to install new packages\n";
        $guide_content .= "2. **Database Setup**: Run `php setup-auth-system.php` to set up the database\n";
        $guide_content .= "3. **Environment**: Update `.env` file with proper email configuration\n";
        $guide_content .= "4. **Testing**: Test all authentication flows\n";
        $guide_content .= "5. **Frontend Integration**: Update components to use new auth store\n\n";
        
        $guide_content .= "## API Changes\n\n";
        $guide_content .= "The new authentication system uses JWT tokens instead of sessions.\n";
        $guide_content .= "Update your API calls to include the Authorization header:\n\n";
        $guide_content .= "```javascript\n";
        $guide_content .= "headers: {\n";
        $guide_content .= "  'Authorization': `Bearer \${token}`,\n";
        $guide_content .= "  'Content-Type': 'application/json'\n";
        $guide_content .= "}\n";
        $guide_content .= "```\n\n";
        
        $guide_content .= "## Security Features\n\n";
        $guide_content .= "- JWT token authentication\n";
        $guide_content .= "- Rate limiting\n";
        $guide_content .= "- Password strength validation\n";
        $guide_content .= "- Email verification\n";
        $guide_content .= "- CSRF protection\n";
        $guide_content .= "- Security logging\n";
        
        $guide_path = $this->projectRoot . '/AUTH_INTEGRATION_GUIDE.md';
        file_put_contents($guide_path, $guide_content);
        
        echo "✅ Integration guide created: AUTH_INTEGRATION_GUIDE.md\n";
        return true;
    }
    
    public function run() {
        echo "BlueFilmX Authentication System Integration\n";
        echo "=========================================\n";
        
        $steps = [
            'backupOldFiles',
            'copyNewAuthFiles',
            'copyUtilityFiles',
            'updateImportPaths',
            'createLogDirectories',
            'updatePackageJson',
            'generateIntegrationGuide'
        ];
        
        foreach ($steps as $step) {
            if (!$this->$step()) {
                echo "\n❌ Integration failed at step: $step\n";
                return false;
            }
        }
        
        echo "\n🎉 Authentication system integration completed!\n";
        echo "\nNext steps:\n";
        echo "1. Run: npm install\n";
        echo "2. Run: php setup-auth-system.php\n";
        echo "3. Test the authentication system\n";
        echo "4. Read AUTH_INTEGRATION_GUIDE.md for details\n";
        
        return true;
    }
}

// Run integration if called directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $integration = new AuthIntegration();
    $integration->run();
}
?>