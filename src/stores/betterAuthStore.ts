/**
 * BetterAuth Store
 * Modern authentication store using BetterAuth
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authClient, type User, type Session } from '../lib/auth-client';

interface BetterAuthState {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isInitialized: boolean;
  
  // Actions
  initialize: () => Promise<void>;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string, name?: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  updateUser: (data: Partial<User>) => Promise<{ success: boolean; error?: string }>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<{ success: boolean; error?: string }>;
  resetPassword: (email: string) => Promise<{ success: boolean; error?: string }>;
  sendVerificationEmail: (email: string) => Promise<{ success: boolean; error?: string }>;
  verifyEmail: (token: string) => Promise<{ success: boolean; error?: string }>;
}

export const useBetterAuthStore = create<BetterAuthState>()(persist(
  (set, get) => ({
    user: null,
    session: null,
    isLoading: false,
    isInitialized: false,

    initialize: async () => {
      set({ isLoading: true });
      try {
        const { data: session, error } = await authClient.getSession();
        if (session && !error) {
          set({ 
            user: session.user, 
            session,
            isLoading: false,
            isInitialized: true 
          });
        } else {
          set({ 
            user: null, 
            session: null,
            isLoading: false,
            isInitialized: true 
          });
        }
      } catch (error) {
        console.error('Failed to initialize auth:', error);
        set({ 
          user: null, 
          session: null,
          isLoading: false,
          isInitialized: true 
        });
      }
    },

    signIn: async (email: string, password: string) => {
      set({ isLoading: true });
      try {
        const result = await authClient.signIn.email({ email, password });
        if (result.data && !result.error) {
          set({ 
            user: result.data.user, 
            session: { ...result.data.session, user: result.data.user },
            isLoading: false 
          });
          return { success: true };
        } else {
          set({ isLoading: false });
          return { success: false, error: result.error || 'Sign in failed' };
        }
      } catch (error) {
        set({ isLoading: false });
        return { success: false, error: 'An unexpected error occurred' };
      }
    },

    signUp: async (email: string, password: string, name?: string) => {
      set({ isLoading: true });
      try {
        const result = await authClient.signUp.email({ email, password, name });
        if (result.data && !result.error) {
          set({ 
            user: result.data.user, 
            session: { ...result.data.session, user: result.data.user },
            isLoading: false 
          });
          return { success: true };
        } else {
          set({ isLoading: false });
          return { success: false, error: result.error || 'Sign up failed' };
        }
      } catch (error) {
        set({ isLoading: false });
        return { success: false, error: 'An unexpected error occurred' };
      }
    },

    signOut: async () => {
      set({ isLoading: true });
      try {
        await authClient.signOut();
        set({ 
          user: null, 
          session: null,
          isLoading: false 
        });
      } catch (error) {
        console.error('Sign out error:', error);
        // Clear state anyway
        set({ 
          user: null, 
          session: null,
          isLoading: false 
        });
      }
    },

    updateUser: async (data: Partial<User>) => {
      set({ isLoading: true });
      try {
        const result = await authClient.updateUser(data);
        if (result.data && !result.error) {
          set(state => ({ 
            user: { ...state.user!, ...result.data },
            isLoading: false 
          }));
          return { success: true };
        } else {
          set({ isLoading: false });
          return { success: false, error: result.error || 'Update failed' };
        }
      } catch (error) {
        set({ isLoading: false });
        return { success: false, error: 'An unexpected error occurred' };
      }
    },

    changePassword: async (currentPassword: string, newPassword: string) => {
      set({ isLoading: true });
      try {
        const result = await authClient.changePassword({ currentPassword, newPassword });
        if (!result.error) {
          set({ isLoading: false });
          return { success: true };
        } else {
          set({ isLoading: false });
          return { success: false, error: result.error || 'Password change failed' };
        }
      } catch (error) {
        set({ isLoading: false });
        return { success: false, error: 'An unexpected error occurred' };
      }
    },

    resetPassword: async (email: string) => {
      set({ isLoading: true });
      try {
        const result = await authClient.resetPassword({ email });
        if (!result.error) {
          set({ isLoading: false });
          return { success: true };
        } else {
          set({ isLoading: false });
          return { success: false, error: result.error || 'Password reset failed' };
        }
      } catch (error) {
        set({ isLoading: false });
        return { success: false, error: 'An unexpected error occurred' };
      }
    },

    sendVerificationEmail: async (email: string) => {
      set({ isLoading: true });
      try {
        const result = await authClient.sendVerificationEmail({ email });
        if (!result.error) {
          set({ isLoading: false });
          return { success: true };
        } else {
          set({ isLoading: false });
          return { success: false, error: result.error || 'Failed to send verification email' };
        }
      } catch (error) {
        set({ isLoading: false });
        return { success: false, error: 'An unexpected error occurred' };
      }
    },

    verifyEmail: async (token: string) => {
      set({ isLoading: true });
      try {
        const result = await authClient.verifyEmail({ token });
        if (!result.error) {
          set({ isLoading: false });
          return { success: true };
        } else {
          set({ isLoading: false });
          return { success: false, error: result.error || 'Email verification failed' };
        }
      } catch (error) {
        set({ isLoading: false });
        return { success: false, error: 'An unexpected error occurred' };
      }
    },
  }),
  {
    name: 'better-auth-storage',
    partialize: (state) => ({ 
      user: state.user, 
      session: state.session,
      isInitialized: state.isInitialized 
    }),
  }
));