-- BlueFilmX MySQL Database Schema
-- Converted from Supabase PostgreSQL schema

-- Create database (run this separately if needed)
-- CREATE DATABASE bluefilmx_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE bluefilmx_db;

-- Drop tables if they exist (for clean migration)
DROP TABLE IF EXISTS comment_replies;
DROP TABLE IF EXISTS comments;
DROP TABLE IF EXISTS collection_videos;
DROP TABLE IF EXISTS collections;
DROP TABLE IF EXISTS video_tags;
DROP TABLE IF EXISTS videos;
DROP TABLE IF EXISTS categories;
DROP TABLE IF EXISTS profiles;

-- Create profiles table
CREATE TABLE profiles (
  id VARCHAR(36) PRIMARY KEY,
  username VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255),
  avatar_url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_profiles_username (username)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create categories table
CREATE TABLE categories (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_categories_slug (slug)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create videos table
CREATE TABLE videos (
  id VARCHAR(36) PRIMARY KEY,
  title VARCHAR(500) NOT NULL,
  description TEXT,
  thumbnail_url TEXT,
  video_url TEXT NOT NULL,
  duration INT DEFAULT 0,
  views INT DEFAULT 0,
  likes INT DEFAULT 0,
  is_hd BOOLEAN DEFAULT FALSE,
  category VARCHAR(255) DEFAULT 'uncategorized',
  user_id VARCHAR(36) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_videos_user_id (user_id),
  INDEX idx_videos_category (category),
  INDEX idx_videos_created_at (created_at),
  INDEX idx_videos_views (views),
  INDEX idx_videos_likes (likes),
  FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create collections table
CREATE TABLE collections (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  thumbnail_url TEXT,
  user_id VARCHAR(36) NOT NULL,
  is_public BOOLEAN DEFAULT TRUE,
  video_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_collections_user_id (user_id),
  INDEX idx_collections_is_public (is_public),
  FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create collection_videos junction table
CREATE TABLE collection_videos (
  collection_id VARCHAR(36) NOT NULL,
  video_id VARCHAR(36) NOT NULL,
  position INT DEFAULT 0,
  added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (collection_id, video_id),
  INDEX idx_collection_videos_collection_id (collection_id),
  INDEX idx_collection_videos_video_id (video_id),
  FOREIGN KEY (collection_id) REFERENCES collections(id) ON DELETE CASCADE,
  FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create video_tags table
CREATE TABLE video_tags (
  id VARCHAR(36) PRIMARY KEY,
  video_id VARCHAR(36) NOT NULL,
  tag VARCHAR(255) NOT NULL,
  INDEX idx_video_tags_video_id (video_id),
  INDEX idx_video_tags_tag (tag),
  FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create comments table
CREATE TABLE comments (
  id VARCHAR(36) PRIMARY KEY,
  video_id VARCHAR(36) NOT NULL,
  user_id VARCHAR(36) NOT NULL,
  user_name VARCHAR(255),
  user_avatar TEXT,
  content TEXT NOT NULL,
  likes INT DEFAULT 0,
  is_liked BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_comments_video_id (video_id),
  INDEX idx_comments_user_id (user_id),
  INDEX idx_comments_created_at (created_at),
  FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create comment_replies table
CREATE TABLE comment_replies (
  id VARCHAR(36) PRIMARY KEY,
  comment_id VARCHAR(36) NOT NULL,
  user_id VARCHAR(36) NOT NULL,
  user_name VARCHAR(255),
  user_avatar TEXT,
  content TEXT NOT NULL,
  likes INT DEFAULT 0,
  is_liked BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_comment_replies_comment_id (comment_id),
  INDEX idx_comment_replies_user_id (user_id),
  INDEX idx_comment_replies_created_at (created_at),
  FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default categories
INSERT INTO categories (id, name, slug, description) VALUES
(UUID(), 'Hot', 'hot', 'Popular and trending videos'),
(UUID(), 'Trending', 'trending', 'Videos gaining popularity right now'),
(UUID(), 'New', 'new', 'Recently uploaded videos');

-- Create a simple user for testing (you can remove this later)
INSERT INTO profiles (id, username, avatar_url) VALUES
(UUID(), 'admin', NULL);

COMMIT;
