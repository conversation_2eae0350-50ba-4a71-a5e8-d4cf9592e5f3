<?php
/**
 * Web-based Password Hash Fixer
 * Access this file through your browser to fix authentication issues
 */

// Only allow access from specific IPs or with a secret key for security
$secret = $_GET['secret'] ?? '';
if ($secret !== 'fix-auth-2024') {
    die('Access denied. Use: ?secret=fix-auth-2024');
}

require_once 'api/config/database.php';

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>BlueFilmX - Fix Authentication</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
        .user-info { background: #e9ecef; padding: 15px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 BlueFilmX Authentication Fixer</h1>
        
        <?php
        try {
            $database = new Database();
            $db = $database->getConnection();
            
            echo '<div class="success">✅ Database connection successful!</div>';
            
            // Define default passwords for existing users
            $userPasswords = [
                'username' => 'password123',
                'jayliinight' => 'jaylii123'
            ];
            
            // Check current state
            echo '<h2>📊 Current Database State</h2>';
            $query = "SELECT id, username, password_hash, is_approved FROM profiles ORDER BY created_at";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $allUsers = $stmt->fetchAll();
            
            echo '<div class="info">Found ' . count($allUsers) . ' total users in database:</div>';
            
            foreach ($allUsers as $user) {
                $hasPassword = !empty($user['password_hash']) ? '✅ Has password' : '❌ No password';
                $isApproved = $user['is_approved'] ? '✅ Approved' : '❌ Not approved';
                echo '<div class="user-info">';
                echo '<strong>' . htmlspecialchars($user['username']) . '</strong><br>';
                echo 'ID: ' . htmlspecialchars($user['id']) . '<br>';
                echo 'Password: ' . $hasPassword . '<br>';
                echo 'Status: ' . $isApproved;
                echo '</div>';
            }
            
            // Fix users without password hashes
            echo '<h2>🔧 Fixing Password Hashes</h2>';
            
            $usersWithoutPasswords = array_filter($allUsers, function($user) {
                return empty($user['password_hash']);
            });
            
            if (empty($usersWithoutPasswords)) {
                echo '<div class="success">✅ All users already have password hashes!</div>';
            } else {
                echo '<div class="info">Fixing ' . count($usersWithoutPasswords) . ' users without password hashes...</div>';
                
                foreach ($usersWithoutPasswords as $user) {
                    $username = $user['username'];
                    $password = $userPasswords[$username] ?? null;
                    
                    if (!$password) {
                        echo '<div class="error">⚠️ No default password defined for "' . htmlspecialchars($username) . '", skipping...</div>';
                        continue;
                    }
                    
                    // Generate password hash
                    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                    
                    // Update user with password hash
                    $updateQuery = "UPDATE profiles SET password_hash = :password_hash WHERE id = :id";
                    $updateStmt = $db->prepare($updateQuery);
                    $updateStmt->bindParam(':password_hash', $passwordHash);
                    $updateStmt->bindParam(':id', $user['id']);
                    
                    if ($updateStmt->execute()) {
                        echo '<div class="success">✅ Updated password hash for "' . htmlspecialchars($username) . '"</div>';
                    } else {
                        echo '<div class="error">❌ Failed to update password hash for "' . htmlspecialchars($username) . '"</div>';
                    }
                }
            }
            
            // Show login credentials
            echo '<h2>🔑 Login Credentials</h2>';
            echo '<div class="info">You can now login with these credentials:</div>';
            
            foreach ($userPasswords as $username => $password) {
                echo '<div class="user-info">';
                echo '<strong>Username:</strong> ' . htmlspecialchars($username) . '<br>';
                echo '<strong>Password:</strong> ' . htmlspecialchars($password);
                echo '</div>';
            }
            
            // Test authentication
            echo '<h2>🧪 Test Authentication</h2>';
            echo '<div class="info">Testing login for first user...</div>';
            
            $testUsername = 'username';
            $testPassword = 'password123';
            
            $testQuery = "SELECT id, username, password_hash, is_approved FROM profiles WHERE username = :username";
            $testStmt = $db->prepare($testQuery);
            $testStmt->bindParam(':username', $testUsername);
            $testStmt->execute();
            $testUser = $testStmt->fetch();
            
            if ($testUser && password_verify($testPassword, $testUser['password_hash'])) {
                echo '<div class="success">✅ Authentication test PASSED! Login should work now.</div>';
            } else {
                echo '<div class="error">❌ Authentication test FAILED! There may still be an issue.</div>';
            }
            
            echo '<h2>🚀 Next Steps</h2>';
            echo '<div class="info">';
            echo '1. Try logging in to your website with the credentials above<br>';
            echo '2. If login works, the 401 error should be resolved<br>';
            echo '3. Consider changing the default passwords after logging in<br>';
            echo '4. Delete this file for security: <code>fix-auth-web.php</code>';
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            echo '<div class="info">Make sure your database credentials are correct in api/config/database.php</div>';
        }
        ?>
        
        <hr>
        <p><small>🔒 This tool should be deleted after use for security reasons.</small></p>
    </div>
</body>
</html>