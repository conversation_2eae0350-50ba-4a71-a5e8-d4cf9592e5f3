import{r as e,R as t}from"./react-core-B9nwsbCA.js";
/**
 * @remix-run/router v1.15.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function n(){return n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},n.apply(this,arguments)}var r;!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(r||(r={}));const a="popstate";function o(e){return void 0===e&&(e={}),function(e,t,o,i){void 0===i&&(i={});let{window:h=document.defaultView,v5Compat:p=!1}=i,d=h.history,f=r.Pop,m=null,v=g();null==v&&(v=0,d.replaceState(n({},d.state,{idx:v}),""));function g(){return(d.state||{idx:null}).idx}function y(){f=r.Pop;let e=g(),t=null==e?null:e-v;v=e,m&&m({action:f,location:x.location,delta:t})}function b(e,t){f=r.Push;let n=u(x.location,e,t);v=g()+1;let a=s(n,v),o=x.createHref(n);try{d.pushState(a,"",o)}catch(l){if(l instanceof DOMException&&"DataCloneError"===l.name)throw l;h.location.assign(o)}p&&m&&m({action:f,location:x.location,delta:1})}function w(e,t){f=r.Replace;let n=u(x.location,e,t);v=g();let a=s(n,v),o=x.createHref(n);d.replaceState(a,"",o),p&&m&&m({action:f,location:x.location,delta:0})}function E(e){let t="null"!==h.location.origin?h.location.origin:h.location.href,n="string"==typeof e?e:c(e);return n=n.replace(/ $/,"%20"),l(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}let x={get action(){return f},get location(){return e(h,d)},listen(e){if(m)throw new Error("A history only accepts one active listener");return h.addEventListener(a,y),m=e,()=>{h.removeEventListener(a,y),m=null}},createHref:e=>t(h,e),createURL:E,encodeLocation(e){let t=E(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:b,replace:w,go:e=>d.go(e)};return x}((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return u("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:c(t)}),0,e)}function l(e,t){if(!1===e||null==e)throw new Error(t)}function i(e,t){if(!e)try{throw new Error(t)}catch(n){}}function s(e,t){return{usr:e.state,key:e.key,idx:t}}function u(e,t,r,a){return void 0===r&&(r=null),n({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?h(t):t,{state:r,key:t&&t.key||a||Math.random().toString(36).substr(2,8)})}function c(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function h(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}var p;function d(e,t,n){void 0===n&&(n="/");let r=U(("string"==typeof t?h(t):t).pathname||"/",n);if(null==r)return null;let a=f(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(a);let o=null;for(let l=0;null==o&&l<a.length;++l){let e=P(r);o=R(a[l],e)}return o}function f(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,o)=>{let i={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(l(i.relativePath.startsWith(r),'Absolute route path "'+i.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(r.length));let s=B([r,i.relativePath]),u=n.concat(i);e.children&&e.children.length>0&&(l(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),f(e.children,t,u,s)),(null!=e.path||e.index)&&t.push({path:s,score:C(s,e.index),routesMeta:u})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of m(e.path))a(e,t,r);else a(e,t)})),t}function m(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let l=m(r.join("/")),i=[];return i.push(...l.map((e=>""===e?o:[o,e].join("/")))),a&&i.push(...l),i.map((t=>e.startsWith("/")&&""===t?"/":t))}!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(p||(p={}));const v=/^:[\w-]+$/,g=3,y=2,b=1,w=10,E=-2,x=e=>"*"===e;function C(e,t){let n=e.split("/"),r=n.length;return n.some(x)&&(r+=E),t&&(r+=y),n.filter((e=>!x(e))).reduce(((e,t)=>e+(v.test(t)?g:""===t?b:w)),r)}function R(e,t){let{routesMeta:n}=e,r={},a="/",o=[];for(let l=0;l<n.length;++l){let e=n[l],i=l===n.length-1,s="/"===a?t:t.slice(a.length)||"/",u=S({path:e.relativePath,caseSensitive:e.caseSensitive,end:i},s);if(!u)return null;Object.assign(r,u.params);let c=e.route;o.push({params:r,pathname:B([a,u.pathname]),pathnameBase:j(B([a,u.pathnameBase])),route:c}),"/"!==u.pathnameBase&&(a=B([a,u.pathnameBase]))}return o}function S(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);i("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let o=new RegExp(a,t?void 0:"i");return[o,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],l=o.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:r.reduce(((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=s[n]||"";l=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const i=s[n];return e[r]=a&&!i?void 0:(i||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:l,pattern:e}}function P(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return i(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function U(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function O(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function k(e,t){let n=function(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}(e);return t?n.map(((t,n)=>n===e.length-1?t.pathname:t.pathnameBase)):n.map((e=>e.pathnameBase))}function L(e,t,r,a){let o;void 0===a&&(a=!1),"string"==typeof e?o=h(e):(o=n({},e),l(!o.pathname||!o.pathname.includes("?"),O("?","pathname","search",o)),l(!o.pathname||!o.pathname.includes("#"),O("#","pathname","hash",o)),l(!o.search||!o.search.includes("#"),O("#","search","hash",o)));let i,s=""===e||""===o.pathname,u=s?"/":o.pathname;if(null==u)i=r;else{let e=t.length-1;if(!a&&u.startsWith("..")){let t=u.split("/");for(;".."===t[0];)t.shift(),e-=1;o.pathname=t.join("/")}i=e>=0?t[e]:"/"}let c=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"==typeof e?h(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:D(r),hash:N(a)}}(o,i),p=u&&"/"!==u&&u.endsWith("/"),d=(s||"."===u)&&r.endsWith("/");return c.pathname.endsWith("/")||!p&&!d||(c.pathname+="/"),c}const B=e=>e.join("/").replace(/\/\/+/g,"/"),j=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),D=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",N=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";const T=["post","put","patch","delete"];new Set(T);const W=["get",...T];
/**
 * React Router v6.22.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function $(){return $=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$.apply(this,arguments)}new Set(W);const F=e.createContext(null),A=e.createContext(null),_=e.createContext(null),M=e.createContext(null),I=e.createContext({outlet:null,matches:[],isDataRoute:!1}),H=e.createContext(null);function J(){return null!=e.useContext(M)}function z(){return J()||l(!1),e.useContext(M).location}function V(t){e.useContext(_).static||e.useLayoutEffect(t)}function K(){let{isDataRoute:t}=e.useContext(I);return t?function(){let{router:t}=function(){let t=e.useContext(F);return t||l(!1),t}(te.UseNavigateStable),n=re(ne.UseNavigateStable),r=e.useRef(!1);return V((()=>{r.current=!0})),e.useCallback((function(e,a){void 0===a&&(a={}),r.current&&("number"==typeof e?t.navigate(e):t.navigate(e,$({fromRouteId:n},a)))}),[t,n])}():function(){J()||l(!1);let t=e.useContext(F),{basename:n,future:r,navigator:a}=e.useContext(_),{matches:o}=e.useContext(I),{pathname:i}=z(),s=JSON.stringify(k(o,r.v7_relativeSplatPath)),u=e.useRef(!1);return V((()=>{u.current=!0})),e.useCallback((function(e,r){if(void 0===r&&(r={}),!u.current)return;if("number"==typeof e)return void a.go(e);let o=L(e,JSON.parse(s),i,"path"===r.relative);null==t&&"/"!==n&&(o.pathname="/"===o.pathname?n:B([n,o.pathname])),(r.replace?a.replace:a.push)(o,r.state,r)}),[n,a,s,i,t])}()}function q(){let{matches:t}=e.useContext(I),n=t[t.length-1];return n?n.params:{}}function G(t,n){let{relative:r}=void 0===n?{}:n,{future:a}=e.useContext(_),{matches:o}=e.useContext(I),{pathname:l}=z(),i=JSON.stringify(k(o,a.v7_relativeSplatPath));return e.useMemo((()=>L(t,JSON.parse(i),l,"path"===r)),[t,i,l,r])}function Q(t,n){return function(t,n,a,o){J()||l(!1);let{navigator:i}=e.useContext(_),{matches:s}=e.useContext(I),u=s[s.length-1],c=u?u.params:{};!u||u.pathname;let p=u?u.pathnameBase:"/";u&&u.route;let f,m=z();if(n){var v;let e="string"==typeof n?h(n):n;"/"===p||(null==(v=e.pathname)?void 0:v.startsWith(p))||l(!1),f=e}else f=m;let g=f.pathname||"/",y=g;if("/"!==p){let e=p.replace(/^\//,"").split("/");y="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let b=d(t,{pathname:y}),w=function(t,n,r,a){var o,i;void 0===n&&(n=[]);void 0===r&&(r=null);void 0===a&&(a=null);if(null==t){if(null==(i=r)||!i.errors)return null;t=r.matches}let s=t,u=null==(o=r)?void 0:o.errors;if(null!=u){let e=s.findIndex((e=>e.route.id&&(null==u?void 0:u[e.route.id])));e>=0||l(!1),s=s.slice(0,Math.min(s.length,e+1))}let c=!1,h=-1;if(r&&a&&a.v7_partialHydration)for(let e=0;e<s.length;e++){let t=s[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(h=e),t.route.id){let{loaderData:e,errors:n}=r,a=t.route.loader&&void 0===e[t.route.id]&&(!n||void 0===n[t.route.id]);if(t.route.lazy||a){c=!0,s=h>=0?s.slice(0,h+1):[s[0]];break}}}return s.reduceRight(((t,a,o)=>{let l,i=!1,p=null,d=null;r&&(l=u&&a.route.id?u[a.route.id]:void 0,p=a.route.errorElement||Y,c&&(h<0&&0===o?(i=!0,d=null):h===o&&(i=!0,d=a.route.hydrateFallbackElement||null)));let f=n.concat(s.slice(0,o+1)),m=()=>{let n;return n=l?p:i?d:a.route.Component?e.createElement(a.route.Component,null):a.route.element?a.route.element:t,e.createElement(ee,{match:a,routeContext:{outlet:t,matches:f,isDataRoute:null!=r},children:n})};return r&&(a.route.ErrorBoundary||a.route.errorElement||0===o)?e.createElement(Z,{location:r.location,revalidation:r.revalidation,component:p,error:l,children:m(),routeContext:{outlet:null,matches:f,isDataRoute:!0}}):m()}),null)}(b&&b.map((e=>Object.assign({},e,{params:Object.assign({},c,e.params),pathname:B([p,i.encodeLocation?i.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?p:B([p,i.encodeLocation?i.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),s,a,o);if(n&&w)return e.createElement(M.Provider,{value:{location:$({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:r.Pop}},w);return w}(t,n)}function X(){let t=function(){var t;let n=e.useContext(H),r=function(){let t=e.useContext(A);return t||l(!1),t}(ne.UseRouteError),a=re(ne.UseRouteError);if(void 0!==n)return n;return null==(t=r.errors)?void 0:t[a]}(),n=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),r=t instanceof Error?t.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return e.createElement(e.Fragment,null,e.createElement("h2",null,"Unexpected Application Error!"),e.createElement("h3",{style:{fontStyle:"italic"}},n),r?e.createElement("pre",{style:a},r):null,null)}const Y=e.createElement(X,null);class Z extends e.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){}render(){return void 0!==this.state.error?e.createElement(I.Provider,{value:this.props.routeContext},e.createElement(H.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ee(t){let{routeContext:n,match:r,children:a}=t,o=e.useContext(F);return o&&o.static&&o.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=r.route.id),e.createElement(I.Provider,{value:n},a)}var te=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(te||{}),ne=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ne||{});function re(t){let n=function(){let t=e.useContext(I);return t||l(!1),t}(),r=n.matches[n.matches.length-1];return r.route.id||l(!1),r.route.id}function ae(e){l(!1)}function oe(t){let{basename:n="/",children:a=null,location:o,navigationType:i=r.Pop,navigator:s,static:u=!1,future:c}=t;J()&&l(!1);let p=n.replace(/^\/*/,"/"),d=e.useMemo((()=>({basename:p,navigator:s,static:u,future:$({v7_relativeSplatPath:!1},c)})),[p,c,s,u]);"string"==typeof o&&(o=h(o));let{pathname:f="/",search:m="",hash:v="",state:g=null,key:y="default"}=o,b=e.useMemo((()=>{let e=U(f,p);return null==e?null:{location:{pathname:e,search:m,hash:v,state:g,key:y},navigationType:i}}),[p,f,m,v,g,y,i]);return null==b?null:e.createElement(_.Provider,{value:d},e.createElement(M.Provider,{children:a,value:b}))}function le(e){let{children:t,location:n}=e;return Q(ie(t),n)}function ie(t,n){void 0===n&&(n=[]);let r=[];return e.Children.forEach(t,((t,a)=>{if(!e.isValidElement(t))return;let o=[...n,a];if(t.type===e.Fragment)return void r.push.apply(r,ie(t.props.children,o));t.type!==ae&&l(!1),t.props.index&&t.props.children&&l(!1);let i={id:t.props.id||o.join("-"),caseSensitive:t.props.caseSensitive,element:t.props.element,Component:t.props.Component,index:t.props.index,path:t.props.path,loader:t.props.loader,action:t.props.action,errorElement:t.props.errorElement,ErrorBoundary:t.props.ErrorBoundary,hasErrorBoundary:null!=t.props.ErrorBoundary||null!=t.props.errorElement,shouldRevalidate:t.props.shouldRevalidate,handle:t.props.handle,lazy:t.props.lazy};t.props.children&&(i.children=ie(t.props.children,o)),r.push(i)})),r}
/**
 * React Router DOM v6.22.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function se(){return se=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},se.apply(this,arguments)}function ue(e){return void 0===e&&(e=""),new URLSearchParams("string"==typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce(((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map((e=>[n,e])):[[n,r]])}),[]))}new Promise((()=>{}));const ce=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","unstable_viewTransition"];try{window.__reactRouterVersion="6"}catch(Ee){}const he=t.startTransition;function pe(t){let{basename:n,children:r,future:a,window:l}=t,i=e.useRef();null==i.current&&(i.current=o({window:l,v5Compat:!0}));let s=i.current,[u,c]=e.useState({action:s.action,location:s.location}),{v7_startTransition:h}=a||{},p=e.useCallback((e=>{h&&he?he((()=>c(e))):c(e)}),[c,h]);return e.useLayoutEffect((()=>s.listen(p)),[s,p]),e.createElement(oe,{basename:n,children:r,location:u.location,navigationType:u.action,navigator:s,future:a})}const de="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,fe=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,me=e.forwardRef((function(t,n){let r,{onClick:a,relative:o,reloadDocument:i,replace:s,state:u,target:h,to:p,preventScrollReset:d,unstable_viewTransition:f}=t,m=function(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(t,ce),{basename:v}=e.useContext(_),g=!1;if("string"==typeof p&&fe.test(p)&&(r=p,de))try{let e=new URL(window.location.href),t=p.startsWith("//")?new URL(e.protocol+p):new URL(p),n=U(t.pathname,v);t.origin===e.origin&&null!=n?p=n+t.search+t.hash:g=!0}catch(Ee){}let y=function(t,n){let{relative:r}=void 0===n?{}:n;J()||l(!1);let{basename:a,navigator:o}=e.useContext(_),{hash:i,pathname:s,search:u}=G(t,{relative:r}),c=s;return"/"!==a&&(c="/"===s?a:B([a,s])),o.createHref({pathname:c,search:u,hash:i})}(p,{relative:o}),b=function(t,n){let{target:r,replace:a,state:o,preventScrollReset:l,relative:i,unstable_viewTransition:s}=void 0===n?{}:n,u=K(),h=z(),p=G(t,{relative:i});return e.useCallback((e=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(e,r)){e.preventDefault();let n=void 0!==a?a:c(h)===c(p);u(t,{replace:n,state:o,preventScrollReset:l,relative:i,unstable_viewTransition:s})}}),[h,u,p,a,o,r,t,l,i,s])}(p,{replace:s,state:u,target:h,preventScrollReset:d,relative:o,unstable_viewTransition:f});return e.createElement("a",se({},m,{href:r||y,onClick:g||i?a:function(e){a&&a(e),e.defaultPrevented||b(e)},ref:n,target:h}))}));var ve,ge,ye,be;function we(t){let n=e.useRef(ue(t)),r=e.useRef(!1),a=z(),o=e.useMemo((()=>function(e,t){let n=ue(e);return t&&t.forEach(((e,r)=>{n.has(r)||t.getAll(r).forEach((e=>{n.append(r,e)}))})),n}(a.search,r.current?null:n.current)),[a.search]),l=K(),i=e.useCallback(((e,t)=>{const n=ue("function"==typeof e?e(o):e);r.current=!0,l("?"+n,t)}),[l,o]);return[o,i]}(ge=ve||(ve={})).UseScrollRestoration="useScrollRestoration",ge.UseSubmit="useSubmit",ge.UseSubmitFetcher="useSubmitFetcher",ge.UseFetcher="useFetcher",ge.useViewTransitionState="useViewTransitionState",(be=ye||(ye={})).UseFetcher="useFetcher",be.UseFetchers="useFetchers",be.UseScrollRestoration="useScrollRestoration";export{pe as B,me as L,le as R,ae as a,z as b,we as c,q as d,K as u};
