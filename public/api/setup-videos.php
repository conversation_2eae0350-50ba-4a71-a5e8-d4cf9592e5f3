<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Videos Table - BlueFilmX</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            color: #28a745;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            color: #0c5460;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .danger {
            background-color: #dc3545;
        }
        .danger:hover {
            background-color: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Setup Videos Table</h1>
        <p>This will create the videos table and optionally add sample data for testing.</p>

        <?php
        require_once 'config.php';

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $pdo = new PDO($dsn, $username, $password, $options);
                
                if (isset($_POST['create_table'])) {
                    echo "<h2>Creating Videos Table...</h2>";
                    
                    $createVideosTable = "
                        CREATE TABLE IF NOT EXISTS videos (
                            id VARCHAR(255) PRIMARY KEY,
                            title VARCHAR(500) NOT NULL,
                            description TEXT,
                            thumbnail_url TEXT,
                            video_url TEXT NOT NULL,
                            duration INT DEFAULT 0,
                            views INT DEFAULT 0,
                            likes INT DEFAULT 0,
                            is_hd BOOLEAN DEFAULT FALSE,
                            category VARCHAR(100),
                            tags TEXT,
                            user_id VARCHAR(255),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            INDEX idx_category (category),
                            INDEX idx_user_id (user_id),
                            INDEX idx_created_at (created_at),
                            INDEX idx_views (views)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ";

                    $pdo->exec($createVideosTable);
                    echo "<p class='success'>✅ Videos table created successfully.</p>";
                }
                
                if (isset($_POST['add_sample_data'])) {
                    echo "<h2>Adding Sample Videos...</h2>";
                    
                    $sampleVideos = [
                        [
                            'id' => 'video_' . uniqid(),
                            'title' => 'Sample Video 1',
                            'description' => 'This is a sample video for testing purposes.',
                            'thumbnail_url' => 'https://placehold.co/400x225/blue/white?text=Video+1',
                            'video_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                            'duration' => 596,
                            'views' => 1250,
                            'likes' => 45,
                            'is_hd' => true,
                            'category' => 'trending',
                            'tags' => 'sample, test, demo',
                            'user_id' => 'user_sample1'
                        ],
                        [
                            'id' => 'video_' . uniqid(),
                            'title' => 'Sample Video 2',
                            'description' => 'Another sample video with different content.',
                            'thumbnail_url' => 'https://placehold.co/400x225/green/white?text=Video+2',
                            'video_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
                            'duration' => 653,
                            'views' => 890,
                            'likes' => 32,
                            'is_hd' => true,
                            'category' => 'hot',
                            'tags' => 'sample, test, demo',
                            'user_id' => 'user_sample2'
                        ],
                        [
                            'id' => 'video_' . uniqid(),
                            'title' => 'Sample Video 3',
                            'description' => 'Third sample video for comprehensive testing.',
                            'thumbnail_url' => 'https://placehold.co/400x225/red/white?text=Video+3',
                            'video_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
                            'duration' => 15,
                            'views' => 2100,
                            'likes' => 78,
                            'is_hd' => false,
                            'category' => 'new',
                            'tags' => 'sample, test, demo',
                            'user_id' => 'user_sample3'
                        ]
                    ];
                    
                    $insertQuery = "INSERT INTO videos (id, title, description, thumbnail_url, video_url, duration, views, likes, is_hd, category, tags, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    $stmt = $pdo->prepare($insertQuery);
                    
                    foreach ($sampleVideos as $video) {
                        $stmt->execute([
                            $video['id'],
                            $video['title'],
                            $video['description'],
                            $video['thumbnail_url'],
                            $video['video_url'],
                            $video['duration'],
                            $video['views'],
                            $video['likes'],
                            $video['is_hd'],
                            $video['category'],
                            $video['tags'],
                            $video['user_id']
                        ]);
                    }
                    
                    echo "<p class='success'>✅ Sample videos added successfully.</p>";
                }
                
                if (isset($_POST['clear_videos'])) {
                    echo "<h2>Clearing Videos...</h2>";
                    $pdo->exec("DELETE FROM videos");
                    echo "<p class='success'>✅ All videos cleared.</p>";
                }
                
            } catch (PDOException $e) {
                echo "<p class='error'>❌ Database Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
        ?>

        <div class="info">
            <strong>Note:</strong> This will create a videos table with sample data for testing. 
            The sample videos use publicly available test videos from Google.
        </div>

        <form method="POST">
            <button type="submit" name="create_table">Create Videos Table</button>
            <button type="submit" name="add_sample_data">Add Sample Videos</button>
            <button type="submit" name="clear_videos" class="danger" onclick="return confirm('Are you sure you want to delete all videos?')">Clear All Videos</button>
        </form>

        <h3>Current Videos Count</h3>
        <?php
        try {
            $pdo = new PDO($dsn, $username, $password, $options);
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos");
            $result = $stmt->fetch();
            echo "<p>Total videos in database: <strong>" . $result['count'] . "</strong></p>";
        } catch (Exception $e) {
            echo "<p class='error'>Could not count videos: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        ?>

        <p><a href="/api/test.php">← Back to API Test</a></p>
    </div>
</body>
</html>
