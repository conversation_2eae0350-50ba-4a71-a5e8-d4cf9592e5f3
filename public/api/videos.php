<?php
/**
 * Videos API Endpoint
 * Handles video CRUD operations
 */

// Enable CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once 'config.php';

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];
$pathParts = explode('/', trim(parse_url($path, PHP_URL_PATH), '/'));

// Extract video ID if present
$videoId = null;
if (count($pathParts) > 2 && $pathParts[2] !== '') {
    $videoId = $pathParts[2];
}

// Connect to database
try {
    $pdo = new PDO($dsn, $username, $password, $options);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit();
}

// Route requests
switch ($method) {
    case 'GET':
        if ($videoId) {
            getVideo($pdo, $videoId);
        } else {
            getVideos($pdo);
        }
        break;
    
    case 'POST':
        createVideo($pdo);
        break;
    
    case 'PUT':
        if ($videoId) {
            updateVideo($pdo, $videoId);
        } else {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Video ID required for update']);
        }
        break;
    
    case 'DELETE':
        if ($videoId) {
            deleteVideo($pdo, $videoId);
        } else {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Video ID required for delete']);
        }
        break;
    
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
}

/**
 * Get videos with pagination and filtering
 */
function getVideos($pdo) {
    try {
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
        $category = isset($_GET['category']) ? $_GET['category'] : null;
        $search = isset($_GET['search']) ? $_GET['search'] : null;
        $sortBy = isset($_GET['sort']) ? $_GET['sort'] : 'created_at';
        $order = isset($_GET['order']) ? $_GET['order'] : 'DESC';

        $offset = ($page - 1) * $limit;
        
        // Build query
        $whereConditions = [];
        $params = [];
        
        if ($category) {
            $whereConditions[] = "v.category = :category";
            $params[':category'] = $category;
        }
        
        if ($search) {
            $whereConditions[] = "(v.title LIKE :search OR v.description LIKE :search OR v.tags LIKE :search)";
            $params[':search'] = "%$search%";
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // Count total videos
        $countQuery = "SELECT COUNT(*) FROM videos v $whereClause";
        $countStmt = $pdo->prepare($countQuery);
        $countStmt->execute($params);
        $total = $countStmt->fetchColumn();
        
        // Get videos with user info
        $query = "
            SELECT 
                v.id,
                v.title,
                v.description,
                v.thumbnail_url,
                v.video_url,
                v.duration,
                v.views,
                v.likes,
                v.is_hd,
                v.category,
                v.tags,
                v.user_id,
                v.created_at,
                v.updated_at,
                'Unknown User' as username,
                NULL as user_avatar
            FROM videos v
            $whereClause
            ORDER BY v.$sortBy $order
            LIMIT :limit OFFSET :offset
        ";
        
        $stmt = $pdo->prepare($query);
        
        // Bind parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

        $stmt->execute();
        $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Convert boolean and numeric fields
        foreach ($videos as &$video) {
            $video['is_hd'] = (bool)$video['is_hd'];
            $video['views'] = (int)$video['views'];
            $video['likes'] = (int)$video['likes'];
            $video['duration'] = (int)$video['duration'];
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'videos' => $videos,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => (int)$total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Error fetching videos: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to fetch videos']);
    }
}

/**
 * Get single video by ID
 */
function getVideo($pdo, $videoId) {
    try {
        // Increment view count
        $updateQuery = "UPDATE videos SET views = views + 1 WHERE id = :id";
        $updateStmt = $pdo->prepare($updateQuery);
        $updateStmt->bindParam(':id', $videoId);
        $updateStmt->execute();
        
        // Get video
        $query = "
            SELECT 
                v.*,
                'Unknown User' as username,
                NULL as user_avatar
            FROM videos v
            WHERE v.id = :id
        ";
        
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':id', $videoId);
        $stmt->execute();
        
        $video = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$video) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Video not found']);
            return;
        }
        
        // Convert boolean and numeric fields
        $video['is_hd'] = (bool)$video['is_hd'];
        $video['views'] = (int)$video['views'];
        $video['likes'] = (int)$video['likes'];
        $video['duration'] = (int)$video['duration'];
        
        echo json_encode([
            'success' => true,
            'data' => $video
        ]);
        
    } catch (Exception $e) {
        error_log("Error fetching video: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to fetch video']);
    }
}

/**
 * Create new video (placeholder)
 */
function createVideo($pdo) {
    http_response_code(501);
    echo json_encode(['success' => false, 'error' => 'Video creation not implemented yet']);
}

/**
 * Update video (placeholder)
 */
function updateVideo($pdo, $videoId) {
    http_response_code(501);
    echo json_encode(['success' => false, 'error' => 'Video update not implemented yet']);
}

/**
 * Delete video (placeholder)
 */
function deleteVideo($pdo, $videoId) {
    http_response_code(501);
    echo json_encode(['success' => false, 'error' => 'Video deletion not implemented yet']);
}
?>
