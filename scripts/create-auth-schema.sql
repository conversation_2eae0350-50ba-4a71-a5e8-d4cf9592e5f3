-- MySQL Authentication Schema for cPanel Deployment
-- Based on auth-server.js table structures

-- Users table
CREATE TABLE IF NOT EXISTS user (
    id VARCHAR(255) PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    emailVerified <PERSON>OOLEAN NOT NULL DEFAULT FALSE,
    name VA<PERSON>HA<PERSON>(255),
    createdAt BIGINT NOT NULL,
    updatedAt BIGINT NOT NULL,
    image TEXT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Accounts table for OAuth providers and credentials
CREATE TABLE IF NOT EXISTS account (
    id VARCHAR(255) PRIMARY KEY,
    accountId VARCHAR(255) NOT NULL,
    providerId VARCHAR(255) NOT NULL,
    userId VARCHAR(255) NOT NULL,
    accessToken TEXT,
    refreshToken TEXT,
    idToken TEXT,
    accessTokenExpiresAt BIGINT,
    refreshTokenExpiresAt BIGINT,
    scope TEXT,
    password VARCHAR(255),
    createdAt BIGINT NOT NULL,
    updatedAt BIGINT NOT NULL,
    FOREIG<PERSON> KEY (userId) REFERENCES user (id) ON DELETE CASCADE,
    INDEX idx_user_id (userId),
    INDEX idx_account_id (accountId),
    INDEX idx_provider_id (providerId)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sessions table
CREATE TABLE IF NOT EXISTS session (
    id VARCHAR(255) PRIMARY KEY,
    expiresAt BIGINT NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    createdAt BIGINT NOT NULL,
    updatedAt BIGINT NOT NULL,
    ipAddress VARCHAR(45),
    userAgent TEXT,
    userId VARCHAR(255) NOT NULL,
    FOREIGN KEY (userId) REFERENCES user (id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_user_id (userId),
    INDEX idx_expires_at (expiresAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Verification table for email verification
CREATE TABLE IF NOT EXISTS verification (
    id VARCHAR(255) PRIMARY KEY,
    identifier VARCHAR(255) NOT NULL,
    value VARCHAR(255) NOT NULL,
    expiresAt BIGINT NOT NULL,
    createdAt BIGINT,
    updatedAt BIGINT,
    INDEX idx_identifier (identifier),
    INDEX idx_value (value),
    INDEX idx_expires_at (expiresAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default admin user (optional)
-- Password: admin123 (hashed)
INSERT IGNORE INTO user (id, email, emailVerified, name, createdAt, updatedAt) 
VALUES (
    'admin-user-id-12345',
    '<EMAIL>',
    1,
    'Administrator',
    UNIX_TIMESTAMP() * 1000,
    UNIX_TIMESTAMP() * 1000
);

INSERT IGNORE INTO account (id, accountId, providerId, userId, password, createdAt, updatedAt) 
VALUES (
    'admin-account-id-12345',
    '<EMAIL>',
    'credential',
    'admin-user-id-12345',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- admin123
    UNIX_TIMESTAMP() * 1000,
    UNIX_TIMESTAMP() * 1000
);

-- Clean up expired sessions (run this periodically)
-- DELETE FROM session WHERE expiresAt < UNIX_TIMESTAMP() * 1000;

-- Show table status
SELECT 'Authentication tables created successfully' as status;
SHOW TABLES LIKE '%user%';
SHOW TABLES LIKE '%account%';
SHOW TABLES LIKE '%session%';
SHOW TABLES LIKE '%verification%';