<?php
/**
 * Simple Logout Test
 */

require_once 'namecheap-api/config/database.php';
require_once 'auth-system/backend/AuthService.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

try {
    $authService = new AuthService();
    
    // Test logout with empty session
    $result = $authService->logout('');
    
    echo json_encode([
        'test' => 'logout_functionality',
        'result' => $result,
        'session_before' => $_SESSION ?? [],
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'test' => 'logout_functionality',
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>