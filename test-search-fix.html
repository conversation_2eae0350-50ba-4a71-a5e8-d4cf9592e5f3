<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #2a2a2a;
        }
        .success {
            border-color: #4ade80;
            background-color: #1a2e1a;
        }
        .error {
            border-color: #ef4444;
            background-color: #2e1a1a;
        }
        .loading {
            border-color: #fbbf24;
            background-color: #2e2a1a;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
        input {
            background-color: #374151;
            color: white;
            border: 1px solid #6b7280;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
        pre {
            background-color: #111;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔍 BlueFilmX Search Fix Test</h1>
    <p>This page tests the search functionality to verify the "getVideoQuery is not defined" error is fixed.</p>

    <div class="test-section">
        <h3>Test 1: Direct API Search</h3>
        <input type="text" id="searchInput" placeholder="Enter search term" value="sugar">
        <button onclick="testDirectAPISearch()">Test API Search</button>
        <div id="api-search-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Frontend Search Simulation</h3>
        <button onclick="testFrontendSearch()">Test Frontend Search Logic</button>
        <div id="frontend-search-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Check for getVideoQuery Function</h3>
        <button onclick="testGetVideoQuery()">Check getVideoQuery</button>
        <div id="getvideo-result"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://www.bluefilmx.com/api';

        function setResult(elementId, message, type = 'loading') {
            const element = document.getElementById(elementId);
            element.className = `test-section ${type}`;
            element.innerHTML = message;
        }

        async function testDirectAPISearch() {
            const searchTerm = document.getElementById('searchInput').value;
            setResult('api-search-result', `Testing API search for: "${searchTerm}"...`, 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/videos.php?search=${encodeURIComponent(searchTerm)}&limit=5`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                setResult('api-search-result', `
                    <strong>✅ API Search Success!</strong><br>
                    Status: ${response.status}<br>
                    Results found: ${data.data?.videos?.length || 0}<br>
                    Total matches: ${data.data?.pagination?.total || 0}<br>
                    <pre>${JSON.stringify(data, null, 2).substring(0, 500)}...</pre>
                `, 'success');

            } catch (error) {
                setResult('api-search-result', `
                    <strong>❌ API Search Error:</strong><br>
                    ${error.message}<br>
                    <small>Check browser console for more details</small>
                `, 'error');
                console.error('API Search Error:', error);
            }
        }

        async function testFrontendSearch() {
            setResult('frontend-search-result', 'Testing frontend search logic...', 'loading');
            
            try {
                // Simulate the frontend search logic
                const searchTerm = document.getElementById('searchInput').value;
                
                // Test if we can create the API client structure
                const apiClient = {
                    getVideos: async (params) => {
                        const searchParams = new URLSearchParams();
                        if (params.search) searchParams.append('search', params.search);
                        if (params.limit) searchParams.append('limit', params.limit.toString());
                        
                        const response = await fetch(`${API_BASE_URL}/videos.php?${searchParams.toString()}`, {
                            method: 'GET',
                            credentials: 'include',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        
                        return response.json();
                    }
                };

                const result = await apiClient.getVideos({
                    search: searchTerm,
                    limit: 5
                });

                setResult('frontend-search-result', `
                    <strong>✅ Frontend Search Logic Success!</strong><br>
                    Search term: "${searchTerm}"<br>
                    Results: ${result.data?.videos?.length || 0}<br>
                    API client working correctly<br>
                    <small>Frontend search logic is functional</small>
                `, 'success');

            } catch (error) {
                setResult('frontend-search-result', `
                    <strong>❌ Frontend Search Error:</strong><br>
                    ${error.message}<br>
                    <small>Frontend search logic has issues</small>
                `, 'error');
                console.error('Frontend Search Error:', error);
            }
        }

        function testGetVideoQuery() {
            setResult('getvideo-result', 'Checking for getVideoQuery function...', 'loading');
            
            try {
                // Check if getVideoQuery is defined in the global scope
                if (typeof getVideoQuery !== 'undefined') {
                    setResult('getvideo-result', `
                        <strong>✅ getVideoQuery Found!</strong><br>
                        Type: ${typeof getVideoQuery}<br>
                        Function is available in global scope<br>
                        <small>This suggests the function is properly loaded</small>
                    `, 'success');
                } else {
                    setResult('getvideo-result', `
                        <strong>ℹ️ getVideoQuery Not in Global Scope</strong><br>
                        This is expected for the new MySQL API implementation<br>
                        The function should be imported from modules, not global<br>
                        <small>This is actually the correct behavior</small>
                    `, 'success');
                }

                // Check if we can access it through window
                if (window.getVideoQuery) {
                    setResult('getvideo-result', `
                        <strong>⚠️ getVideoQuery Found on Window!</strong><br>
                        This might be from old cached JavaScript<br>
                        <small>Consider clearing browser cache</small>
                    `, 'error');
                }

            } catch (error) {
                setResult('getvideo-result', `
                    <strong>❌ Error checking getVideoQuery:</strong><br>
                    ${error.message}
                `, 'error');
                console.error('getVideoQuery Check Error:', error);
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            console.log('🧪 Search Fix Test Page Loaded');
            console.log('Current URL:', window.location.href);
            console.log('User Agent:', navigator.userAgent);
            
            // Auto-test getVideoQuery
            testGetVideoQuery();
        });
    </script>
</body>
</html>
