/**
 * API Client for BlueFilmX MySQL Backend
 * Replaces Supabase client functionality
 */

// API Configuration
const API_BASE_URL = '/api';

// Types
export interface Video {
  id: string;
  title: string;
  description?: string;
  thumbnail_url?: string;
  video_url: string;
  duration: number;
  views: number;
  likes: number;
  is_hd: boolean;
  category: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  is_part_of_multi_upload?: boolean;
  is_first_in_multi_upload?: boolean;
  total_parts_in_multi_upload?: number;
  tags?: string;
  username?: string;
  user_avatar?: string;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: string;
  username: string;
  avatar_url?: string;
  is_approved: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: {
    videos?: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
  success: boolean;
}

// API Client Class
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;

    try {
      const defaultOptions: RequestInit = {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        credentials: 'include', // Include cookies for session-based auth
      };

      console.log(`🌐 API Request: ${options.method || 'GET'} ${url}`);

      const response = await fetch(url, { ...defaultOptions, ...options });

      console.log(`📡 API Response: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ API Error Response:`, errorText);
        throw new Error(`API Error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log(`✅ API Success:`, data);
      return data;

    } catch (error) {
      console.error(`❌ Network/API Error for ${url}:`, error);

      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Network error during upload. Please check your internet connection and try again.');
      }

      throw error;
    }
  }

  // Videos API
  async getVideos(params: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
    sort?: string;
    order?: string;
  } = {}): Promise<PaginatedResponse<Video>> {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.category && params.category !== 'all') searchParams.append('category', params.category);
    if (params.search) searchParams.append('search', params.search);
    if (params.sort) searchParams.append('sort', params.sort);
    if (params.order) searchParams.append('order', params.order);

    const endpoint = `/videos.php?${searchParams.toString()}`;
    return this.request<PaginatedResponse<Video>>(endpoint);
  }

  async getVideo(id: string): Promise<ApiResponse<Video>> {
    return this.request<ApiResponse<Video>>(`/videos.php/${id}`);
  }

  async createVideo(videoData: {
    title: string;
    description?: string;
    video_url: string;
    thumbnail_url?: string;
    category?: string;
    duration?: number;
  }): Promise<ApiResponse<{ id: string; message: string }>> {
    return this.request<ApiResponse<{ id: string; message: string }>>('/videos.php', {
      method: 'POST',
      body: JSON.stringify(videoData),
    });
  }

  async updateVideo(id: string, videoData: Partial<Video>): Promise<ApiResponse<{ message: string }>> {
    return this.request<ApiResponse<{ message: string }>>(`/videos.php/${id}`, {
      method: 'PUT',
      body: JSON.stringify(videoData),
    });
  }

  async deleteVideo(id: string): Promise<ApiResponse<{ message: string }>> {
    return this.request<ApiResponse<{ message: string }>>(`/videos.php/${id}`, {
      method: 'DELETE',
    });
  }

  // Categories API
  async getCategories(): Promise<ApiResponse<Category[]>> {
    return this.request<ApiResponse<Category[]>>('/categories.php');
  }

  // Authentication API
  async login(username: string, password: string): Promise<ApiResponse<{ user: User; message: string }>> {
    return this.request<ApiResponse<{ user: User; message: string }>>('/auth.php?action=login', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });
  }

  async register(username: string, password: string): Promise<ApiResponse<{ user: User; message: string }>> {
    return this.request<ApiResponse<{ user: User; message: string }>>('/auth.php?action=register', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });
  }

  async logout(): Promise<ApiResponse<{ message: string }>> {
    return this.request<ApiResponse<{ message: string }>>('/auth.php?action=logout', {
      method: 'POST',
    });
  }

  async getCurrentUser(): Promise<ApiResponse<{ user: User }>> {
    try {
      return await this.request<ApiResponse<{ user: User }>>('/auth.php');
    } catch (error) {
      // For auth endpoint, 401 errors are expected when no user is logged in
      // Re-throw with a more specific message to help identify this case
      if (error instanceof Error && error.message.includes('401')) {
        throw new Error('Not authenticated');
      }
      throw error;
    }
  }

  // Favorites API
  async getFavorites(): Promise<ApiResponse<{ videos: Video[]; count: number }>> {
    return this.request<ApiResponse<{ videos: Video[]; count: number }>>('/favorites.php');
  }

  async addToFavorites(videoId: string): Promise<ApiResponse<{ message: string }>> {
    return this.request<ApiResponse<{ message: string }>>('/favorites.php', {
      method: 'POST',
      body: JSON.stringify({ video_id: videoId }),
    });
  }

  async removeFromFavorites(videoId: string): Promise<ApiResponse<{ message: string }>> {
    return this.request<ApiResponse<{ message: string }>>('/favorites.php', {
      method: 'DELETE',
      body: JSON.stringify({ video_id: videoId }),
    });
  }

  // File Upload API
  async uploadFile(file: File, type: 'video' | 'thumbnail'): Promise<ApiResponse<{
    url: string;
    filename: string;
    size: number;
    type: string;
  }>> {
    try {
      console.log(`🔄 Starting ${type} upload:`, {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type
      });

      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);

      const response = await this.request<ApiResponse<{
        url: string;
        filename: string;
        size: number;
        type: string;
      }>>('/upload.php', {
        method: 'POST',
        body: formData,
        headers: {}, // Remove Content-Type to let browser set it for FormData
      });

      console.log(`✅ ${type} upload response:`, response);
      return response;

    } catch (error) {
      console.error(`❌ ${type} upload failed:`, error);
      throw error;
    }
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Helper functions for backward compatibility with Supabase patterns
export const getVideoQuery = (params: any = {}) => {
  return {
    select: (fields: string) => ({
      order: (field: string, options: { ascending?: boolean } = {}) => ({
        range: (from: number, to: number) => ({
          eq: (column: string, value: any) => apiClient.getVideos({
            page: Math.floor(from / (to - from + 1)) + 1,
            limit: to - from + 1,
            category: column === 'category' ? value : undefined,
            sort: field,
            order: options.ascending ? 'ASC' : 'DESC',
          }),
        }),
        limit: (count: number) => apiClient.getVideos({
          limit: count,
          sort: field,
          order: options.ascending ? 'ASC' : 'DESC',
        }),
      }),
      limit: (count: number) => apiClient.getVideos({ limit: count }),
    }),
  };
};

// Export for backward compatibility
export { apiClient as api };
export default apiClient;
