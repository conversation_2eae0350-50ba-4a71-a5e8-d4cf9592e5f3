<?php
/**
 * JWT Helper Class
 * Handles JWT token generation, validation, and management
 */

class JWTHelper {
    private $secretKey;
    private $algorithm = 'HS256';
    private $tokenExpiry = 86400; // 24 hours
    
    public function __construct() {
        $this->secretKey = $this->getSecretKey();
    }
    
    /**
     * Generate JWT token
     */
    public function generateToken(array $payload): string {
        $header = json_encode(['typ' => 'JWT', 'alg' => $this->algorithm]);
        
        $payload['iat'] = time();
        $payload['exp'] = time() + $this->tokenExpiry;
        $payload['jti'] = $this->generateJTI();
        
        $payload = json_encode($payload);
        
        $base64Header = $this->base64UrlEncode($header);
        $base64Payload = $this->base64UrlEncode($payload);
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $this->secretKey, true);
        $base64Signature = $this->base64UrlEncode($signature);
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }
    
    /**
     * Validate and decode JWT token
     */
    public function validateToken(string $token): ?array {
        try {
            $parts = explode('.', $token);
            
            if (count($parts) !== 3) {
                return null;
            }
            
            [$header, $payload, $signature] = $parts;
            
            // Verify signature
            $expectedSignature = hash_hmac('sha256', $header . "." . $payload, $this->secretKey, true);
            $expectedSignature = $this->base64UrlEncode($expectedSignature);
            
            if (!hash_equals($expectedSignature, $signature)) {
                return null;
            }
            
            // Decode payload
            $decodedPayload = json_decode($this->base64UrlDecode($payload), true);
            
            if (!$decodedPayload) {
                return null;
            }
            
            // Check expiration
            if (isset($decodedPayload['exp']) && $decodedPayload['exp'] < time()) {
                return null;
            }
            
            return $decodedPayload;
            
        } catch (Exception $e) {
            error_log('JWT validation error: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Refresh token if it's close to expiry
     */
    public function refreshToken(string $token): ?string {
        $payload = $this->validateToken($token);
        
        if (!$payload) {
            return null;
        }
        
        // Check if token is within refresh window (last 2 hours)
        $timeUntilExpiry = $payload['exp'] - time();
        if ($timeUntilExpiry > 7200) { // 2 hours
            return $token; // No need to refresh yet
        }
        
        // Remove old timestamp data
        unset($payload['iat'], $payload['exp'], $payload['jti']);
        
        return $this->generateToken($payload);
    }
    
    /**
     * Extract user ID from token
     */
    public function getUserIdFromToken(string $token): ?string {
        $payload = $this->validateToken($token);
        return $payload['user_id'] ?? null;
    }
    
    /**
     * Extract session ID from token
     */
    public function getSessionIdFromToken(string $token): ?string {
        $payload = $this->validateToken($token);
        return $payload['session_id'] ?? null;
    }
    
    /**
     * Get user roles from token
     */
    public function getUserRolesFromToken(string $token): array {
        $payload = $this->validateToken($token);
        return $payload['roles'] ?? [];
    }
    
    /**
     * Base64 URL encode
     */
    private function base64UrlEncode(string $data): string {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    /**
     * Base64 URL decode
     */
    private function base64UrlDecode(string $data): string {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }
    
    /**
     * Generate unique JWT ID
     */
    private function generateJTI(): string {
        return bin2hex(random_bytes(16));
    }
    
    /**
     * Get secret key from environment or generate one
     */
    private function getSecretKey(): string {
        $key = $_ENV['JWT_SECRET'] ?? null;
        
        if (!$key) {
            // In production, this should be set in environment variables
            $key = 'your-super-secret-jwt-key-change-this-in-production';
            error_log('Warning: Using default JWT secret key. Set JWT_SECRET environment variable.');
        }
        
        return $key;
    }
}