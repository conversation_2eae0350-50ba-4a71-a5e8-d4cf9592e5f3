/**
 * New Auth Store using MySQL API
 * Replaces Supabase authentication
 */

import { create } from 'zustand';
import { apiClient, User } from '../lib/api';

interface AuthState {
  user: User | null;
  isLoading: boolean;
  isApproved: boolean;
  signUp: (username: string, password: string) => Promise<void>;
  signIn: (username: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  loadUser: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isLoading: true,
  isApproved: false,

  signUp: async (username: string, password: string) => {
    try {
      const response = await apiClient.register(username, password);
      
      if (response.success && response.data.user) {
        set({
          user: response.data.user,
          isApproved: response.data.user.is_approved || false,
          isLoading: false,
        });
      } else {
        throw new Error('Registration failed');
      }
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  },

  signIn: async (username: string, password: string) => {
    try {
      const response = await apiClient.login(username, password);
      
      if (response.success && response.data.user) {
        set({
          user: response.data.user,
          isApproved: response.data.user.is_approved || false,
          isLoading: false,
        });
      } else {
        throw new Error('Login failed');
      }
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  },

  signOut: async () => {
    try {
      // Clear local state immediately for better UX
      set({
        user: null,
        isApproved: false,
        isLoading: false,
      });
      
      // Attempt to logout on server
      await apiClient.logout();
      
      // Clear any cached data or local storage if needed
      if (typeof window !== 'undefined') {
        // Clear any user-specific data from localStorage
        localStorage.removeItem('user-preferences');
        localStorage.removeItem('upload-progress');
      }
      
    } catch (error) {
      console.error('Sign out error:', error);
      // Local state is already cleared above, so user experience isn't affected
      // Just log the server-side error for debugging
    }
  },

  loadUser: async () => {
    try {
      set({ isLoading: true });

      const response = await apiClient.getCurrentUser();

      if (response.success && response.data.user) {
        set({
          user: response.data.user,
          isApproved: response.data.user.is_approved || false,
          isLoading: false,
        });
      } else {
        // No user session
        set({
          user: null,
          isApproved: false,
          isLoading: false,
        });
      }
    } catch (error) {
      // Check if it's a 401 (unauthorized) error - this is expected when no user is logged in
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('401') || errorMessage.includes('Unauthorized') || errorMessage.includes('Not authenticated')) {
        // 401 is expected when no user session exists - don't log as error
        // Silently handle this case
      } else {
        // Log other errors that are unexpected
        console.error('Load user error:', error);
      }

      // If there's an error (like 401), assume no user session
      set({
        user: null,
        isApproved: false,
        isLoading: false,
      });
    }
  },
}));

// Initialize auth state on app load
if (typeof window !== 'undefined') {
  // Load user on app start
  useAuthStore.getState().loadUser();
}
