<?php
/**
 * Database Setup Script
 * Creates the necessary tables for authentication
 */

// Set content type for web display
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html>
<head>
    <title>BlueFilm Database Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🎬 BlueFilm Database Setup</h1>";

// Include database configuration
require_once 'config.php';

// Get database connection
$pdo = getDbConnection();
if (!$pdo) {
    echo "<p class='error'>❌ Database connection failed.</p>";
    echo "<p>Please check your database configuration in <code>config.php</code></p>";
    echo "<p>Make sure you have:</p>";
    echo "<ul>";
    echo "<li>Created a MySQL database in cPanel</li>";
    echo "<li>Created a database user with proper permissions</li>";
    echo "<li>Updated the credentials in config.php</li>";
    echo "</ul>";
    echo "</div></body></html>";
    exit();
}

echo "<p class='success'>✅ Connected to database successfully.</p>";
    
try {
    // Create users table
    echo "<h2>Creating Users Table...</h2>";
    $createUsersTable = "
        CREATE TABLE IF NOT EXISTS users (
            id VARCHAR(255) PRIMARY KEY,
            username VARCHAR(255) UNIQUE NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            name VARCHAR(255),
            password_hash VARCHAR(255) NOT NULL,
            email_verified BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_username (username)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $pdo->exec($createUsersTable);
    echo "<p class='success'>✅ Users table created successfully.</p>";

    // Create user_sessions table
    echo "<h2>Creating User Sessions Table...</h2>";
    $createSessionsTable = "
        CREATE TABLE IF NOT EXISTS user_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            token VARCHAR(255) UNIQUE NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_token (token),
            INDEX idx_user_id (user_id),
            INDEX idx_expires_at (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $pdo->exec($createSessionsTable);
    echo "<p class='success'>✅ User sessions table created successfully.</p>";

    // Create a cleanup procedure for expired sessions
    echo "<h2>Setting Up Session Cleanup...</h2>";
    try {
        $createCleanupProcedure = "
            CREATE EVENT IF NOT EXISTS cleanup_expired_sessions
            ON SCHEDULE EVERY 1 HOUR
            DO
            DELETE FROM user_sessions WHERE expires_at < NOW()
        ";

        $pdo->exec($createCleanupProcedure);
        echo "<p class='success'>✅ Session cleanup procedure created successfully.</p>";
    } catch (Exception $e) {
        echo "<p class='info'>ℹ️ Session cleanup procedure skipped (may not be supported on shared hosting).</p>";
    }

    // Test the setup
    echo "<h2>Testing Database Setup...</h2>";
    $testQuery = "SELECT COUNT(*) as count FROM users";
    $stmt = $pdo->query($testQuery);
    $result = $stmt->fetch();
    echo "<p class='success'>✅ Database test successful. Current users: " . $result['count'] . "</p>";

    echo "<h2>🎉 Database Setup Complete!</h2>";
    echo "<p class='success'>Your authentication system is now ready to use.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Try signing up for a new account on your website</li>";
    echo "<li>Test the login functionality</li>";
    echo "<li>Delete this setup file for security: <code>rm setup-db.php</code></li>";
    echo "</ul>";

} catch (Exception $e) {
    echo "<p class='error'>❌ Database setup failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
}

echo "</div></body></html>";
?>
?>
