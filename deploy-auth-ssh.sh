#!/bin/bash

# Deploy Authentication System to cPanel via SSH
# This script deploys the MySQL-based authentication system directly to Namecheap hosting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

# SSH Configuration
SSH_HOST="premium34.web-hosting.com"
SSH_PORT="21098"
SSH_USER="bluerpcm"
DB_NAME="bluerpcm_bluefilmx"
DB_USER="bluerpcm_dbuser"
DB_PASS="kingpatrick100"

# Remote paths
API_REMOTE_PATH="public_html/api"
FRONTEND_REMOTE_PATH="public_html"

print_status "🚀 Starting Authentication System deployment to cPanel via SSH..."

# Step 1: Build the frontend
print_status "📦 Building frontend..."
npm run build
print_success "✅ Frontend built successfully!"

# Step 2: Test SSH connection
print_status "🔑 Testing SSH connection..."
if ssh -p ${SSH_PORT} ${SSH_USER}@${SSH_HOST} "echo 'SSH connection successful!'" >/dev/null 2>&1; then
    print_success "✅ SSH connection working!"
else
    print_error "❌ SSH connection failed. Please check your SSH setup."
    exit 1
fi

# Step 3: Create database schema
print_status "🗄️  Setting up authentication database schema..."
ssh -p ${SSH_PORT} ${SSH_USER}@${SSH_HOST} "mysql -u ${DB_USER} -p${DB_PASS} ${DB_NAME} -e '
CREATE TABLE IF NOT EXISTS user (
    id VARCHAR(36) PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    avatar_url TEXT,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS account (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    provider VARCHAR(50) NOT NULL,
    provider_account_id VARCHAR(255) NOT NULL,
    access_token TEXT,
    refresh_token TEXT,
    expires_at BIGINT,
    token_type VARCHAR(50),
    scope TEXT,
    id_token TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    UNIQUE KEY unique_provider_account (provider, provider_account_id)
);

CREATE TABLE IF NOT EXISTS session (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    expires TIMESTAMP NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS verification (
    id VARCHAR(36) PRIMARY KEY,
    identifier VARCHAR(255) NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
'"
print_success "✅ Database schema created!"

# Step 4: Upload API files
print_status "📤 Uploading API files..."

# Upload auth-mysql.php
scp -P ${SSH_PORT} namecheap-api/auth-mysql.php ${SSH_USER}@${SSH_HOST}:${API_REMOTE_PATH}/auth-mysql.php
print_success "✅ auth-mysql.php uploaded!"

# Upload updated database.php
scp -P ${SSH_PORT} namecheap-api/config/database.php ${SSH_USER}@${SSH_HOST}:${API_REMOTE_PATH}/config/database.php
print_success "✅ database.php uploaded!"

# Upload .htaccess
scp -P ${SSH_PORT} namecheap-api/.htaccess ${SSH_USER}@${SSH_HOST}:${API_REMOTE_PATH}/.htaccess
print_success "✅ API .htaccess uploaded!"

# Step 5: Upload frontend files
print_status "📤 Uploading frontend files..."

# Upload main files
scp -P ${SSH_PORT} dist/index.html ${SSH_USER}@${SSH_HOST}:${FRONTEND_REMOTE_PATH}/index.html
scp -P ${SSH_PORT} dist/404.html ${SSH_USER}@${SSH_HOST}:${FRONTEND_REMOTE_PATH}/404.html 2>/dev/null || echo "404.html not found, skipping..."
scp -P ${SSH_PORT} dist/favicon.ico ${SSH_USER}@${SSH_HOST}:${FRONTEND_REMOTE_PATH}/favicon.ico 2>/dev/null || echo "favicon.ico not found, skipping..."
scp -P ${SSH_PORT} dist/favicon.svg ${SSH_USER}@${SSH_HOST}:${FRONTEND_REMOTE_PATH}/favicon.svg 2>/dev/null || echo "favicon.svg not found, skipping..."

# Upload assets directory
if [ -d "dist/assets" ]; then
    ssh -p ${SSH_PORT} ${SSH_USER}@${SSH_HOST} "mkdir -p ${FRONTEND_REMOTE_PATH}/assets"
    scp -r -P ${SSH_PORT} dist/assets/* ${SSH_USER}@${SSH_HOST}:${FRONTEND_REMOTE_PATH}/assets/
    print_success "✅ Assets uploaded!"
fi

print_success "✅ Frontend files uploaded!"

# Step 6: Set proper permissions
print_status "🔒 Setting file permissions..."
ssh -p ${SSH_PORT} ${SSH_USER}@${SSH_HOST} "
chmod 644 ${API_REMOTE_PATH}/auth-mysql.php
chmod 644 ${API_REMOTE_PATH}/config/database.php
chmod 644 ${API_REMOTE_PATH}/.htaccess
chmod 644 ${FRONTEND_REMOTE_PATH}/index.html
chmod -R 644 ${FRONTEND_REMOTE_PATH}/assets/* 2>/dev/null || true
"
print_success "✅ Permissions set!"

# Step 7: Test the deployment
print_status "🧪 Testing deployment..."

# Test API endpoint
print_status "Testing API endpoint..."
api_test=$(curl -s -o /dev/null -w "%{http_code}" "https://www.bluefilmx.com/api/auth-mysql.php?action=test" || echo "000")
if [ "$api_test" = "200" ]; then
    print_success "✅ API endpoint is responding!"
else
    print_warning "⚠️  API endpoint test returned: $api_test"
fi

# Test frontend
print_status "Testing frontend..."
frontend_test=$(curl -s -o /dev/null -w "%{http_code}" "https://www.bluefilmx.com/" || echo "000")
if [ "$frontend_test" = "200" ]; then
    print_success "✅ Frontend is responding!"
else
    print_warning "⚠️  Frontend test returned: $frontend_test"
fi

print_success "🎉 Authentication system deployment completed!"
print_status "📋 Deployment Summary:"
echo "   • Frontend: https://www.bluefilmx.com/"
echo "   • API: https://www.bluefilmx.com/api/auth-mysql.php"
echo "   • Database: MySQL authentication tables created"
echo "   • Authentication endpoints: signin, signup, signout, session"

print_status "🔧 Next steps:"
echo "   1. Test the authentication system in your browser"
echo "   2. Create a test user account"
echo "   3. Verify session management is working"
echo "   4. Check that the frontend can communicate with the API"

print_status "📝 Test URLs:"
echo "   • Sign up: https://www.bluefilmx.com/ (use the auth modal)"
echo "   • API test: https://www.bluefilmx.com/api/auth-mysql.php?action=test"