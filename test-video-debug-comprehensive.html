<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Video Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #2a2a2a;
        }
        .success { border-color: #4ade80; background-color: #1a2e1a; }
        .error { border-color: #ef4444; background-color: #2e1a1a; }
        .loading { border-color: #fbbf24; background-color: #2e2a1a; }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #2563eb; }
        video {
            width: 100%;
            max-width: 600px;
            height: auto;
            background-color: #000;
            border-radius: 5px;
            margin: 10px 0;
        }
        pre {
            background-color: #111;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log {
            background-color: #111;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .log-error { color: #ef4444; }
        .log-success { color: #4ade80; }
        .log-info { color: #60a5fa; }
        .log-warn { color: #fbbf24; }
        .video-info {
            background-color: #374151;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔍 Comprehensive Video Debug</h1>
    <p>Complete video playback debugging with URL validation, format testing, and browser compatibility</p>

    <div class="test-section">
        <h3>Step 1: Get Video Data</h3>
        <button onclick="getVideoData()">Get Latest Video</button>
        <div id="video-data-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 2: URL Validation Test</h3>
        <button onclick="testUrlValidation()" id="urlValidationBtn" disabled>Test URL Validation</button>
        <div id="url-validation-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 3: Video File Analysis</h3>
        <button onclick="analyzeVideoFile()" id="analyzeBtn" disabled>Analyze Video File</button>
        <div id="analysis-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 4: Browser Compatibility</h3>
        <button onclick="testBrowserCompatibility()">Test Browser Support</button>
        <div id="browser-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 5: Video Player Test</h3>
        <button onclick="testVideoPlayer()" id="playerBtn" disabled>Test Video Player</button>
        <div id="player-result"></div>
    </div>

    <div class="test-section">
        <h3>Debug Logs</h3>
        <button onclick="clearLogs()">Clear Logs</button>
        <div id="debug-logs" class="log"></div>
    </div>

    <script>
        let currentVideo = null;
        let logs = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logs.push({ type, message, timestamp });
            updateLogsDisplay();
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateLogsDisplay() {
            const container = document.getElementById('debug-logs');
            const recentLogs = logs.slice(-30);
            
            container.innerHTML = recentLogs.map(log => 
                `<div class="log-${log.type}">[${log.timestamp}] ${log.message}</div>`
            ).join('');
            
            container.scrollTop = container.scrollHeight;
        }

        function clearLogs() {
            logs = [];
            updateLogsDisplay();
        }

        function setResult(elementId, message, type = 'loading') {
            const element = document.getElementById(elementId);
            element.className = `test-section ${type}`;
            element.innerHTML = message;
        }

        // Simulate the validateStorageUrl function from the frontend
        function validateStorageUrl(url) {
            if (!url) return '';

            let cleanedUrl = url.trim()
                .replace(/[\r\n\t]/g, '')
                .replace(/%0A/g, '')
                .replace(/%0D/g, '')
                .replace(/%09/g, '');

            if (cleanedUrl.includes('supabase.co/storage/v1/object/public/') ||
                cleanedUrl.includes('bluefilmx.com/media/') ||
                cleanedUrl.includes('premium34.web-hosting.com') ||
                cleanedUrl.startsWith('http://') ||
                cleanedUrl.startsWith('https://')) {
                return cleanedUrl;
            }

            if (cleanedUrl.startsWith('/storage/') || cleanedUrl.startsWith('storage/')) {
                const cleanPath = cleanedUrl.startsWith('/') ? cleanedUrl.slice(1) : cleanedUrl;
                return `https://vsnsglgyapexhwyfylic.supabase.co/${cleanPath}`;
            }

            if (cleanedUrl.startsWith('/media/') || cleanedUrl.startsWith('media/')) {
                const cleanPath = cleanedUrl.startsWith('/') ? cleanedUrl : `/${cleanedUrl}`;
                return `https://www.bluefilmx.com${cleanPath}`;
            }

            if (!cleanedUrl.includes('://') && !cleanedUrl.startsWith('/')) {
                return `https://www.bluefilmx.com/media/${cleanedUrl}`;
            }

            return cleanedUrl;
        }

        async function getVideoData() {
            setResult('video-data-result', 'Fetching video data...', 'loading');
            log('Fetching latest video from API');
            
            try {
                const response = await fetch('https://www.bluefilmx.com/api/videos.php?limit=1', {
                    credentials: 'include',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();
                log(`API response received: ${JSON.stringify(data).substring(0, 100)}...`);
                
                if (data.success && data.data.videos && data.data.videos.length > 0) {
                    currentVideo = data.data.videos[0];
                    
                    setResult('video-data-result', `
                        <strong>✅ Video Data Retrieved!</strong><br>
                        <div class="video-info">
                            <strong>ID:</strong> ${currentVideo.id}<br>
                            <strong>Title:</strong> ${currentVideo.title}<br>
                            <strong>Original Video URL:</strong> ${currentVideo.video_url}<br>
                            <strong>Original Thumbnail URL:</strong> ${currentVideo.thumbnail_url || 'None'}<br>
                            <strong>Duration:</strong> ${currentVideo.duration || 0} seconds<br>
                            <strong>Views:</strong> ${currentVideo.views}<br>
                            <strong>Created:</strong> ${currentVideo.created_at}<br>
                            <strong>User:</strong> ${currentVideo.username || 'Unknown'}
                        </div>
                    `, 'success');
                    
                    // Enable next tests
                    document.getElementById('urlValidationBtn').disabled = false;
                    document.getElementById('analyzeBtn').disabled = false;
                    document.getElementById('playerBtn').disabled = false;
                    
                    log(`Video loaded: ${currentVideo.title}`);
                } else {
                    throw new Error('No videos found in API response');
                }

            } catch (error) {
                log(`Failed to get video: ${error.message}`, 'error');
                setResult('video-data-result', `
                    <strong>❌ Failed to Get Video:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        async function testUrlValidation() {
            if (!currentVideo) return;

            setResult('url-validation-result', 'Testing URL validation...', 'loading');
            log('Testing URL validation logic');
            
            const originalVideoUrl = currentVideo.video_url;
            const originalThumbnailUrl = currentVideo.thumbnail_url;
            
            const validatedVideoUrl = validateStorageUrl(originalVideoUrl);
            const validatedThumbnailUrl = validateStorageUrl(originalThumbnailUrl);
            
            log(`Original video URL: ${originalVideoUrl}`);
            log(`Validated video URL: ${validatedVideoUrl}`);
            log(`Original thumbnail URL: ${originalThumbnailUrl}`);
            log(`Validated thumbnail URL: ${validatedThumbnailUrl}`);
            
            // Test if URLs are accessible
            let videoAccessible = false;
            let thumbnailAccessible = false;
            
            try {
                const videoResponse = await fetch(validatedVideoUrl, { method: 'HEAD' });
                videoAccessible = videoResponse.ok;
                log(`Video URL accessible: ${videoAccessible} (${videoResponse.status})`);
            } catch (error) {
                log(`Video URL test failed: ${error.message}`, 'error');
            }
            
            try {
                if (validatedThumbnailUrl) {
                    const thumbnailResponse = await fetch(validatedThumbnailUrl, { method: 'HEAD' });
                    thumbnailAccessible = thumbnailResponse.ok;
                    log(`Thumbnail URL accessible: ${thumbnailAccessible} (${thumbnailResponse.status})`);
                }
            } catch (error) {
                log(`Thumbnail URL test failed: ${error.message}`, 'warn');
            }
            
            setResult('url-validation-result', `
                <strong>📋 URL Validation Results:</strong><br>
                <div class="video-info">
                    <strong>Video URL Validation:</strong><br>
                    • Original: ${originalVideoUrl}<br>
                    • Validated: ${validatedVideoUrl}<br>
                    • Accessible: ${videoAccessible ? '✅ Yes' : '❌ No'}<br>
                    <br>
                    <strong>Thumbnail URL Validation:</strong><br>
                    • Original: ${originalThumbnailUrl || 'None'}<br>
                    • Validated: ${validatedThumbnailUrl || 'None'}<br>
                    • Accessible: ${thumbnailAccessible ? '✅ Yes' : '❌ No'}<br>
                </div>
            `, videoAccessible ? 'success' : 'error');
        }

        async function analyzeVideoFile() {
            if (!currentVideo) return;

            setResult('analysis-result', 'Analyzing video file...', 'loading');
            log('Starting video file analysis');
            
            const videoUrl = validateStorageUrl(currentVideo.video_url);
            
            try {
                // Get file headers
                const response = await fetch(videoUrl, { method: 'HEAD' });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                });
                
                log(`Video file headers: ${JSON.stringify(headers)}`);
                
                // Try to create a video element to test format support
                const video = document.createElement('video');
                video.preload = 'metadata';
                
                let formatSupported = false;
                let videoMetadata = {};
                
                await new Promise((resolve) => {
                    video.addEventListener('loadedmetadata', () => {
                        formatSupported = true;
                        videoMetadata = {
                            duration: video.duration,
                            videoWidth: video.videoWidth,
                            videoHeight: video.videoHeight,
                            readyState: video.readyState,
                            networkState: video.networkState
                        };
                        log(`Video metadata loaded: ${JSON.stringify(videoMetadata)}`, 'success');
                        resolve();
                    });
                    
                    video.addEventListener('error', (e) => {
                        const error = video.error;
                        let errorMessage = 'Unknown error';
                        if (error) {
                            switch (error.code) {
                                case error.MEDIA_ERR_DECODE:
                                    errorMessage = 'Video format not supported (decode error)';
                                    break;
                                case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                                    errorMessage = 'Video source format not supported';
                                    break;
                                case error.MEDIA_ERR_NETWORK:
                                    errorMessage = 'Network error loading video';
                                    break;
                                default:
                                    errorMessage = `Error code: ${error.code}`;
                            }
                        }
                        log(`Video format test failed: ${errorMessage}`, 'error');
                        resolve();
                    });
                    
                    video.src = videoUrl;
                    
                    // Timeout after 10 seconds
                    setTimeout(() => {
                        log('Video format test timed out', 'warn');
                        resolve();
                    }, 10000);
                });
                
                setResult('analysis-result', `
                    <strong>📊 Video File Analysis:</strong><br>
                    <div class="video-info">
                        <strong>File Properties:</strong><br>
                        • Content-Type: ${headers['content-type'] || 'Not specified'}<br>
                        • Content-Length: ${headers['content-length'] ? (parseInt(headers['content-length']) / 1024 / 1024).toFixed(2) + ' MB' : 'Not specified'}<br>
                        • Accept-Ranges: ${headers['accept-ranges'] || 'Not specified'}<br>
                        • Last-Modified: ${headers['last-modified'] || 'Not specified'}<br>
                        <br>
                        <strong>Format Support:</strong><br>
                        • Browser Compatible: ${formatSupported ? '✅ Yes' : '❌ No'}<br>
                        ${formatSupported ? `
                        • Duration: ${videoMetadata.duration?.toFixed(2) || 'Unknown'} seconds<br>
                        • Dimensions: ${videoMetadata.videoWidth || 'Unknown'}x${videoMetadata.videoHeight || 'Unknown'}<br>
                        • Ready State: ${videoMetadata.readyState || 'Unknown'}<br>
                        ` : ''}
                    </div>
                `, formatSupported ? 'success' : 'error');
                
            } catch (error) {
                log(`Video analysis failed: ${error.message}`, 'error');
                setResult('analysis-result', `
                    <strong>❌ Video Analysis Failed:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        function testBrowserCompatibility() {
            setResult('browser-result', 'Testing browser compatibility...', 'loading');
            log('Testing browser video format support');
            
            const video = document.createElement('video');
            const formats = [
                { type: 'video/mp4; codecs="avc1.42E01E"', name: 'MP4 (H.264 Baseline)' },
                { type: 'video/mp4; codecs="avc1.4D401E"', name: 'MP4 (H.264 Main)' },
                { type: 'video/mp4; codecs="avc1.64001E"', name: 'MP4 (H.264 High)' },
                { type: 'video/mp4', name: 'MP4 (Generic)' },
                { type: 'video/webm; codecs="vp8"', name: 'WebM (VP8)' },
                { type: 'video/webm; codecs="vp9"', name: 'WebM (VP9)' },
                { type: 'video/webm', name: 'WebM (Generic)' },
                { type: 'video/ogg; codecs="theora"', name: 'Ogg (Theora)' }
            ];

            let supportInfo = '<strong>📋 Browser Video Format Support:</strong><br><br>';
            
            formats.forEach(format => {
                const support = video.canPlayType(format.type);
                let supportText = '';
                let supportClass = '';
                
                switch (support) {
                    case 'probably':
                        supportText = '✅ Probably';
                        supportClass = 'log-success';
                        break;
                    case 'maybe':
                        supportText = '⚠️ Maybe';
                        supportClass = 'log-info';
                        break;
                    default:
                        supportText = '❌ No';
                        supportClass = 'log-error';
                        break;
                }
                
                supportInfo += `<span class="${supportClass}">${format.name}: ${supportText}</span><br>`;
                log(`${format.name}: ${supportText}`, support === 'probably' ? 'success' : support === 'maybe' ? 'info' : 'error');
            });

            supportInfo += '<br><strong>🌐 Browser Information:</strong><br>';
            supportInfo += `<strong>User Agent:</strong> ${navigator.userAgent}<br>`;
            supportInfo += `<strong>Platform:</strong> ${navigator.platform}<br>`;
            
            setResult('browser-result', supportInfo, 'success');
        }

        async function testVideoPlayer() {
            if (!currentVideo) return;

            setResult('player-result', 'Testing video player...', 'loading');
            log('Creating video player for testing');
            
            const videoUrl = validateStorageUrl(currentVideo.video_url);
            const thumbnailUrl = validateStorageUrl(currentVideo.thumbnail_url);
            
            const videoElement = document.createElement('video');
            videoElement.controls = true;
            videoElement.preload = 'metadata';
            videoElement.style.width = '100%';
            videoElement.style.maxWidth = '600px';
            videoElement.style.height = 'auto';
            videoElement.poster = thumbnailUrl;

            let loadSuccess = false;
            let errorMessage = '';
            let metadata = {};

            const eventPromise = new Promise((resolve) => {
                videoElement.addEventListener('loadedmetadata', () => {
                    loadSuccess = true;
                    metadata = {
                        duration: videoElement.duration,
                        videoWidth: videoElement.videoWidth,
                        videoHeight: videoElement.videoHeight,
                        readyState: videoElement.readyState,
                        networkState: videoElement.networkState
                    };
                    log(`Video player loaded successfully: ${JSON.stringify(metadata)}`, 'success');
                    resolve();
                });

                videoElement.addEventListener('error', (e) => {
                    const error = videoElement.error;
                    if (error) {
                        switch (error.code) {
                            case error.MEDIA_ERR_ABORTED:
                                errorMessage = 'Video playback aborted';
                                break;
                            case error.MEDIA_ERR_NETWORK:
                                errorMessage = 'Network error during video loading';
                                break;
                            case error.MEDIA_ERR_DECODE:
                                errorMessage = 'Video format not supported by this browser (decode error)';
                                break;
                            case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                                errorMessage = 'Video source format not supported';
                                break;
                            default:
                                errorMessage = `Unknown error (code: ${error.code})`;
                        }
                        if (error.message) {
                            errorMessage += ` - ${error.message}`;
                        }
                    }
                    log(`Video player error: ${errorMessage}`, 'error');
                    resolve();
                });

                setTimeout(() => {
                    if (!loadSuccess && !errorMessage) {
                        errorMessage = 'Video loading timeout (10 seconds)';
                        log('Video player test timed out', 'warn');
                    }
                    resolve();
                }, 10000);
            });

            videoElement.src = videoUrl;
            log(`Video player source set: ${videoUrl}`);

            await eventPromise;

            if (loadSuccess) {
                setResult('player-result', `
                    <strong>✅ Video Player Working!</strong><br>
                    <div class="video-info">
                        <strong>Video Properties:</strong><br>
                        • Duration: ${metadata.duration?.toFixed(2) || 'Unknown'} seconds<br>
                        • Dimensions: ${metadata.videoWidth || 'Unknown'}x${metadata.videoHeight || 'Unknown'}<br>
                        • Ready State: ${metadata.readyState || 'Unknown'}<br>
                        • Network State: ${metadata.networkState || 'Unknown'}<br>
                    </div>
                    <br>
                    ${videoElement.outerHTML}
                `, 'success');
            } else {
                setResult('player-result', `
                    <strong>❌ Video Player Failed!</strong><br>
                    <strong>Error:</strong> ${errorMessage}<br>
                    <strong>Video URL:</strong> ${videoUrl}<br>
                    <strong>Thumbnail URL:</strong> ${thumbnailUrl}<br>
                    <br>
                    <strong>This is the exact error users are seeing!</strong>
                `, 'error');
            }
        }

        // Auto-start tests
        window.addEventListener('load', () => {
            log('🧪 Comprehensive Video Debug Page Loaded');
            setTimeout(() => {
                getVideoData();
            }, 500);
            setTimeout(() => {
                testBrowserCompatibility();
            }, 1000);
        });
    </script>
</body>
</html>
