#!/bin/bash

# Deploy CORS Fix to Namecheap Server
# This script uploads the fixed API files to resolve CORS issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Server configuration
SERVER_HOST="premium34.web-hosting.com"
SERVER_USER="bluerpcm"
SSH_PORT="21098"
API_REMOTE_PATH="public_html/api"
HTACCESS_REMOTE_PATH="public_html"

print_status() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

print_status "🔧 Deploying CORS Fix to BlueFilmX Server..."
echo ""

# Check if required files exist
if [ ! -f "namecheap-api/config/database.php" ]; then
    print_error "❌ Error: namecheap-api/config/database.php not found"
    exit 1
fi

if [ ! -f "namecheap-api/.htaccess" ]; then
    print_error "❌ Error: namecheap-api/.htaccess not found"
    exit 1
fi

if [ ! -f ".htaccess" ]; then
    print_error "❌ Error: .htaccess not found"
    exit 1
fi

# Create backup of current files on server
print_status "📦 Creating backup of current files..."
ssh -p ${SSH_PORT} ${SERVER_USER}@${SERVER_HOST} "
    cd ${API_REMOTE_PATH}
    if [ -f config/database.php ]; then
        cp config/database.php config/database.php.backup-\$(date +%Y%m%d-%H%M%S)
        echo 'Backed up database.php'
    fi
    if [ -f .htaccess ]; then
        cp .htaccess .htaccess.backup-\$(date +%Y%m%d-%H%M%S)
        echo 'Backed up API .htaccess'
    fi
    cd ../${HTACCESS_REMOTE_PATH}
    if [ -f .htaccess ]; then
        cp .htaccess .htaccess.backup-\$(date +%Y%m%d-%H%M%S)
        echo 'Backed up main .htaccess'
    fi
"

# Upload fixed database.php
print_status "📤 Uploading fixed database.php..."
scp -P ${SSH_PORT} namecheap-api/config/database.php ${SERVER_USER}@${SERVER_HOST}:${API_REMOTE_PATH}/config/database.php
print_success "✅ database.php uploaded"

# Upload fixed API .htaccess
print_status "📤 Uploading fixed API .htaccess..."
scp -P ${SSH_PORT} namecheap-api/.htaccess ${SERVER_USER}@${SERVER_HOST}:${API_REMOTE_PATH}/.htaccess
print_success "✅ API .htaccess uploaded"

# Upload fixed main .htaccess
print_status "📤 Uploading fixed main .htaccess..."
scp -P ${SSH_PORT} .htaccess ${SERVER_USER}@${SERVER_HOST}:${HTACCESS_REMOTE_PATH}/.htaccess
print_success "✅ Main .htaccess uploaded"

# Upload test file
print_status "📤 Uploading CORS test file..."
scp -P ${SSH_PORT} test-cors-fix.html ${SERVER_USER}@${SERVER_HOST}:${HTACCESS_REMOTE_PATH}/test-cors-fix.html
print_success "✅ Test file uploaded"

# Set proper permissions
print_status "🔐 Setting file permissions..."
ssh -p ${SSH_PORT} ${SERVER_USER}@${SERVER_HOST} "
    chmod 644 ${API_REMOTE_PATH}/config/database.php
    chmod 644 ${API_REMOTE_PATH}/.htaccess
    chmod 644 ${HTACCESS_REMOTE_PATH}/.htaccess
    chmod 644 ${HTACCESS_REMOTE_PATH}/test-cors-fix.html
"
print_success "✅ Permissions set"

echo ""
print_success "🎉 CORS fix deployment completed!"
echo ""
print_status "📋 What was fixed:"
echo "   • Removed wildcard CORS headers from .htaccess files"
echo "   • PHP setCORSHeaders() function now handles CORS properly"
echo "   • Added support for both bluefilmx.com and www.bluefilmx.com"
echo "   • Added debug logging for troubleshooting"
echo ""
print_status "🧪 Testing:"
echo "   1. Open: https://www.bluefilmx.com/test-cors-fix.html"
echo "   2. Click the test buttons to verify CORS is working"
echo "   3. Check browser console for any remaining errors"
echo ""
print_status "🔍 Monitoring:"
echo "   • Check server error logs for CORS debug messages"
echo "   • Test your main website at https://www.bluefilmx.com"
echo "   • Verify API calls work without CORS errors"
echo ""
print_warning "⚠️  Note: Debug logging is enabled. Remove it in production by editing database.php"
