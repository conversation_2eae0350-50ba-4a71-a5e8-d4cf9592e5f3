<?php
/**
 * Database Configuration for Namecheap Hosting
 * Update these values with your actual database credentials
 */

// Database configuration - UPDATE THESE VALUES
$DB_CONFIG = [
    'host' => 'localhost',  // Usually 'localhost' on shared hosting
    'dbname' => 'your_database_name',  // Your database name from cPanel
    'username' => 'your_db_username',  // Your database username
    'password' => 'your_db_password'   // Your database password
];

// You can also use environment variables if available
if (isset($_ENV['DB_HOST'])) {
    $DB_CONFIG['host'] = $_ENV['DB_HOST'];
}
if (isset($_ENV['DB_NAME'])) {
    $DB_CONFIG['dbname'] = $_ENV['DB_NAME'];
}
if (isset($_ENV['DB_USER'])) {
    $DB_CONFIG['username'] = $_ENV['DB_USER'];
}
if (isset($_ENV['DB_PASS'])) {
    $DB_CONFIG['password'] = $_ENV['DB_PASS'];
}

/**
 * Get database connection
 */
function getDbConnection() {
    global $DB_CONFIG;
    
    try {
        $dsn = "mysql:host={$DB_CONFIG['host']};dbname={$DB_CONFIG['dbname']};charset=utf8mb4";
        $pdo = new PDO($dsn, $DB_CONFIG['username'], $DB_CONFIG['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        return null;
    }
}
?>
