<?php
/**
 * Test Authentication System
 * Verifies that the new authentication system is working properly
 */

require_once __DIR__ . '/namecheap-api/config/database.php';
require_once __DIR__ . '/auth-system/backend/AuthService.php';

echo "🧪 Testing Authentication System...\n\n";

try {
    // Test database connection
    echo "1. Testing database connection...\n";
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    echo "✅ Database connection successful\n\n";
    
    // Test AuthService initialization
    echo "2. Testing AuthService initialization...\n";
    $authService = new AuthService();
    echo "✅ AuthService initialized successfully\n\n";
    
    // Test user registration
    echo "3. Testing user registration...\n";
    $testUserData = [
        'email' => 'testuser_' . time() . '@example.com',
        'username' => 'testuser_' . time(),
        'password' => 'TestPassword123!',
        'first_name' => 'Test',
        'last_name' => 'User'
    ];
    
    $registerResult = $authService->register($testUserData);
    if ($registerResult['success']) {
        echo "✅ User registration successful\n";
        echo "   User ID: " . $registerResult['data']['user']['id'] . "\n\n";
        
        // Test user login
        echo "4. Testing user login...\n";
        $loginResult = $authService->login(
            $testUserData['email'],
            $testUserData['password']
        );
        
        if ($loginResult['success']) {
            echo "✅ User login successful\n";
            echo "   Token: " . substr($loginResult['data']['token'], 0, 20) . "...\n";
            echo "   Session ID: " . $loginResult['data']['session_id'] . "\n\n";
            
            // Test token validation
            echo "5. Testing token validation...\n";
            $token = $loginResult['data']['token'];
            $validateResult = $authService->validateToken($token);
            
            if ($validateResult['success']) {
                echo "✅ Token validation successful\n";
                echo "   User: " . $validateResult['data']['user']['email'] . "\n\n";
            } else {
                echo "❌ Token validation failed: " . $validateResult['error'] . "\n\n";
            }
        } else {
            echo "❌ User login failed: " . $loginResult['error'] . "\n\n";
        }
    } else {
        echo "❌ User registration failed: " . $registerResult['error'] . "\n\n";
    }
    
    // Test existing test users
    echo "6. Testing existing test users...\n";
    $testLogin = $authService->login(
        '<EMAIL>',
        'admin123'
    );
    
    if ($testLogin['success']) {
        echo "✅ Admin test user login successful\n";
    } else {
        echo "❌ Admin test user login failed: " . $testLogin['error'] . "\n";
    }
    
    $testLogin2 = $authService->login(
        '<EMAIL>',
        'testpassword123'
    );
    
    if ($testLogin2['success']) {
        echo "✅ Test user login successful\n";
    } else {
        echo "❌ Test user login failed: " . $testLogin2['error'] . "\n";
    }
    
    echo "\n🎉 Authentication system test completed!\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>