/**
 * Namecheap Storage Service
 * Replaces Supabase storage functionality with direct HTTP uploads
 */

import { useAuthStore } from '../stores/authStore';

// Configuration
const STORAGE_CONFIG = {
  baseUrl: import.meta.env.VITE_STORAGE_BASE_URL || window.location.origin,
  uploadEndpoint: '/api/upload.php',
  videosPath: '/media/videos',
  thumbnailsPath: '/media/thumbnails',
  maxFileSize: 100 * 1024 * 1024, // 100MB
  chunkSize: 5 * 1024 * 1024, // 5MB chunks
};

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface UploadResult {
  url: string;
  filename: string;
  size: number;
  type: string;
}

export interface StorageError {
  message: string;
  code?: string;
}

/**
 * Check if user is authenticated
 */
function isAuthenticated(): boolean {
  const { user } = useAuthStore.getState();
  return !!user;
}

/**
 * Upload a file with progress tracking
 */
export async function uploadFile(
  file: File,
  type: 'video' | 'image',
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> {
  if (!isAuthenticated()) {
    throw new Error('Authentication required');
  }

  // Validate file size
  if (file.size > STORAGE_CONFIG.maxFileSize) {
    throw new Error(`File too large. Maximum size: ${STORAGE_CONFIG.maxFileSize / 1024 / 1024}MB`);
  }

  // Determine if we need chunked upload
  const useChunkedUpload = file.size > STORAGE_CONFIG.chunkSize;

  if (useChunkedUpload) {
    return uploadFileInChunks(file, type, onProgress);
  } else {
    return uploadFileRegular(file, type, onProgress);
  }
}

/**
 * Regular file upload for smaller files
 */
async function uploadFileRegular(
  file: File,
  type: 'video' | 'image',
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> {
  if (!isAuthenticated()) {
    throw new Error('Authentication required');
  }

  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', type);

  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    // Track upload progress
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable && onProgress) {
        onProgress({
          loaded: event.loaded,
          total: event.total,
          percentage: Math.round((event.loaded / event.total) * 100)
        });
      }
    });

    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText);
          if (response.success) {
            resolve(response);
          } else {
            reject(new Error(response.error || 'Upload failed'));
          }
        } catch (error) {
          reject(new Error('Invalid response from server'));
        }
      } else {
        try {
          const response = JSON.parse(xhr.responseText);
          reject(new Error(response.error || `Upload failed with status ${xhr.status}`));
        } catch {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      }
    });

    xhr.addEventListener('error', () => {
      reject(new Error('Network error during upload'));
    });

    xhr.addEventListener('timeout', () => {
      reject(new Error('Upload timeout'));
    });

    xhr.open('POST', STORAGE_CONFIG.baseUrl + STORAGE_CONFIG.uploadEndpoint);
    xhr.withCredentials = true; // Include cookies for session-based auth
    xhr.timeout = 300000; // 5 minutes
    xhr.send(formData);
  });
}

/**
 * Chunked file upload for larger files
 */
async function uploadFileInChunks(
  file: File,
  type: 'video' | 'image',
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> {
  if (!isAuthenticated()) {
    throw new Error('Authentication required');
  }

  const chunkSize = STORAGE_CONFIG.chunkSize;
  const totalChunks = Math.ceil(file.size / chunkSize);
  let uploadedBytes = 0;

  console.log(`Starting chunked upload: ${totalChunks} chunks of ${chunkSize} bytes each`);

  for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
    const start = chunkIndex * chunkSize;
    const end = Math.min(start + chunkSize, file.size);
    const chunk = file.slice(start, end);

    const formData = new FormData();
    formData.append('file', chunk, file.name);
    formData.append('type', type);
    formData.append('chunkIndex', chunkIndex.toString());
    formData.append('totalChunks', totalChunks.toString());

    try {
      const response = await uploadChunk(formData);
      uploadedBytes += chunk.size;

      // Update progress
      if (onProgress) {
        onProgress({
          loaded: uploadedBytes,
          total: file.size,
          percentage: Math.round((uploadedBytes / file.size) * 100)
        });
      }

      // If this was the last chunk, return the final result
      if (chunkIndex === totalChunks - 1) {
        return response;
      }

      console.log(`Uploaded chunk ${chunkIndex + 1}/${totalChunks}`);
    } catch (error) {
      console.error(`Failed to upload chunk ${chunkIndex}:`, error);
      throw new Error(`Chunk upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  throw new Error('Chunked upload completed but no final response received');
}

/**
 * Upload a single chunk
 */
function uploadChunk(formData: FormData): Promise<UploadResult> {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText);
          if (response.success) {
            resolve(response);
          } else {
            reject(new Error(response.error || 'Chunk upload failed'));
          }
        } catch (error) {
          reject(new Error('Invalid response from server'));
        }
      } else {
        try {
          const response = JSON.parse(xhr.responseText);
          reject(new Error(response.error || `Chunk upload failed with status ${xhr.status}`));
        } catch {
          reject(new Error(`Chunk upload failed with status ${xhr.status}`));
        }
      }
    });

    xhr.addEventListener('error', () => {
      reject(new Error('Network error during chunk upload'));
    });

    xhr.addEventListener('timeout', () => {
      reject(new Error('Chunk upload timeout'));
    });

    xhr.open('POST', STORAGE_CONFIG.baseUrl + STORAGE_CONFIG.uploadEndpoint);
    xhr.withCredentials = true; // Include cookies for session-based auth
    xhr.timeout = 60000; // 1 minute per chunk
    xhr.send(formData);
  });
}

/**
 * Generate public URL for a file
 */
export function getPublicUrl(path: string, type: 'video' | 'image'): string {
  const basePath = type === 'video' ? STORAGE_CONFIG.videosPath : STORAGE_CONFIG.thumbnailsPath;
  return `${STORAGE_CONFIG.baseUrl}${basePath}/${path}`;
}

/**
 * Validate if a URL is from our storage
 */
export function isStorageUrl(url: string): boolean {
  return url.includes(STORAGE_CONFIG.baseUrl) && 
         (url.includes(STORAGE_CONFIG.videosPath) || url.includes(STORAGE_CONFIG.thumbnailsPath));
}

/**
 * Extract file path from storage URL
 */
export function extractPathFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    
    if (pathname.includes(STORAGE_CONFIG.videosPath)) {
      return pathname.replace(STORAGE_CONFIG.videosPath + '/', '');
    } else if (pathname.includes(STORAGE_CONFIG.thumbnailsPath)) {
      return pathname.replace(STORAGE_CONFIG.thumbnailsPath + '/', '');
    }
    
    return null;
  } catch {
    return null;
  }
}

/**
 * Check if storage service is available
 */
export async function checkStorageHealth(): Promise<boolean> {
  try {
    const response = await fetch(STORAGE_CONFIG.baseUrl + '/api/upload.php', {
      method: 'GET',
      credentials: 'include',
      timeout: 5000
    });
    return response.ok;
  } catch {
    return false;
  }
}

/**
 * Get storage configuration
 */
export function getStorageConfig() {
  return { ...STORAGE_CONFIG };
}
