#!/bin/bash

# Extract and upload media files to Namecheap server
# This script extracts video.zip and thumbnail.zip and uploads them to your server

echo "🎬 Extracting and uploading media files to Namecheap server..."

# Create temporary directories
mkdir -p temp-media/videos
mkdir -p temp-media/thumbnails

# Extract video files
echo "📹 Extracting videos..."
unzip -q video.zip -d temp-media/videos/
echo "✅ Videos extracted"

# Extract thumbnail files  
echo "🖼️ Extracting thumbnails..."
unzip -q thumbnail.zip -d temp-media/thumbnails/
echo "✅ Thumbnails extracted"

# Create media directory structure on server
echo "📁 Creating media directories on server..."
scp -P 21098 -r temp-media/* <EMAIL>:public_html/media/

echo "🚀 Upload complete!"
echo "Your media files are now available at:"
echo "- Videos: https://www.bluefilmx.com/media/videos/"
echo "- Thumbnails: https://www.bluefilmx.com/media/thumbnails/"

# Clean up
rm -rf temp-media/

echo "✅ Media upload completed successfully!"
