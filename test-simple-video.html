<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Video Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        video {
            width: 100%;
            max-width: 600px;
            height: auto;
            background-color: #000;
            border-radius: 5px;
            margin: 20px 0;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #2a2a2a;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .log {
            background-color: #111;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .error { color: #ef4444; }
        .success { color: #4ade80; }
        .info { color: #60a5fa; }
    </style>
</head>
<body>
    <h1>🎬 Simple Video Test</h1>
    <p>Testing video playback with minimal setup</p>

    <div class="test-section">
        <h3>Test Video URLs</h3>
        <button onclick="testVideo('https://www.bluefilmx.com/media/videos/1749736915_79c6e6c523b72d5e_2025-05-18_23_11_46.mp4')">Test Uploaded Video</button>
        <button onclick="testVideo('https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4')">Test Reference Video (Big Buck Bunny)</button>
        <button onclick="testVideo('https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4')">Test Sample Video</button>
    </div>

    <div class="test-section">
        <h3>Video Player</h3>
        <video id="testVideo" controls preload="metadata">
            Your browser does not support the video tag.
        </video>
    </div>

    <div class="test-section">
        <h3>Debug Log</h3>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        const video = document.getElementById('testVideo');
        const logDiv = document.getElementById('log');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            logDiv.innerHTML = '';
        }

        // Set up video event listeners
        video.addEventListener('loadstart', () => {
            log('Video load started', 'info');
        });

        video.addEventListener('loadedmetadata', () => {
            log(`Video metadata loaded - Duration: ${video.duration}s, Size: ${video.videoWidth}x${video.videoHeight}`, 'success');
        });

        video.addEventListener('loadeddata', () => {
            log('Video data loaded', 'success');
        });

        video.addEventListener('canplay', () => {
            log('Video can start playing', 'success');
        });

        video.addEventListener('canplaythrough', () => {
            log('Video can play through without buffering', 'success');
        });

        video.addEventListener('error', (e) => {
            const error = video.error;
            let errorMessage = 'Unknown error';
            
            if (error) {
                switch (error.code) {
                    case error.MEDIA_ERR_ABORTED:
                        errorMessage = 'Video playback aborted by user';
                        break;
                    case error.MEDIA_ERR_NETWORK:
                        errorMessage = 'Network error occurred while loading video';
                        break;
                    case error.MEDIA_ERR_DECODE:
                        errorMessage = 'Video format not supported by this browser (decode error)';
                        break;
                    case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                        errorMessage = 'Video source format not supported';
                        break;
                    default:
                        errorMessage = `Unknown error (code: ${error.code})`;
                }
                
                if (error.message) {
                    errorMessage += ` - ${error.message}`;
                }
            }
            
            log(`VIDEO ERROR: ${errorMessage}`, 'error');
        });

        video.addEventListener('stalled', () => {
            log('Video download stalled', 'error');
        });

        video.addEventListener('suspend', () => {
            log('Video download suspended', 'info');
        });

        video.addEventListener('abort', () => {
            log('Video loading aborted', 'error');
        });

        video.addEventListener('emptied', () => {
            log('Video element emptied', 'info');
        });

        video.addEventListener('progress', () => {
            if (video.buffered.length > 0) {
                const buffered = video.buffered.end(video.buffered.length - 1);
                const duration = video.duration || 0;
                const percent = duration > 0 ? (buffered / duration * 100).toFixed(1) : 0;
                log(`Video buffering: ${percent}% (${buffered.toFixed(1)}s of ${duration.toFixed(1)}s)`, 'info');
            }
        });

        function testVideo(url) {
            log(`Testing video: ${url}`, 'info');
            
            // Clear previous video
            video.src = '';
            video.load();
            
            // Set new source
            setTimeout(() => {
                video.src = url;
                video.load();
                log(`Video source set to: ${url}`, 'info');
            }, 100);
        }

        // Test browser video format support
        function testBrowserSupport() {
            log('=== Browser Video Format Support ===', 'info');
            
            const testVideo = document.createElement('video');
            const formats = [
                'video/mp4; codecs="avc1.42E01E"',
                'video/mp4; codecs="avc1.4D401E"', 
                'video/mp4; codecs="avc1.64001E"',
                'video/mp4',
                'video/webm; codecs="vp8"',
                'video/webm; codecs="vp9"',
                'video/webm',
                'video/ogg; codecs="theora"'
            ];
            
            formats.forEach(format => {
                const support = testVideo.canPlayType(format);
                const supportText = support === 'probably' ? 'PROBABLY' : 
                                  support === 'maybe' ? 'MAYBE' : 'NO';
                log(`${format}: ${supportText}`, support === 'probably' ? 'success' : 
                    support === 'maybe' ? 'info' : 'error');
            });
            
            log(`User Agent: ${navigator.userAgent}`, 'info');
        }

        // Auto-test on load
        window.addEventListener('load', () => {
            log('Simple Video Test Page Loaded', 'info');
            testBrowserSupport();
        });
    </script>
</body>
</html>
