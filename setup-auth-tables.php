<?php
require_once 'namecheap-api/config/database.php';

try {
    $db = Database::getInstance()->getConnection();
    
    echo "Creating authentication tables...\n";
    
    // Create login_attempts table
    $db->exec("
        CREATE TABLE IF NOT EXISTS login_attempts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id VARCHAR(36) NOT NULL,
            success BOOLEAN NOT NULL DEFAULT 0,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
    ");
    echo "✅ login_attempts table created\n";
    
    // Create user_sessions table
    $db->exec("
        CREATE TABLE IF NOT EXISTS user_sessions (
            id VARCHAR(36) PRIMARY KEY,
            user_id VARCHAR(36) NOT NULL,
            expires_at DATETIME NOT NULL,
            device_info TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREI<PERSON><PERSON> KEY (user_id) REFERENCES users(id)
        )
    ");
    echo "✅ user_sessions table created\n";
    
    // Create email_verifications table
    $db->exec("
        CREATE TABLE IF NOT EXISTS email_verifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id VARCHAR(36) NOT NULL,
            token VARCHAR(255) NOT NULL,
            expires_at DATETIME NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
    ");
    echo "✅ email_verifications table created\n";
    
    // Create rate_limits table
    $db->exec("
        CREATE TABLE IF NOT EXISTS rate_limits (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            identifier VARCHAR(255) NOT NULL,
            action VARCHAR(50) NOT NULL,
            attempts INTEGER DEFAULT 1,
            window_start DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(identifier, action)
        )
    ");
    echo "✅ rate_limits table created\n";
    
    // Add username column to users table if it doesn't exist
    try {
        $db->exec("ALTER TABLE users ADD COLUMN username VARCHAR(50) UNIQUE");
        echo "✅ username column added to users table\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'duplicate column name') !== false) {
            echo "ℹ️ username column already exists\n";
        } else {
            throw $e;
        }
    }
    
    echo "\n🎉 All authentication tables created successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error setting up tables: " . $e->getMessage() . "\n";
    exit(1);
}
?>