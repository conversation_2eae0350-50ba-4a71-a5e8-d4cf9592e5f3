<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>BlueFilmX CORS Fix Test</h1>
    <p>This page tests the API endpoints to verify CORS is working correctly with credentials.</p>

    <div class="test-section">
        <h3>Test 1: Videos API (GET)</h3>
        <button onclick="testVideosAPI()">Test Videos API</button>
        <div id="videos-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Auth API (GET - Check Current User)</h3>
        <button onclick="testAuthAPI()">Test Auth API</button>
        <div id="auth-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Categories API (GET)</h3>
        <button onclick="testCategoriesAPI()">Test Categories API</button>
        <div id="categories-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 4: Preflight OPTIONS Request</h3>
        <button onclick="testPreflightRequest()">Test Preflight</button>
        <div id="preflight-result"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://www.bluefilmx.com/api';

        function setResult(elementId, message, type = 'loading') {
            const element = document.getElementById(elementId);
            element.className = `test-section ${type}`;
            element.innerHTML = message;
        }

        async function testVideosAPI() {
            setResult('videos-result', 'Testing videos API...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/videos.php?limit=3`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                setResult('videos-result', `
                    <strong>✅ Success!</strong><br>
                    Status: ${response.status}<br>
                    Videos found: ${data.data?.videos?.length || 0}<br>
                    <pre>${JSON.stringify(data, null, 2).substring(0, 500)}...</pre>
                `, 'success');

            } catch (error) {
                setResult('videos-result', `
                    <strong>❌ Error:</strong><br>
                    ${error.message}<br>
                    <small>Check browser console for more details</small>
                `, 'error');
                console.error('Videos API Error:', error);
            }
        }

        async function testAuthAPI() {
            setResult('auth-result', 'Testing auth API...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth.php`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.status === 401) {
                    setResult('auth-result', `
                        <strong>✅ Success!</strong><br>
                        Status: ${response.status} (Expected - No user logged in)<br>
                        Response: ${data.error || 'Authentication required'}<br>
                        <small>CORS is working - 401 is expected when not logged in</small>
                    `, 'success');
                } else if (response.ok) {
                    setResult('auth-result', `
                        <strong>✅ Success!</strong><br>
                        Status: ${response.status}<br>
                        User logged in: ${data.data?.user?.username || 'Unknown'}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                setResult('auth-result', `
                    <strong>❌ Error:</strong><br>
                    ${error.message}<br>
                    <small>Check browser console for more details</small>
                `, 'error');
                console.error('Auth API Error:', error);
            }
        }

        async function testCategoriesAPI() {
            setResult('categories-result', 'Testing categories API...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/categories.php`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                setResult('categories-result', `
                    <strong>✅ Success!</strong><br>
                    Status: ${response.status}<br>
                    Categories found: ${data.data?.length || 0}<br>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `, 'success');

            } catch (error) {
                setResult('categories-result', `
                    <strong>❌ Error:</strong><br>
                    ${error.message}<br>
                    <small>Check browser console for more details</small>
                `, 'error');
                console.error('Categories API Error:', error);
            }
        }

        async function testPreflightRequest() {
            setResult('preflight-result', 'Testing preflight OPTIONS request...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/videos.php`, {
                    method: 'OPTIONS',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                setResult('preflight-result', `
                    <strong>✅ Success!</strong><br>
                    Status: ${response.status}<br>
                    CORS Headers:<br>
                    - Access-Control-Allow-Origin: ${response.headers.get('Access-Control-Allow-Origin')}<br>
                    - Access-Control-Allow-Credentials: ${response.headers.get('Access-Control-Allow-Credentials')}<br>
                    - Access-Control-Allow-Methods: ${response.headers.get('Access-Control-Allow-Methods')}<br>
                `, 'success');

            } catch (error) {
                setResult('preflight-result', `
                    <strong>❌ Error:</strong><br>
                    ${error.message}<br>
                    <small>Check browser console for more details</small>
                `, 'error');
                console.error('Preflight Error:', error);
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            console.log('🧪 Starting CORS tests...');
            console.log('Current origin:', window.location.origin);
        });
    </script>
</body>
</html>
