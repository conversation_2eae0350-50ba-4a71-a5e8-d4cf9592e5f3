<?php
/**
 * MySQL Authentication API for cPanel Deployment
 * Converts Node.js auth-server.js functionality to PHP
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/config/database.php';

class MySQLAuth {
    private $db;
    
    public function __construct() {
        $database = Database::getInstance();
        $this->db = $database->getConnection();
    }
    
    public function signUp($email, $password, $name) {
        try {
            // Check if user already exists
            $stmt = $this->db->prepare("SELECT id FROM user WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                return ['success' => false, 'error' => 'User already exists'];
            }
            
            // Generate user ID and timestamps
            $userId = $this->generateId();
            $timestamp = time() * 1000; // Convert to milliseconds
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert user
            $stmt = $this->db->prepare("
                INSERT INTO user (id, email, emailVerified, name, createdAt, updatedAt) 
                VALUES (?, ?, 0, ?, ?, ?)
            ");
            $stmt->execute([$userId, $email, $name, $timestamp, $timestamp]);
            
            // Insert account with password
            $accountId = $this->generateId();
            $stmt = $this->db->prepare("
                INSERT INTO account (id, accountId, providerId, userId, password, createdAt, updatedAt) 
                VALUES (?, ?, 'credential', ?, ?, ?, ?)
            ");
            $stmt->execute([$accountId, $email, $userId, $hashedPassword, $timestamp, $timestamp]);
            
            return ['success' => true, 'user' => ['id' => $userId, 'email' => $email, 'name' => $name]];
            
        } catch (Exception $e) {
            error_log('SignUp error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Registration failed'];
        }
    }
    
    public function signIn($email, $password) {
        try {
            // Get user and password
            $stmt = $this->db->prepare("
                SELECT u.id, u.email, u.name, a.password 
                FROM user u 
                JOIN account a ON u.id = a.userId 
                WHERE u.email = ? AND a.providerId = 'credential'
            ");
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user || !password_verify($password, $user['password'])) {
                return ['success' => false, 'error' => 'Invalid credentials'];
            }
            
            // Create session
            $sessionId = $this->generateId();
            $token = $this->generateToken();
            $timestamp = time() * 1000;
            $expiresAt = $timestamp + (7 * 24 * 60 * 60 * 1000); // 7 days
            
            $stmt = $this->db->prepare("
                INSERT INTO session (id, expiresAt, token, createdAt, updatedAt, userId, ipAddress, userAgent) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $sessionId, $expiresAt, $token, $timestamp, $timestamp, 
                $user['id'], $_SERVER['REMOTE_ADDR'] ?? '', $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
            
            return [
                'success' => true, 
                'token' => $token,
                'user' => ['id' => $user['id'], 'email' => $user['email'], 'name' => $user['name']]
            ];
            
        } catch (Exception $e) {
            error_log('SignIn error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Login failed'];
        }
    }
    
    public function getSession($token) {
        try {
            $stmt = $this->db->prepare("
                SELECT s.userId, s.expiresAt, u.email, u.name 
                FROM session s 
                JOIN user u ON s.userId = u.id 
                WHERE s.token = ? AND s.expiresAt > ?
            ");
            $stmt->execute([$token, time() * 1000]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$session) {
                return ['success' => false, 'error' => 'Invalid or expired session'];
            }
            
            return [
                'success' => true,
                'user' => ['id' => $session['userId'], 'email' => $session['email'], 'name' => $session['name']]
            ];
            
        } catch (Exception $e) {
            error_log('GetSession error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Session validation failed'];
        }
    }
    
    public function signOut($token) {
        try {
            $stmt = $this->db->prepare("DELETE FROM session WHERE token = ?");
            $stmt->execute([$token]);
            return ['success' => true];
        } catch (Exception $e) {
            error_log('SignOut error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Logout failed'];
        }
    }
    
    private function generateId() {
        return bin2hex(random_bytes(16));
    }
    
    private function generateToken() {
        return bin2hex(random_bytes(32));
    }
}

// Handle requests
try {
    $auth = new MySQLAuth();
    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true) ?? [];
    $action = $_GET['action'] ?? '';
    
    switch ($method) {
        case 'POST':
            switch ($action) {
                case 'signup':
                    $result = $auth->signUp($input['email'] ?? '', $input['password'] ?? '', $input['name'] ?? '');
                    break;
                case 'signin':
                    $result = $auth->signIn($input['email'] ?? '', $input['password'] ?? '');
                    break;
                case 'signout':
                    $token = $input['token'] ?? $_SERVER['HTTP_AUTHORIZATION'] ?? '';
                    $token = str_replace('Bearer ', '', $token);
                    $result = $auth->signOut($token);
                    break;
                default:
                    $result = ['success' => false, 'error' => 'Invalid action'];
            }
            break;
            
        case 'GET':
            if ($action === 'session') {
                $token = $_GET['token'] ?? $_SERVER['HTTP_AUTHORIZATION'] ?? '';
                $token = str_replace('Bearer ', '', $token);
                $result = $auth->getSession($token);
            } else {
                $result = ['success' => false, 'error' => 'Invalid action'];
            }
            break;
            
        default:
            $result = ['success' => false, 'error' => 'Method not allowed'];
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    error_log('Auth API error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}
?>