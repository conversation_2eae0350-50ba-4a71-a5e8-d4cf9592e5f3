<?php
require_once 'config/database.php';

// Auth helper functions
function getCurrentUser() {
    if (!isset($_SESSION)) {
        session_start();
    }
    return $_SESSION['user_id'] ?? null;
}

function requireAuth() {
    $userId = getCurrentUser();
    if (!$userId) {
        errorResponse('Authentication required', 401);
    }
    return $userId;
}

setCORSHeaders();

$database = new Database();
$db = $database->getConnection();

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get the request method
$method = $_SERVER['REQUEST_METHOD'];

// For this API, we'll handle video ID through request body or query params
$videoId = null;

switch ($method) {
    case 'GET':
        getUserFavorites($db);
        break;

    case 'POST':
        $input = json_decode(file_get_contents('php://input'), true);
        if (isset($input['video_id'])) {
            addToFavorites($db, $input['video_id']);
        } else {
            errorResponse('Video ID required');
        }
        break;

    case 'DELETE':
        $input = json_decode(file_get_contents('php://input'), true);
        if (isset($input['video_id'])) {
            removeFromFavorites($db, $input['video_id']);
        } else {
            errorResponse('Video ID required');
        }
        break;

    default:
        errorResponse('Method not allowed', 405);
}

function getUserFavorites($db) {
    $userId = requireAuth();
    error_log("Favorites: User ID = " . $userId);

    try {
        // Get user's favorite video IDs
        $favQuery = "SELECT video_id FROM favorites WHERE user_id = :user_id ORDER BY created_at DESC";
        $favStmt = $db->prepare($favQuery);
        $favStmt->bindParam(':user_id', $userId);
        $favStmt->execute();
        $favoriteIds = $favStmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($favoriteIds)) {
            successResponse([
                'videos' => [],
                'count' => 0
            ]);
            return;
        }
        
        // Get video details for favorites
        $placeholders = str_repeat('?,', count($favoriteIds) - 1) . '?';
        $query = "
            SELECT 
                v.*,
                p.username,
                p.avatar_url as user_avatar
            FROM videos v
            LEFT JOIN profiles p ON v.user_id = p.id
            WHERE v.id IN ($placeholders)
            ORDER BY v.created_at DESC
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute($favoriteIds);
        $videos = $stmt->fetchAll();
        
        // Convert boolean fields
        foreach ($videos as &$video) {
            $video['is_hd'] = (bool)$video['is_hd'];
            $video['views'] = (int)$video['views'];
            $video['likes'] = (int)$video['likes'];
            $video['duration'] = (int)$video['duration'];
        }
        
        successResponse([
            'videos' => $videos,
            'count' => count($videos)
        ]);
        
    } catch (Exception $e) {
        error_log("Error fetching favorites: " . $e->getMessage());
        errorResponse('Failed to fetch favorites', 500);
    }
}

function addToFavorites($db, $videoId) {
    $userId = requireAuth();
    
    if (!$videoId) {
        errorResponse('Video ID is required');
    }
    
    try {
        // Check if video exists
        $checkVideoQuery = "SELECT id FROM videos WHERE id = :video_id";
        $checkVideoStmt = $db->prepare($checkVideoQuery);
        $checkVideoStmt->bindParam(':video_id', $videoId);
        $checkVideoStmt->execute();
        
        if (!$checkVideoStmt->fetch()) {
            errorResponse('Video not found', 404);
        }
        
        // Check if already in favorites
        $checkQuery = "SELECT id FROM favorites WHERE user_id = :user_id AND video_id = :video_id";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->bindParam(':user_id', $userId);
        $checkStmt->bindParam(':video_id', $videoId);
        $checkStmt->execute();
        
        if ($checkStmt->fetch()) {
            // Already in favorites
            successResponse(['message' => 'Video already in favorites']);
            return;
        }
        
        // Add to favorites
        $insertQuery = "INSERT INTO favorites (id, user_id, video_id, created_at) VALUES (:id, :user_id, :video_id, NOW())";
        $insertStmt = $db->prepare($insertQuery);
        
        $favoriteId = generateUUID();
        $insertStmt->bindParam(':id', $favoriteId);
        $insertStmt->bindParam(':user_id', $userId);
        $insertStmt->bindParam(':video_id', $videoId);
        
        $insertStmt->execute();
        
        successResponse(['message' => 'Video added to favorites']);
        
    } catch (Exception $e) {
        error_log("Error adding to favorites: " . $e->getMessage());
        errorResponse('Failed to add to favorites', 500);
    }
}

function removeFromFavorites($db, $videoId) {
    $userId = requireAuth();
    
    if (!$videoId) {
        errorResponse('Video ID is required');
    }
    
    try {
        $query = "DELETE FROM favorites WHERE user_id = :user_id AND video_id = :video_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':video_id', $videoId);
        
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            successResponse(['message' => 'Video removed from favorites']);
        } else {
            successResponse(['message' => 'Video was not in favorites']);
        }
        
    } catch (Exception $e) {
        error_log("Error removing from favorites: " . $e->getMessage());
        errorResponse('Failed to remove from favorites', 500);
    }
}
?>
