<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Simple debug endpoint to test API accessibility
$debug_info = [
    'success' => true,
    'message' => 'Debug endpoint working',
    'timestamp' => date('Y-m-d H:i:s'),
    'server_info' => [
        'php_version' => phpversion(),
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'Unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown'
    ],
    'files_check' => [
        'videos_simple_exists' => file_exists(__DIR__ . '/videos-simple.php'),
        'videos_exists' => file_exists(__DIR__ . '/videos.php'),
        'config_exists' => file_exists(__DIR__ . '/config.php')
    ]
];

echo json_encode($debug_info, JSON_PRETTY_PRINT);
?>
