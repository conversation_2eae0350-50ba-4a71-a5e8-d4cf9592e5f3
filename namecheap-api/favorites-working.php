<?php
require_once 'config/database.php';

// Set CORS headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Start session
if (!isset($_SESSION)) {
    session_start();
}

// Helper functions
function getCurrentUser() {
    return $_SESSION['user_id'] ?? null;
}

function requireAuth() {
    $userId = getCurrentUser();
    if (!$userId) {
        http_response_code(401);
        echo json_encode(['success' => false, 'error' => 'Authentication required']);
        exit();
    }
    return $userId;
}

function successResponse($data) {
    echo json_encode(['success' => true, 'data' => $data]);
    exit();
}

function errorResponse($message, $code = 400) {
    http_response_code($code);
    echo json_encode(['success' => false, 'error' => $message]);
    exit();
}

function generateUUID() {
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Get the request method
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        getUserFavorites($db);
        break;
    
    case 'POST':
        $input = json_decode(file_get_contents('php://input'), true);
        if (isset($input['video_id'])) {
            addToFavorites($db, $input['video_id']);
        } else {
            errorResponse('Video ID required');
        }
        break;
    
    case 'DELETE':
        $input = json_decode(file_get_contents('php://input'), true);
        if (isset($input['video_id'])) {
            removeFromFavorites($db, $input['video_id']);
        } else {
            errorResponse('Video ID required');
        }
        break;
    
    default:
        errorResponse('Method not allowed', 405);
}

function getUserFavorites($db) {
    $userId = requireAuth();
    
    try {
        // Get user's favorite video IDs
        $favQuery = "SELECT video_id FROM favorites WHERE user_id = ? ORDER BY created_at DESC";
        $favStmt = $db->prepare($favQuery);
        $favStmt->execute([$userId]);
        $favoriteIds = $favStmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($favoriteIds)) {
            successResponse([
                'videos' => [],
                'count' => 0
            ]);
            return;
        }
        
        // Get video details for favorites
        $placeholders = str_repeat('?,', count($favoriteIds) - 1) . '?';
        $query = "
            SELECT 
                v.*,
                p.username,
                p.avatar_url as user_avatar
            FROM videos v
            LEFT JOIN profiles p ON v.user_id = p.id
            WHERE v.id IN ($placeholders)
            ORDER BY v.created_at DESC
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute($favoriteIds);
        $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Convert boolean fields
        foreach ($videos as &$video) {
            $video['is_hd'] = (bool)$video['is_hd'];
            $video['views'] = (int)$video['views'];
            $video['likes'] = (int)$video['likes'];
            $video['duration'] = (int)$video['duration'];
        }
        
        successResponse([
            'videos' => $videos,
            'count' => count($videos)
        ]);
        
    } catch (Exception $e) {
        error_log("Error fetching favorites: " . $e->getMessage());
        errorResponse('Failed to fetch favorites', 500);
    }
}

function addToFavorites($db, $videoId) {
    $userId = requireAuth();
    
    if (!$videoId) {
        errorResponse('Video ID is required');
    }
    
    try {
        // Check if video exists
        $checkVideoQuery = "SELECT id FROM videos WHERE id = ?";
        $checkVideoStmt = $db->prepare($checkVideoQuery);
        $checkVideoStmt->execute([$videoId]);
        
        if (!$checkVideoStmt->fetch()) {
            errorResponse('Video not found', 404);
        }
        
        // Check if already in favorites
        $checkQuery = "SELECT id FROM favorites WHERE user_id = ? AND video_id = ?";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->execute([$userId, $videoId]);
        
        if ($checkStmt->fetch()) {
            // Already in favorites
            successResponse(['message' => 'Video already in favorites']);
            return;
        }
        
        // Add to favorites
        $insertQuery = "INSERT INTO favorites (id, user_id, video_id, created_at) VALUES (?, ?, ?, NOW())";
        $insertStmt = $db->prepare($insertQuery);
        
        $favoriteId = generateUUID();
        $insertStmt->execute([$favoriteId, $userId, $videoId]);
        
        successResponse(['message' => 'Video added to favorites']);
        
    } catch (Exception $e) {
        error_log("Error adding to favorites: " . $e->getMessage());
        errorResponse('Failed to add to favorites', 500);
    }
}

function removeFromFavorites($db, $videoId) {
    $userId = requireAuth();
    
    if (!$videoId) {
        errorResponse('Video ID is required');
    }
    
    try {
        $query = "DELETE FROM favorites WHERE user_id = ? AND video_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$userId, $videoId]);
        
        if ($stmt->rowCount() > 0) {
            successResponse(['message' => 'Video removed from favorites']);
        } else {
            successResponse(['message' => 'Video was not in favorites']);
        }
        
    } catch (Exception $e) {
        error_log("Error removing from favorites: " . $e->getMessage());
        errorResponse('Failed to remove from favorites', 500);
    }
}
?>
