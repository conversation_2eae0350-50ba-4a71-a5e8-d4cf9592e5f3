#!/bin/bash

# cPanel Deployment Script for BlueFilmX
# This script prepares your application for cPanel hosting

set -e

echo "🚀 Starting cPanel Deployment Preparation..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create deployment directory
DEPLOY_DIR="cpanel-deployment"
echo -e "${BLUE}📁 Creating deployment directory: $DEPLOY_DIR${NC}"
rm -rf $DEPLOY_DIR
mkdir -p $DEPLOY_DIR

# Step 1: Build the frontend
echo -e "${BLUE}🔨 Building frontend application...${NC}"
npm run build

if [ ! -d "dist" ]; then
    echo -e "${RED}❌ Build failed - dist directory not found${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Frontend build completed${NC}"

# Step 2: Copy frontend files
echo -e "${BLUE}📋 Copying frontend files...${NC}"
cp -r dist/* $DEPLOY_DIR/
echo -e "${GREEN}✅ Frontend files copied${NC}"

# Step 3: Prepare API files
echo -e "${BLUE}🔧 Preparing API files...${NC}"
mkdir -p $DEPLOY_DIR/api

# Copy API files
cp namecheap-api/auth-mysql.php $DEPLOY_DIR/api/
cp namecheap-api/videos.php $DEPLOY_DIR/api/
cp namecheap-api/categories.php $DEPLOY_DIR/api/
cp namecheap-api/upload.php $DEPLOY_DIR/api/
cp namecheap-api/.htaccess $DEPLOY_DIR/api/

# Copy config and utils
cp -r namecheap-api/config $DEPLOY_DIR/api/
cp -r namecheap-api/utils $DEPLOY_DIR/api/

echo -e "${GREEN}✅ API files prepared${NC}"

# Step 4: Create database setup files
echo -e "${BLUE}🗄️ Preparing database files...${NC}"
mkdir -p $DEPLOY_DIR/database
cp scripts/create-auth-schema.sql $DEPLOY_DIR/database/
cp scripts/create-mysql-schema.sql $DEPLOY_DIR/database/ 2>/dev/null || echo "Note: create-mysql-schema.sql not found"
cp mysql-import/import-data.sql $DEPLOY_DIR/database/ 2>/dev/null || echo "Note: import-data.sql not found"
echo -e "${GREEN}✅ Database files prepared${NC}"

# Step 5: Create deployment instructions
echo -e "${BLUE}📝 Creating deployment instructions...${NC}"
cat > $DEPLOY_DIR/DEPLOYMENT_INSTRUCTIONS.md << 'EOF'
# cPanel Deployment Instructions

## 🎯 Quick Deployment Steps

### Step 1: Database Setup (5 minutes)
1. **Create MySQL Database in cPanel:**
   - Database name: `bluerpcm_bluefilmx` (or your preferred name)
   - Username: `bluerpcm_dbuser` (or your preferred username)
   - Password: Choose a strong password
   - Grant ALL PRIVILEGES to the user

2. **Import Database Schema:**
   - Go to phpMyAdmin in cPanel
   - Select your database
   - Import `database/create-auth-schema.sql`
   - Import `database/create-mysql-schema.sql` (if available)
   - Import `database/import-data.sql` (if available)

### Step 2: Update Database Configuration (2 minutes)
1. **Edit `api/config/database.php`:**
   ```php
   $dbname = 'your_actual_database_name';
   $username = 'your_actual_username';
   $password = 'your_actual_password';
   ```

### Step 3: Upload Files (5 minutes)
1. **Upload Frontend Files:**
   - Upload ALL files from this deployment folder (except `api/` and `database/`) to `public_html/`
   - Make sure `index.html` is in the root of `public_html/`

2. **Upload API Files:**
   - Upload the entire `api/` folder to `public_html/api/`
   - Ensure `.htaccess` file is included

### Step 4: Test Deployment (2 minutes)
1. **Test API Endpoints:**
   - Visit: `https://yourdomain.com/api/auth-mysql.php?action=session`
   - Should return JSON (not 404)

2. **Test Website:**
   - Visit: `https://yourdomain.com`
   - Should load without errors
   - Try logging in with test credentials

## 🔧 Configuration Notes

### Database Credentials
Update these in `api/config/database.php`:
- `$host = 'localhost';` (usually correct for cPanel)
- `$dbname = 'your_database_name';`
- `$username = 'your_database_user';`
- `$password = 'your_database_password';`

### CORS Configuration
The API is configured for:
- `https://www.bluefilmx.com`
- Local development URLs

Update in `api/config/database.php` if your domain is different.

## 🚨 Troubleshooting

### If API returns 404:
- Check that `.htaccess` file exists in `api/` folder
- Verify mod_rewrite is enabled on your hosting

### If database connection fails:
- Verify database credentials in `api/config/database.php`
- Check that database user has proper privileges
- Test connection in phpMyAdmin

### If authentication doesn't work:
- Check browser console for CORS errors
- Verify API endpoints return JSON
- Test with browser developer tools

## 📞 Support
If you encounter issues:
1. Check the browser console for errors
2. Test API endpoints directly
3. Verify database connection in phpMyAdmin
4. Check cPanel error logs

---
**Total Deployment Time: ~15 minutes**
EOF

echo -e "${GREEN}✅ Deployment instructions created${NC}"

# Step 6: Create ZIP package
echo -e "${BLUE}📦 Creating deployment package...${NC}"
zip -r cpanel-deployment.zip $DEPLOY_DIR/
echo -e "${GREEN}✅ Deployment package created: cpanel-deployment.zip${NC}"

# Step 7: Display summary
echo -e "\n${GREEN}🎉 Deployment preparation completed!${NC}"
echo -e "\n${YELLOW}📋 What's ready:${NC}"
echo -e "  ✅ Frontend built and ready"
echo -e "  ✅ PHP API files prepared"
echo -e "  ✅ MySQL authentication system"
echo -e "  ✅ Database schema files"
echo -e "  ✅ Deployment instructions"
echo -e "  ✅ ZIP package: cpanel-deployment.zip"

echo -e "\n${BLUE}📁 Deployment structure:${NC}"
echo "cpanel-deployment/"
echo "├── index.html (and other frontend files)"
echo "├── assets/ (CSS, JS, images)"
echo "├── api/"
echo "│   ├── auth-mysql.php (MySQL authentication)"
echo "│   ├── videos.php"
echo "│   ├── categories.php"
echo "│   ├── upload.php"
echo "│   ├── .htaccess"
echo "│   ├── config/database.php"
echo "│   └── utils/"
echo "├── database/"
echo "│   ├── create-auth-schema.sql"
echo "│   └── import-data.sql (if available)"
echo "└── DEPLOYMENT_INSTRUCTIONS.md"

echo -e "\n${YELLOW}🚀 Next steps:${NC}"
echo -e "  1. Extract cpanel-deployment.zip"
echo -e "  2. Follow DEPLOYMENT_INSTRUCTIONS.md"
echo -e "  3. Upload files to cPanel"
echo -e "  4. Configure database"
echo -e "  5. Test your website!"

echo -e "\n${GREEN}🎯 Your BlueFilmX application is ready for cPanel deployment!${NC}"