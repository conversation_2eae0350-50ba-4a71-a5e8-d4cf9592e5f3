import React, { useState, useEffect, useRef, memo } from 'react';
import { Video } from '../../types';
import { useUserPreferencesStore } from '../../stores/userPreferencesStore';
import { useVideoStore } from '../../stores/videoStore';
import { getIsMobile } from '../../utils/deviceDetection';
import { validateStorageUrl } from '../../utils/mediaUtils';

interface NativeVideoPlayerProps {
  video: Video;
  onBack?: () => void;
}

/**
 * NativeVideoPlayer component that uses the browser's native video controls
 * with consistent behavior across iOS and Android devices
 */
const NativeVideoPlayer: React.FC<NativeVideoPlayerProps> = ({ video, onBack }) => {
  // Player state
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(video.duration || 0);
  const [isMobileDevice, setIsMobileDevice] = useState(getIsMobile());
  const [isAndroid, setIsAndroid] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [videoError, setVideoError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  // Debug logging for video URLs
  useEffect(() => {
    console.log('🎬 NativeVideoPlayer loaded with video:', {
      id: video.id,
      title: video.title.substring(0, 50) + '...',
      originalVideoUrl: video.videoUrl,
      validatedVideoUrl: validateStorageUrl(video.videoUrl),
      originalThumbnailUrl: video.thumbnailUrl,
      validatedThumbnailUrl: validateStorageUrl(video.thumbnailUrl)
    });
  }, [video]);

  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const videoContainerRef = useRef<HTMLDivElement>(null);
  const viewCountedRef = useRef<boolean>(false);

  // Stores
  const { updateWatchProgress, getWatchProgress } = useUserPreferencesStore();
  const { incrementVideoViews } = useVideoStore();

  // Detect device platform
  useEffect(() => {
    const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;
    setIsIOS(/iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream);
    setIsAndroid(/Android/i.test(userAgent));
  }, []);

  // Update mobile detection on window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobileDevice(getIsMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (videoRef.current && !isInFullscreen() && videoRef.current.paused) {
        // Video was exited from fullscreen and is paused
        console.log('Exited fullscreen');
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  // Track video progress and save it
  useEffect(() => {
    if (!videoRef.current) return;

    const handleTimeUpdate = () => {
      if (videoRef.current) {
        const currentTime = videoRef.current.currentTime;
        const duration = videoRef.current.duration;
        const progressPercent = (currentTime / duration) * 100;

        setCurrentTime(currentTime);
        setDuration(duration);

        // Save progress every 5 seconds
        if (Math.floor(currentTime) % 5 === 0) {
          updateWatchProgress(video.id, currentTime, duration);
        }

        // Count a view when the user has watched at least 10% of the video
        // but only count it once per session
        if (progressPercent >= 10 && !viewCountedRef.current) {
          incrementVideoViews(video.id);
          viewCountedRef.current = true;
        }
      }
    };

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => {
      setIsPlaying(false);
      // Mark as completed when video ends
      if (duration > 0) {
        updateWatchProgress(video.id, duration, duration);
      }
    };

    const handleLoadedMetadata = () => {
      if (videoRef.current) {
        setDuration(videoRef.current.duration);

        // If we have saved progress, seek to it
        const savedProgress = getWatchProgress(video.id);
        if (savedProgress && !savedProgress.completed) {
          videoRef.current.currentTime = savedProgress.currentTime;
          setCurrentTime(savedProgress.currentTime);
        }
      }
    };

    const handleVideoError = (e: Event) => {
      const target = e.target as HTMLVideoElement;
      const error = target.error;
      if (error) {
        console.error('Video loading error:', {
          code: error.code,
          message: error.message,
          videoSrc: target.src,
          originalVideoUrl: video.videoUrl,
          validatedVideoUrl: validateStorageUrl(video.videoUrl)
        });

        let errorMessage = 'Unknown error';
        switch (error.code) {
          case error.MEDIA_ERR_ABORTED:
            errorMessage = 'Video loading was aborted';
            break;
          case error.MEDIA_ERR_NETWORK:
            errorMessage = 'Network error while loading video';
            break;
          case error.MEDIA_ERR_DECODE:
            errorMessage = 'Video decoding error - format may not be supported';
            break;
          case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
            errorMessage = 'Video format not supported by this browser';
            break;
        }

        setVideoError(`Video failed to load: ${errorMessage}`);
      }
    };

    // Handle fullscreen for Android
    const handleFullscreenClick = () => {
      if (isAndroid && videoRef.current) {
        if (!isInFullscreen()) {
          requestFullscreen(videoRef.current);
        } else {
          exitFullscreen();
        }
      }
    };

    const videoElement = videoRef.current;
    videoElement.addEventListener('timeupdate', handleTimeUpdate);
    videoElement.addEventListener('play', handlePlay);
    videoElement.addEventListener('pause', handlePause);
    videoElement.addEventListener('ended', handleEnded);
    videoElement.addEventListener('loadedmetadata', handleLoadedMetadata);
    videoElement.addEventListener('error', handleVideoError);

    // Add double-tap to fullscreen for Android
    if (isAndroid) {
      videoElement.addEventListener('dblclick', handleFullscreenClick);
    }

    // Fix for iOS fullscreen issues
    if (isIOS) {
      videoElement.addEventListener('webkitbeginfullscreen', () => {
        console.log('iOS entered fullscreen');
      });

      videoElement.addEventListener('webkitendfullscreen', () => {
        console.log('iOS exited fullscreen');
      });
    }

    return () => {
      videoElement.removeEventListener('timeupdate', handleTimeUpdate);
      videoElement.removeEventListener('play', handlePlay);
      videoElement.removeEventListener('pause', handlePause);
      videoElement.removeEventListener('ended', handleEnded);
      videoElement.removeEventListener('loadedmetadata', handleLoadedMetadata);
      videoElement.removeEventListener('error', handleVideoError);

      if (isAndroid) {
        videoElement.removeEventListener('dblclick', handleFullscreenClick);
      }

      if (isIOS) {
        videoElement.removeEventListener('webkitbeginfullscreen', () => {});
        videoElement.removeEventListener('webkitendfullscreen', () => {});
      }

      // Save progress when component unmounts
      if (duration > 0) {
        updateWatchProgress(video.id, currentTime, duration);
      }
    };
  }, [video.id, updateWatchProgress, incrementVideoViews, getWatchProgress, currentTime, duration, isIOS, isAndroid]);

  return (
    <div className="bg-black video-player-container relative z-10">
      {/* Simple container for the video */}
      <div
        ref={videoContainerRef}
        className="relative aspect-video max-h-[70vh] bg-black video-container"
      >
        {videoError ? (
          <div className="w-full h-full flex items-center justify-center bg-gray-800">
            <div className="text-center text-white p-4">
              <h3 className="text-lg font-bold mb-2">Video Unavailable</h3>
              <p className="text-gray-300 text-sm">{videoError}</p>
              <p className="text-gray-400 text-xs mt-2">This video may have been moved or is temporarily unavailable.</p>

              {/* Show retry and download options */}
              <div className="mt-4 space-x-2">
                <button
                  onClick={() => {
                    setVideoError(null);
                    setRetryCount(0);
                    if (videoRef.current) {
                      videoRef.current.load();
                    }
                  }}
                  className="inline-block bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm transition-colors"
                >
                  Retry Video
                </button>
                <a
                  href={validateStorageUrl(video.videoUrl)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors"
                >
                  Download Video File
                </a>
              </div>

              {/* Technical details for debugging */}
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-gray-400 text-xs">Technical Details</summary>
                <div className="mt-2 text-xs text-gray-500 font-mono">
                  <p>Video URL: {validateStorageUrl(video.videoUrl)}</p>
                  <p>Original URL: {video.videoUrl}</p>
                  <p>Error: {videoError}</p>
                  <p>Browser: {navigator.userAgent}</p>
                </div>
              </details>
            </div>
          </div>
        ) : (
          /* Fully Native Video Player with cross-platform attributes */
          <video
            ref={videoRef}
            className="w-full h-full object-contain bg-black native-video-player"
            poster={validateStorageUrl(video.thumbnailUrl)}
            controls
            playsInline
            webkit-playsinline="true"
            x-webkit-airplay="allow"
            preload="metadata"
            autoPlay
            controlsList="nodownload"
            disablePictureInPicture={isMobileDevice}
          >
            {/* Multiple source formats for better compatibility */}
            <source src={validateStorageUrl(video.videoUrl)} type="video/mp4" />
            <source src={validateStorageUrl(video.videoUrl)} type="video/webm" />
            <source src={validateStorageUrl(video.videoUrl)} type="video/ogg" />

            {/* Fallback message */}
            <p className="text-white p-4">
              Your browser does not support the video tag.
              <a href={validateStorageUrl(video.videoUrl)} className="text-blue-400 underline ml-1">
                Download the video file
              </a>
            </p>
          </video>
        )}
      </div>

      {/* Back button below the video for navigation */}
      {onBack && (
        <div className="mt-4 flex justify-start">
          <button
            className="bg-blue-700 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors flex items-center"
            onClick={onBack}
            aria-label="Back to previous page"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
              <path d="M19 12H5M12 19l-7-7 7-7"/>
            </svg>
            Back
          </button>
        </div>
      )}
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default memo(NativeVideoPlayer);
