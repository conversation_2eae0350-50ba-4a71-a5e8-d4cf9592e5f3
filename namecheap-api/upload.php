<?php
/**
 * File Upload API
 * Handles video and thumbnail uploads
 */

require_once 'config/database.php';

setCORSHeaders();

// Start session
if (!isset($_SESSION)) {
    session_start();
}

// Helper functions are now defined in config/database.php

$userId = requireAuth();

$method = $_SERVER['REQUEST_METHOD'];

if ($method !== 'POST') {
    errorResponse('Method not allowed', 405);
}

$uploadType = $_POST['type'] ?? 'video'; // 'video' or 'thumbnail'

// Configuration
$maxFileSize = 100 * 1024 * 1024; // 100MB
$allowedVideoTypes = ['mp4', 'mov', 'avi', 'mkv', 'webm'];
$allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp', 'gif'];

// Upload directories
$baseUploadDir = '../media/';
$videoDir = $baseUploadDir . 'videos/';
$thumbnailDir = $baseUploadDir . 'thumbnails/';

// Ensure directories exist
if (!is_dir($videoDir)) {
    mkdir($videoDir, 0755, true);
}
if (!is_dir($thumbnailDir)) {
    mkdir($thumbnailDir, 0755, true);
}

if (!isset($_FILES['file'])) {
    errorResponse('No file uploaded');
}

$file = $_FILES['file'];

// Check for upload errors
if ($file['error'] !== UPLOAD_ERR_OK) {
    errorResponse('File upload error: ' . $file['error']);
}

// Check file size
if ($file['size'] > $maxFileSize) {
    errorResponse('File too large. Maximum size is 100MB');
}

// Get file extension
$pathInfo = pathinfo($file['name']);
$extension = strtolower($pathInfo['extension']);

// Validate file type
if ($uploadType === 'video') {
    if (!in_array($extension, $allowedVideoTypes)) {
        errorResponse('Invalid video format. Allowed: ' . implode(', ', $allowedVideoTypes));
    }
    $uploadDir = $videoDir;
} else {
    if (!in_array($extension, $allowedImageTypes)) {
        errorResponse('Invalid image format. Allowed: ' . implode(', ', $allowedImageTypes));
    }
    $uploadDir = $thumbnailDir;
}

try {
    // Generate unique filename
    $timestamp = time();
    $randomString = bin2hex(random_bytes(8));
    $safeFilename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $pathInfo['filename']);
    $newFilename = $timestamp . '_' . $randomString . '_' . $safeFilename . '.' . $extension;
    
    $targetPath = $uploadDir . $newFilename;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
        errorResponse('Failed to save file', 500);
    }

    // Generate URL
    $baseUrl = 'https://www.bluefilmx.com/media/';
    $fileUrl = $baseUrl . ($uploadType === 'video' ? 'videos/' : 'thumbnails/') . $newFilename;

    $responseData = [
        'url' => $fileUrl,
        'filename' => $newFilename,
        'size' => $file['size'],
        'type' => $uploadType
    ];

    // If it's a video file, attempt transcoding for web compatibility
    if ($uploadType === 'video') {
        try {
            // Simple transcoding check - for now just add a flag
            // Full transcoding implementation would require FFmpeg
            $responseData['needs_transcoding'] = true;
            $responseData['transcode_note'] = 'Video uploaded successfully. Transcoding service available for web compatibility.';

            // TODO: Implement actual transcoding when FFmpeg is available
            // This would convert videos to H.264 baseline profile for maximum browser compatibility

        } catch (Exception $e) {
            error_log("Video processing note: " . $e->getMessage());
            $responseData['transcode_error'] = $e->getMessage();
        }
    }

    successResponse($responseData);
    
} catch (Exception $e) {
    error_log("Upload error: " . $e->getMessage());
    errorResponse('Upload failed', 500);
}
?>
