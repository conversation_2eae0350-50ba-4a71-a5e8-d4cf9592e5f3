#!/bin/bash

# Extract and upload media files to Namecheap server
# This script extracts video.zip and thumbnail.zip and uploads them to your server

echo "🎬 Extracting and uploading media files to Namecheap server..."

# Create temporary directories
mkdir -p temp-media/videos
mkdir -p temp-media/thumbnails

# Extract video files
echo "📹 Extracting videos..."
unzip -q video.zip -d temp-media/
echo "✅ Videos extracted"

# Extract thumbnail files
echo "🖼️ Extracting thumbnails..."
unzip -q thumbnail.zip -d temp-media/
echo "✅ Thumbnails extracted"

# Move files to proper structure (videos from video2/ folder, thumbnails from tumbo/ folder)
echo "📁 Organizing files..."
if [ -d "temp-media/video2" ]; then
    mv temp-media/video2/* temp-media/videos/ 2>/dev/null || true
    rmdir temp-media/video2 2>/dev/null || true
fi

if [ -d "temp-media/tumbo" ]; then
    mv temp-media/tumbo/* temp-media/thumbnails/ 2>/dev/null || true
    rmdir temp-media/tumbo 2>/dev/null || true
fi

# Remove __MACOSX folders if they exist
rm -rf temp-media/__MACOSX 2>/dev/null || true

# Create media directory on server first
echo "📁 Creating media directory on server..."
ssh -p 21098 <EMAIL> "mkdir -p public_html/media/videos public_html/media/thumbnails"

# Upload videos
echo "📹 Uploading videos to server..."
scp -P 21098 temp-media/videos/* <EMAIL>:public_html/media/videos/

# Upload thumbnails
echo "🖼️ Uploading thumbnails to server..."
scp -P 21098 temp-media/thumbnails/* <EMAIL>:public_html/media/thumbnails/

echo "🚀 Upload complete!"
echo "Your media files are now available at:"
echo "- Videos: https://www.bluefilmx.com/media/videos/"
echo "- Thumbnails: https://www.bluefilmx.com/media/thumbnails/"

# Clean up
rm -rf temp-media/

echo "✅ Media upload completed successfully!"
