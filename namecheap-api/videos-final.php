<?php
/**
 * Final Working Videos API endpoint
 * Returns proper data structure for homepage
 */

require_once 'config/database.php';

// Set CORS headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Start session
if (!isset($_SESSION)) {
    session_start();
}

// Helper functions
function getCurrentUser() {
    return $_SESSION['user_id'] ?? null;
}

function requireAuth() {
    $userId = getCurrentUser();
    if (!$userId) {
        http_response_code(401);
        echo json_encode(['success' => false, 'error' => 'Authentication required']);
        exit();
    }
    return $userId;
}

function generateUUID() {
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

// Get database connection
$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    // Get videos with pagination
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $category = isset($_GET['category']) ? $_GET['category'] : null;
    $search = isset($_GET['search']) ? $_GET['search'] : null;
    $sortBy = isset($_GET['sort']) ? $_GET['sort'] : 'created_at';
    $order = isset($_GET['order']) ? $_GET['order'] : 'DESC';
    $userOnly = isset($_GET['user_only']) ? $_GET['user_only'] : false;

    $offset = ($page - 1) * $limit;
    
    try {
        // Build WHERE conditions
        $whereConditions = [];
        $params = [];
        
        if ($category && $category !== 'all') {
            $whereConditions[] = "v.category = ?";
            $params[] = $category;
        }
        
        if ($search) {
            $whereConditions[] = "(v.title LIKE ? OR v.description LIKE ?)";
            $params[] = '%' . $search . '%';
            $params[] = '%' . $search . '%';
        }

        // Filter by current user if requested
        if ($userOnly) {
            $userId = getCurrentUser();
            if (!$userId) {
                http_response_code(401);
                echo json_encode(['success' => false, 'error' => 'Authentication required for user-only videos']);
                exit();
            }
            $whereConditions[] = "v.user_id = ?";
            $params[] = $userId;
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // Validate sort column
        $allowedSorts = ['created_at', 'views', 'likes', 'title'];
        if (!in_array($sortBy, $allowedSorts)) {
            $sortBy = 'created_at';
        }

        $orderClause = "ORDER BY v.$sortBy $order";
        
        // Get total count
        $countQuery = "SELECT COUNT(*) as total FROM videos v $whereClause";
        $countStmt = $db->prepare($countQuery);
        $countStmt->execute($params);
        $total = $countStmt->fetch()['total'];

        // Get videos with user info
        $query = "
            SELECT
                v.id,
                v.title,
                v.description,
                v.thumbnail_url,
                v.video_url,
                v.duration,
                v.views,
                v.likes,
                v.is_hd,
                v.category,
                v.user_id,
                v.created_at,
                v.updated_at,
                p.username,
                p.avatar_url as user_avatar
            FROM videos v
            LEFT JOIN profiles p ON v.user_id = p.id
            $whereClause
            $orderClause
            LIMIT ? OFFSET ?
        ";

        $stmt = $db->prepare($query);
        $allParams = array_merge($params, [$limit, $offset]);
        $stmt->execute($allParams);
        $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Convert boolean and numeric fields
        foreach ($videos as &$video) {
            $video['is_hd'] = (bool)$video['is_hd'];
            $video['views'] = (int)$video['views'];
            $video['likes'] = (int)$video['likes'];
            $video['duration'] = (int)$video['duration'];
            
            // Ensure all required fields exist
            $video['description'] = $video['description'] ?? '';
            $video['thumbnail_url'] = $video['thumbnail_url'] ?? '';
            $video['username'] = $video['username'] ?? 'Unknown User';
            $video['user_avatar'] = $video['user_avatar'] ?? '';
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'videos' => $videos,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => (int)$total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Error fetching videos: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to fetch videos: ' . $e->getMessage()]);
    }

} elseif ($method === 'POST') {
    // Create video
    $userId = requireAuth();
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['title']) || !isset($input['video_url'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Title and video URL are required']);
        exit();
    }
    
    try {
        $query = "
            INSERT INTO videos (
                id, title, description, thumbnail_url, video_url, 
                duration, category, user_id, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
        ";
        
        $stmt = $db->prepare($query);
        
        $videoId = generateUUID();
        $stmt->execute([
            $videoId,
            $input['title'],
            $input['description'] ?? '',
            $input['thumbnail_url'] ?? '',
            $input['video_url'],
            $input['duration'] ?? 0,
            $input['category'] ?? 'uncategorized',
            $userId
        ]);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $videoId,
                'message' => 'Video created successfully'
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Error creating video: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to create video: ' . $e->getMessage()]);
    }
    
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
}
?>
