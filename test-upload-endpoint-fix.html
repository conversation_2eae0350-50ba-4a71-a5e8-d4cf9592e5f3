<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Endpoint Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #2a2a2a;
        }
        .success {
            border-color: #4ade80;
            background-color: #1a2e1a;
        }
        .error {
            border-color: #ef4444;
            background-color: #2e1a1a;
        }
        .loading {
            border-color: #fbbf24;
            background-color: #2e2a1a;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
        pre {
            background-color: #111;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .endpoint-test {
            background-color: #374151;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
        .endpoint-success { background-color: #1a2e1a; }
        .endpoint-error { background-color: #2e1a1a; }
    </style>
</head>
<body>
    <h1>🔧 Upload Endpoint Fix Test</h1>
    <p>This page tests that the upload endpoint has been fixed from <code>/media/uploads/upload.php</code> to <code>/api/upload.php</code></p>
    <p><strong>BuildId:</strong> 17497366 - Fixed import from uploadStoreNamecheap to uploadStore</p>

    <div class="test-section">
        <h3>Step 1: Test Correct API Endpoint</h3>
        <button onclick="testCorrectEndpoint()">Test /api/upload.php</button>
        <div id="correct-endpoint-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 2: Test Wrong Endpoint (Should Fail)</h3>
        <button onclick="testWrongEndpoint()">Test /media/uploads/upload.php</button>
        <div id="wrong-endpoint-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 3: Frontend Upload Test</h3>
        <button onclick="testFrontendUpload()">Open Upload Page</button>
        <div id="frontend-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 4: Network Monitoring</h3>
        <button onclick="startNetworkMonitoring()">Start Network Monitoring</button>
        <div id="network-result">
            <p>Open browser DevTools (F12) → Network tab to monitor upload requests</p>
            <p><strong>Expected:</strong> Requests should go to <code>/api/upload.php</code></p>
            <p><strong>Not Expected:</strong> Requests to <code>/media/uploads/upload.php</code></p>
        </div>
    </div>

    <script>
        function setResult(elementId, message, type = 'loading') {
            const element = document.getElementById(elementId);
            element.className = `test-section ${type}`;
            element.innerHTML = message;
        }

        async function testCorrectEndpoint() {
            setResult('correct-endpoint-result', 'Testing correct endpoint /api/upload.php...', 'loading');
            
            try {
                const response = await fetch('https://www.bluefilmx.com/api/upload.php', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    setResult('correct-endpoint-result', `
                        <strong>✅ Correct Endpoint Working!</strong><br>
                        <div class="endpoint-test endpoint-success">
                            <strong>URL:</strong> /api/upload.php<br>
                            <strong>Status:</strong> ${response.status} ${response.statusText}<br>
                            <strong>Authenticated:</strong> ${data.authenticated ? 'Yes' : 'No'}<br>
                            <strong>User ID:</strong> ${data.user_id || 'None'}
                        </div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, 'success');
                } else {
                    throw new Error(data.error || 'API failed');
                }

            } catch (error) {
                setResult('correct-endpoint-result', `
                    <strong>❌ Correct Endpoint Failed:</strong><br>
                    <div class="endpoint-test endpoint-error">
                        <strong>URL:</strong> /api/upload.php<br>
                        <strong>Error:</strong> ${error.message}
                    </div>
                `, 'error');
            }
        }

        async function testWrongEndpoint() {
            setResult('wrong-endpoint-result', 'Testing wrong endpoint /media/uploads/upload.php...', 'loading');
            
            try {
                const response = await fetch('https://www.bluefilmx.com/media/uploads/upload.php', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                // If we get here, the endpoint exists (which is bad)
                setResult('wrong-endpoint-result', `
                    <strong>⚠️ Wrong Endpoint Still Exists!</strong><br>
                    <div class="endpoint-test endpoint-error">
                        <strong>URL:</strong> /media/uploads/upload.php<br>
                        <strong>Status:</strong> ${response.status} ${response.statusText}<br>
                        <strong>Issue:</strong> This endpoint should not exist or should not be accessible
                    </div>
                    <p>The frontend should NOT be using this endpoint.</p>
                `, 'error');

            } catch (error) {
                // This is expected - the endpoint should not exist or should fail
                setResult('wrong-endpoint-result', `
                    <strong>✅ Wrong Endpoint Properly Blocked!</strong><br>
                    <div class="endpoint-test endpoint-success">
                        <strong>URL:</strong> /media/uploads/upload.php<br>
                        <strong>Status:</strong> Failed (as expected)<br>
                        <strong>Error:</strong> ${error.message}
                    </div>
                    <p>This is good - the wrong endpoint is not accessible.</p>
                `, 'success');
            }
        }

        function testFrontendUpload() {
            setResult('frontend-result', `
                <strong>🔗 Testing Frontend Upload</strong><br>
                Opening the upload page to test the endpoint fix...<br>
                <br>
                <strong>What to check:</strong><br>
                • No "ERR_HTTP2_PROTOCOL_ERROR" errors<br>
                • No requests to /media/uploads/upload.php<br>
                • Requests should go to /api/upload.php<br>
                • Upload should work without network errors<br>
                <br>
                <strong>How to verify:</strong><br>
                1. Open browser DevTools (F12)<br>
                2. Go to Network tab<br>
                3. Try uploading a file<br>
                4. Check that requests go to /api/upload.php<br>
                <br>
                <a href="https://www.bluefilmx.com/upload" target="_blank" style="color: #60a5fa;">Open Upload Page</a>
            `, 'success');
            
            // Auto-open upload page
            setTimeout(() => {
                window.open('https://www.bluefilmx.com/upload', '_blank');
            }, 1000);
        }

        function startNetworkMonitoring() {
            setResult('network-result', `
                <strong>📡 Network Monitoring Instructions</strong><br>
                <br>
                <strong>Step 1:</strong> Open browser DevTools (F12)<br>
                <strong>Step 2:</strong> Go to Network tab<br>
                <strong>Step 3:</strong> Clear existing requests<br>
                <strong>Step 4:</strong> Go to upload page and try uploading<br>
                <br>
                <strong>✅ Expected Behavior:</strong><br>
                • POST requests to <code>https://www.bluefilmx.com/api/upload.php</code><br>
                • Status 200 responses<br>
                • No ERR_HTTP2_PROTOCOL_ERROR<br>
                <br>
                <strong>❌ Problem Indicators:</strong><br>
                • Requests to <code>/media/uploads/upload.php</code><br>
                • ERR_HTTP2_PROTOCOL_ERROR<br>
                • Network errors during upload<br>
                <br>
                <div style="background-color: #374151; padding: 10px; border-radius: 5px; margin: 10px 0;">
                    <strong>BuildId 17497366 Changes:</strong><br>
                    • Fixed UploadForm.tsx import from uploadStoreNamecheap to uploadStore<br>
                    • Updated namecheapStorage.ts to use /api/upload.php<br>
                    • Removed Bearer token auth, using session-based auth<br>
                </div>
            `, 'success');
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            console.log('🧪 Upload Endpoint Fix Test Page Loaded - BuildId: 17497366');
            setTimeout(() => {
                testCorrectEndpoint();
            }, 500);
            setTimeout(() => {
                testWrongEndpoint();
            }, 1500);
        });
    </script>
</body>
</html>
