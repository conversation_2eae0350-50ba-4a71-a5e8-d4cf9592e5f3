import express from 'express';
import cors from 'cors';
import { createRequire } from 'module';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const require = createRequire(import.meta.url);
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Import the compiled auth module
let auth;
try {
  // Try to import the compiled auth module
  const authModule = await import('./dist/lib/auth.js').catch(() => {
    // Fallback: create a simple handler
    return {
      auth: {
        handler: async (request) => {
          return new Response(JSON.stringify({ message: 'BetterAuth MySQL endpoint ready' }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        }
      }
    };
  });
  auth = authModule.auth;
} catch (error) {
  console.log('Using fallback auth handler:', error.message);
  auth = {
    handler: async (request) => {
      return new Response(JSON.stringify({ message: 'BetterAuth MySQL endpoint ready' }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  };
}

const app = express();
const PORT = 3001;

// Enable CORS
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));

// Parse JSON bodies
app.use(express.json());

// BetterAuth routes - handle all auth paths
app.all('/api/auth/*', async (req, res) => {
  try {
    // Create a proper request object for BetterAuth
    const request = new Request(`http://localhost:3001${req.url}`, {
      method: req.method,
      headers: req.headers,
      body: req.method !== 'GET' && req.method !== 'HEAD' ? JSON.stringify(req.body) : undefined,
    });
    
    const response = await auth.handler(request);
    
    // Set headers from BetterAuth response
    response.headers.forEach((value, key) => {
      res.setHeader(key, value);
    });
    
    // Set status and send response
    res.status(response.status);
    
    const responseText = await response.text();
    if (responseText) {
      res.send(responseText);
    } else {
      res.end();
    }
  } catch (error) {
    console.error('Auth handler error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'BetterAuth API' });
});

app.listen(PORT, () => {
  console.log(`BetterAuth API server running on http://localhost:${PORT}`);
});