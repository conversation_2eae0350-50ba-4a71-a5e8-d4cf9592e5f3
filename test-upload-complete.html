<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Complete Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #2a2a2a;
        }
        .success {
            border-color: #4ade80;
            background-color: #1a2e1a;
        }
        .error {
            border-color: #ef4444;
            background-color: #2e1a1a;
        }
        .loading {
            border-color: #fbbf24;
            background-color: #2e2a1a;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
        button:disabled {
            background-color: #6b7280;
            cursor: not-allowed;
        }
        input[type="file"] {
            background-color: #374151;
            color: white;
            border: 1px solid #6b7280;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px;
            width: 300px;
        }
        input[type="text"] {
            background-color: #374151;
            color: white;
            border: 1px solid #6b7280;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px;
            width: 300px;
        }
        pre {
            background-color: #111;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #374151;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #3b82f6;
            transition: width 0.3s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <h1>📤 Upload Complete Test</h1>
    <p>This page tests the complete video upload functionality including file upload and video creation.</p>

    <div class="test-section">
        <h3>Step 1: Login</h3>
        <input type="text" id="username" placeholder="Username" value="username">
        <input type="password" id="password" placeholder="Password" value="password123">
        <button onclick="testLogin()">Login</button>
        <div id="login-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 2: Test Upload API</h3>
        <button onclick="testUploadAPI()">Test Upload API</button>
        <div id="upload-api-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 3: Test File Upload</h3>
        <input type="file" id="testFile" accept="video/*,image/*">
        <button onclick="testFileUpload()" id="uploadBtn" disabled>Upload Test File</button>
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div id="file-upload-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 4: Test Frontend Upload</h3>
        <button onclick="testFrontendUpload()">Open Upload Page</button>
        <div id="frontend-upload-result"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://www.bluefilmx.com/api';
        let currentUser = null;

        function setResult(elementId, message, type = 'loading') {
            const element = document.getElementById(elementId);
            element.className = `test-section ${type}`;
            element.innerHTML = message;
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            setResult('login-result', `Logging in as ${username}...`, 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth.php?action=login`, {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (data.success && data.data.user) {
                    currentUser = data.data.user;
                    setResult('login-result', `
                        <strong>✅ Login Successful!</strong><br>
                        <strong>User ID:</strong> ${currentUser.id}<br>
                        <strong>Username:</strong> ${currentUser.username}<br>
                        <strong>Approved:</strong> ${currentUser.is_approved ? 'Yes' : 'No'}
                    `, 'success');
                    
                    // Enable upload button
                    document.getElementById('uploadBtn').disabled = false;
                } else {
                    throw new Error('Login failed: ' + (data.error || 'Unknown error'));
                }

            } catch (error) {
                setResult('login-result', `
                    <strong>❌ Login Failed:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        async function testUploadAPI() {
            setResult('upload-api-result', 'Testing Upload API...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/upload.php`, {
                    credentials: 'include',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();
                
                if (data.success) {
                    setResult('upload-api-result', `
                        <strong>✅ Upload API Working!</strong><br>
                        <strong>Authenticated:</strong> ${data.authenticated ? 'Yes' : 'No'}<br>
                        <strong>User ID:</strong> ${data.user_id || 'None'}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, 'success');
                } else {
                    throw new Error(data.error || 'API failed');
                }

            } catch (error) {
                setResult('upload-api-result', `
                    <strong>❌ Upload API Failed:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        async function testFileUpload() {
            const fileInput = document.getElementById('testFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a file first');
                return;
            }

            if (!currentUser) {
                alert('Please login first');
                return;
            }

            setResult('file-upload-result', 'Uploading file...', 'loading');
            
            try {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('type', file.type.startsWith('video/') ? 'video' : 'thumbnail');

                const response = await fetch(`${API_BASE_URL}/upload.php`, {
                    method: 'POST',
                    credentials: 'include',
                    body: formData
                });

                const data = await response.json();
                
                if (data.success) {
                    setResult('file-upload-result', `
                        <strong>✅ File Upload Successful!</strong><br>
                        <strong>URL:</strong> <a href="${data.data.url}" target="_blank" style="color: #60a5fa;">${data.data.url}</a><br>
                        <strong>Filename:</strong> ${data.data.filename}<br>
                        <strong>Size:</strong> ${(data.data.size / 1024 / 1024).toFixed(2)} MB<br>
                        <strong>Type:</strong> ${data.data.type}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, 'success');
                    
                    // Update progress bar
                    document.getElementById('progressFill').style.width = '100%';
                } else {
                    throw new Error(data.error || 'Upload failed');
                }

            } catch (error) {
                setResult('file-upload-result', `
                    <strong>❌ File Upload Failed:</strong><br>
                    ${error.message}
                `, 'error');
                
                // Reset progress bar
                document.getElementById('progressFill').style.width = '0%';
            }
        }

        function testFrontendUpload() {
            setResult('frontend-upload-result', `
                <strong>🔗 Testing Frontend Upload</strong><br>
                Opening the upload page to test the complete frontend integration...<br>
                <br>
                <strong>What to test:</strong><br>
                • Page loads without errors<br>
                • File selection works<br>
                • Upload progress shows<br>
                • No "Network error during upload" messages<br>
                <br>
                <a href="https://www.bluefilmx.com/upload" target="_blank" style="color: #60a5fa;">Open Upload Page</a>
            `, 'success');
            
            // Auto-open upload page
            setTimeout(() => {
                window.open('https://www.bluefilmx.com/upload', '_blank');
            }, 1000);
        }

        // Auto-login on page load for quick testing
        window.addEventListener('load', () => {
            console.log('🧪 Upload Complete Test Page Loaded');
            setTimeout(() => {
                if (confirm('Auto-login for testing?')) {
                    testLogin();
                }
            }, 500);
        });
    </script>
</body>
</html>
