<?php
require_once 'config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if (!isset($_SESSION)) {
    session_start();
}

$userId = $_SESSION['user_id'] ?? null;

if (!$userId) {
    echo json_encode(['success' => false, 'error' => 'Authentication required']);
} else {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $favQuery = "SELECT COUNT(*) as count FROM favorites WHERE user_id = ?";
        $favStmt = $db->prepare($favQuery);
        $favStmt->execute([$userId]);
        $result = $favStmt->fetch();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'videos' => [],
                'count' => (int)$result['count'],
                'user_id' => $userId
            ]
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}
?>
