import{e,X as t,L as s,D as a,f as i,K as r,i as l,V as n,g as o,h as d,P as c,l as u,M as m,m as h,o as x,r as g,s as p,u as b,A as f,I as j,$ as v,T as y,x as N,B as w,E as k,F as P}from"./main-Dmn9Lr65.js";const S=e(((e,t)=>({isUploading:!1,uploadProgress:0,error:null,uploadedVideoUrl:null,uploadedThumbnailUrl:null,uploadVideo:async t=>{const{videoFile:s,thumbnailFile:a,title:i,description:r,category:n}=t;try{const{user:t}=l.getState();if(!t)throw new Error("You must be logged in to upload videos");e({isUploading:!0,uploadProgress:0,error:null,uploadedVideoUrl:null,uploadedThumbnailUrl:null}),e({uploadProgress:5});const c=await d.uploadFile(s,"video");if(!c.success)throw new Error(`Failed to upload video file: ${c.error||"Unknown error"}`);const u=c.data.url;e({uploadProgress:70,uploadedVideoUrl:u});let m=null;if(a){e({uploadProgress:75});try{const t=await d.uploadFile(a,"thumbnail");t.success&&(m=t.data.url,e({uploadedThumbnailUrl:m}))}catch(o){}}e({uploadProgress:85}),e({uploadProgress:90});const h=await d.createVideo({title:i,description:r,video_url:u,thumbnail_url:m||void 0,category:n,duration:0});if(!h.success)throw new Error(`Failed to create video record: ${h.error||"Unknown error"}`);e({uploadProgress:100});const x=h.data.id;return setTimeout((()=>{e({isUploading:!1,uploadProgress:0})}),2e3),x}catch(c){throw e({isUploading:!1,error:c instanceof Error?c.message:"Upload failed",uploadProgress:0}),c}},clearUpload:()=>{e({isUploading:!1,uploadProgress:0,error:null,uploadedVideoUrl:null,uploadedThumbnailUrl:null})},setUploadProgress:t=>{e({uploadProgress:Math.min(100,Math.max(0,t))})}}))),U=[{id:"1",name:"Hot",slug:"hot",icon:t.jsx(s,{size:16,className:"text-red-500"})},{id:"2",name:"Trending",slug:"trending",icon:t.jsx(a,{size:16,className:"text-blue-500"})},{id:"3",name:"New",slug:"new",icon:t.jsx(i,{size:16,className:"text-green-500"})}],T=({onSelect:e,selectedCategory:s="new"})=>t.jsxs("div",{className:"mb-6",children:[t.jsx("label",{className:"block text-sm font-medium text-white mb-2",children:"Category"}),t.jsx("div",{className:"grid grid-cols-3 gap-3",children:U.map((a=>t.jsxs("button",{type:"button",className:"flex flex-col items-center justify-center p-3 rounded-md transition-colors "+(s===a.slug?"bg-gray-700 border-2 border-blue-700":"bg-gray-800 border border-gray-700 hover:border-gray-600"),onClick:()=>e(a.slug),children:[t.jsx("div",{className:"mb-2",children:a.icon}),t.jsx("span",{className:"text-sm font-medium",children:a.name})]},a.id)))})]}),C=()=>{const e=r(),s=n.useRef(null),a=n.useRef(null),[i,d]=n.useState(""),[U,C]=n.useState(""),[L,R]=n.useState("new"),[E,A]=n.useState(""),[z,F]=n.useState(null),[I,V]=n.useState(null),[O,M]=n.useState(null),[D,Y]=n.useState(null),[$,q]=n.useState([]),[H,W]=n.useState(-1),[_,B]=n.useState(!1),[G,K]=n.useState({}),[J,Q]=n.useState(null),[X,Z]=n.useState(null),{uploadVideo:ee,isUploading:te,uploadProgress:se,error:ae}=S(),ie=n.useCallback((e=>{S.setState({error:e})}),[]);n.useEffect((()=>{const e=c();if(Z(e),e.isSupported||ie(`Your browser is missing required features: ${e.missingFeatures.join(", ")}`),u()){m();const e=setInterval((()=>{m()}),3e4);return()=>clearInterval(e)}}),[ie]),n.useEffect((()=>{te&&Q(null)}),[te]);const re=e=>{const t=e.target.files?.[0];if(t){const e=P(t);if(!e.isValid)return void K({...G,video:e.error||"Invalid video file"});F(t);try{const e=URL.createObjectURL(t);M(e)}catch(b){}if(q([]),W(-1),G.video){const e={...G};delete e.video,K(e)}}};return t.jsxs("div",{className:"space-y-6",children:[(u()||/iPad|iPhone|iPod/.test(navigator.userAgent))&&t.jsx("div",{className:"p-4 bg-blue-900/50 border border-blue-700 rounded-lg",children:t.jsxs("div",{className:"flex items-start space-x-3",children:[t.jsx(o,{size:20,className:"text-blue-400 mt-0.5 flex-shrink-0"}),t.jsxs("div",{children:[t.jsx("h3",{className:"text-sm font-medium text-blue-200 mb-1",children:"Mobile Upload Tips"}),t.jsxs("div",{className:"text-sm text-blue-300 space-y-1",children:[t.jsx("p",{children:"• Use MP4 format for best compatibility"}),t.jsx("p",{children:"• Keep files under 60MB for faster uploads"}),t.jsx("p",{children:"• Ensure stable internet connection"}),t.jsx("p",{children:"• Large files will be uploaded in chunks for reliability"}),t.jsx("p",{children:"• Stay on this page during upload to prevent logout"}),t.jsx("p",{children:"• If you experience logout issues during thumbnail selection, try refreshing the page and uploading without auto-generated thumbnails"})]})]})]})}),"undefined"!=typeof navigator&&navigator.connection&&(()=>{const e=navigator.connection;return"slow-2g"===e.effectiveType||"2g"===e.effectiveType?t.jsx("div",{className:"p-4 bg-yellow-900/50 border border-yellow-700 rounded-lg",children:t.jsxs("div",{className:"flex items-start space-x-3",children:[t.jsx(o,{size:20,className:"text-yellow-400 mt-0.5 flex-shrink-0"}),t.jsxs("div",{children:[t.jsx("h3",{className:"text-sm font-medium text-yellow-200 mb-1",children:"Slow Connection Detected"}),t.jsx("p",{className:"text-sm text-yellow-300",children:"Your connection appears to be slow. Consider uploading smaller files or waiting for a better connection."})]})]})}):null})(),X&&!X.isSupported&&t.jsx("div",{className:"p-4 bg-red-900/50 border border-red-700 rounded-lg",children:t.jsxs("div",{className:"flex items-start space-x-3",children:[t.jsx(o,{size:20,className:"text-red-400 mt-0.5 flex-shrink-0"}),t.jsxs("div",{children:[t.jsx("h3",{className:"text-sm font-medium text-red-200 mb-1",children:"Browser Compatibility Issue"}),t.jsxs("p",{className:"text-sm text-red-300",children:["Your browser is missing: ",X.missingFeatures.join(", "),". Please update your browser or try a different one."]})]})]})}),t.jsxs("form",{onSubmit:async t=>{if(t.preventDefault(),!(()=>{const e={};return i.trim()||(e.title="Title is required"),U.trim()||(e.description="Description is required"),z||(e.video="Video file is required"),K(e),0===Object.keys(e).length})())return;if(!z)return;const s=E.split(",").map((e=>e.trim())).filter((e=>e.length>0));try{ie(null);const t=await ee({title:i,description:U,category:L,tags:s,videoFile:z,thumbnailFile:I});t?(Q("Video uploaded successfully! Redirecting to video page..."),setTimeout((()=>{e(`/video/${t}`)}),2e3)):ae||ie("Upload failed. Please try again.")}catch(b){const t=b instanceof Error?b.message:"An unknown error occurred";ie(t)}},className:"space-y-6",children:[t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[t.jsxs("div",{className:"space-y-6",children:[t.jsx(h,{label:"Video Title",placeholder:"Enter a descriptive title",value:i,onChange:e=>d(e.target.value),error:G.title,fullWidth:!0}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-1",children:"Description"}),t.jsx("textarea",{className:"w-full rounded-md bg-gray-800 border border-gray-700 text-white focus:border-orange-500 focus:ring-2 focus:ring-orange-500 focus:ring-opacity-20 p-4 transition-colors placeholder:text-gray-400 min-h-[120px] "+(G.description?"border-red-500":""),placeholder:"Describe your video",value:U,onChange:e=>C(e.target.value)}),G.description&&t.jsx("p",{className:"text-sm text-red-500 mt-1",children:G.description})]}),t.jsx(T,{selectedCategory:L,onSelect:R}),t.jsx(h,{label:"Tags",placeholder:"Enter tags separated by commas",value:E,onChange:e=>A(e.target.value),fullWidth:!0})]}),t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-2",children:"Video File"}),t.jsx("input",{type:"file",ref:s,className:"hidden",accept:"video/*,video/mp4,video/webm,video/quicktime,video/x-msvideo,video/3gpp,video/x-ms-wmv",onChange:re}),z?t.jsxs("div",{className:"relative border rounded-lg overflow-hidden h-[200px]",children:[O&&t.jsx("video",{src:O,className:"w-full h-full object-cover",controls:!0}),t.jsx("button",{type:"button",className:"absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors",onClick:()=>{O&&URL.revokeObjectURL(O),F(null),M(null),s.current&&(s.current.value="")},children:t.jsx(x,{size:16})})]}):t.jsxs("div",{className:"space-y-3",children:[t.jsxs("div",{className:"border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-orange-500 transition-colors h-[200px] border-gray-700",onClick:e=>{e&&(e.preventDefault(),e.stopPropagation()),setTimeout((()=>{s.current?.click()}),100)},children:[t.jsx(g,{size:48,className:"text-gray-500 mb-3"}),t.jsx("p",{className:"text-gray-400 text-center",children:"Click to choose from gallery"}),t.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"MP4, WebM or MOV (max. 50MB)"})]}),(u()||/iPad|iPhone|iPod/.test(navigator.userAgent))&&t.jsx("div",{className:"flex justify-center",children:t.jsx(p,{type:"button",variant:"secondary",size:"sm",leftIcon:t.jsx(b,{size:16}),onClick:e=>{e&&(e.preventDefault(),e.stopPropagation());const t=document.createElement("input");t.type="file",t.accept="video/*",t.capture="environment",t.style.position="absolute",t.style.left="-9999px",t.style.opacity="0",t.onchange=e=>{const s=e.target.files?.[0];s&&re({target:{files:[s]}}),document.body.removeChild(t)},document.body.appendChild(t),setTimeout((()=>{t.click()}),100)},className:"w-full sm:w-auto",children:"Record with Camera"})})]}),G.video&&t.jsx("p",{className:"text-sm text-red-500 mt-1",children:G.video})]}),t.jsxs("div",{children:[t.jsxs("div",{className:"flex justify-between items-center mb-2",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-200",children:"Thumbnail Image (Optional)"}),z&&t.jsx(p,{variant:"secondary",size:"sm",leftIcon:t.jsx(b,{size:16}),onClick:async()=>{if(z)try{B(!0);const{user:t}=l.getState();if(!t)return void K({...G,thumbnail:"Please log in again to generate thumbnails"});if(u()){const e=navigator.deviceMemory;if(e&&e<2)return void K({...G,thumbnail:"Device memory too low for thumbnail generation. Please upload a custom thumbnail instead."});await f(500)}const s=await j((()=>v(z,3)),(()=>{K({...G,thumbnail:"Authentication lost during thumbnail generation. Please refresh the page and try again."})})),{user:a}=l.getState();if(!a)return void K({...G,thumbnail:"Authentication lost during thumbnail generation. Please refresh the page and try again."});let i=[];try{i=s.map((e=>URL.createObjectURL(e))),q(i),u()&&setTimeout((()=>{i.forEach((e=>{try{URL.revokeObjectURL(e)}catch(t){}}))}),3e4)}catch(e){return void K({...G,thumbnail:"Failed to create thumbnail previews. Please try uploading a custom thumbnail."})}if(V(null),Y(null),G.thumbnail){const e={...G};delete e.thumbnail,K(e)}u()&&setTimeout((()=>{const{user:e}=l.getState();e||K({...G,thumbnail:"Authentication lost. Please refresh the page and try again."})}),200)}catch(o){const{user:t}=l.getState();if(!t)return void K({...G,thumbnail:"Authentication lost during thumbnail generation. Please refresh the page and try again."});let s="Failed to generate thumbnails";o instanceof Error&&(s=o.message.includes("Authentication")?"Authentication lost. Please refresh the page and try again.":o.message.includes("memory")?"Insufficient device memory. Try uploading without custom thumbnails.":o.message.includes("timeout")?"Thumbnail generation timed out. Your device may be too slow for this operation.":o.message),K({...G,thumbnail:s})}finally{B(!1)}else K({...G,thumbnail:"Please upload a video first"})},isLoading:_,disabled:_,children:"Auto-Generate"})]}),t.jsx("input",{type:"file",ref:a,className:"hidden",accept:"image/*",onChange:t=>{const s=t.target.files?.[0];if(s){const{user:t}=l.getState();if(!t)return void K({...G,thumbnail:"Please log in again to select a thumbnail"});const a=y(s);if(!a.isValid)return void K({...G,thumbnail:a.error||"Invalid image file"});V(s);try{if(u()){const e=navigator.deviceMemory;if(e&&e<2)Y(null);else{const e=URL.createObjectURL(s);Y(e),setTimeout((()=>{try{URL.revokeObjectURL(e)}catch(s){}}),5e3)}}else{const e=URL.createObjectURL(s);Y(e)}}catch(e){Y(null)}if(G.thumbnail){const e={...G};delete e.thumbnail,K(e)}setTimeout((()=>{const{user:e}=l.getState();e||K({...G,thumbnail:"Authentication lost. Please refresh the page and try again."})}),100)}}}),$.length>0&&t.jsxs("div",{className:"mb-4",children:[t.jsx("p",{className:"text-sm text-gray-300 mb-2",children:"Select a generated thumbnail:"}),t.jsx("div",{className:"grid grid-cols-3 gap-2",children:$.map(((e,s)=>t.jsxs("div",{className:"relative border-2 rounded overflow-hidden cursor-pointer transition-all "+(H===s?"border-orange-500 scale-105":"border-gray-700 hover:border-gray-500"),onClick:()=>(async t=>{if(t>=0&&t<$.length){const{user:s}=l.getState();if(!s)return void K({...G,thumbnail:"Please log in again to select a thumbnail"});W(t);try{const e=await fetch($[t]),s=await e.blob(),a=new File([s],`thumbnail-${Date.now()}.jpg`,{type:"image/jpeg"});V(a),Y($[t]),setTimeout((()=>{const{user:e}=l.getState();e||K({...G,thumbnail:"Authentication lost. Please refresh the page and try again."})}),100)}catch(e){const{user:s}=l.getState();K(s?{...G,thumbnail:"Failed to select thumbnail"}:{...G,thumbnail:"Authentication lost during thumbnail selection. Please refresh the page and try again."})}}})(s),children:[t.jsx("img",{src:e,alt:`Generated thumbnail ${s+1}`,className:"w-full aspect-video object-cover"}),H===s&&t.jsx("div",{className:"absolute top-1 right-1 bg-orange-500 text-white rounded-full p-0.5",children:t.jsx(N,{size:14})})]},s)))})]}),I&&-1!==H?t.jsxs("div",{className:"relative border rounded-lg overflow-hidden h-[200px]",children:[D&&t.jsx("img",{src:D,alt:"Thumbnail preview",className:"w-full h-full object-cover"}),t.jsx("button",{type:"button",className:"absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors",onClick:()=>{V(null),Y(null),W(-1),a.current&&(a.current.value="")},children:t.jsx(x,{size:16})})]}):t.jsxs("div",{className:"border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-orange-500 transition-colors h-[200px] "+(G.thumbnail?"border-red-500":"border-gray-700"),onClick:()=>{a.current?.click()},children:[t.jsx(w,{size:48,className:"text-gray-500 mb-3"}),t.jsx("p",{className:"text-gray-400 text-center",children:"Click to upload custom thumbnail"}),t.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"JPG, PNG or GIF (recommended: 1280×720, max 5MB)"})]}),G.thumbnail&&t.jsx("p",{className:"text-sm text-red-500 mt-1",children:G.thumbnail})]})]})]}),ae&&t.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start",children:[t.jsx(o,{size:20,className:"text-red-500 mr-2 flex-shrink-0 mt-0.5"}),t.jsxs("div",{className:"text-red-200",children:[t.jsx("p",{children:ae}),ae.includes("Storage buckets don't exist")&&t.jsxs("div",{className:"mt-2 text-sm",children:[t.jsx("p",{children:"This is likely because:"}),t.jsxs("ul",{className:"list-disc pl-5 mt-1 space-y-1",children:[t.jsx("li",{children:"The storage buckets haven't been created in Supabase"}),t.jsx("li",{children:"Your user account doesn't have permission to access the buckets"})]}),t.jsxs("p",{className:"mt-2",children:[t.jsx("strong",{children:"Solution:"})," An administrator needs to create the 'videos' and 'thumbnails' buckets in the Supabase dashboard."]})]}),ae.includes("Row Level Security (RLS) policies")&&t.jsxs("div",{className:"mt-2 text-sm",children:[t.jsx("p",{children:"This is likely because:"}),t.jsxs("ul",{className:"list-disc pl-5 mt-1 space-y-1",children:[t.jsx("li",{children:"The Row Level Security (RLS) policies for the storage buckets are not properly configured"}),t.jsx("li",{children:"Your user account doesn't have permission to upload files to these buckets"})]}),t.jsxs("p",{className:"mt-2",children:[t.jsx("strong",{children:"Solution:"})," An administrator needs to check and update the RLS policies for the storage buckets with the following SQL:"]}),t.jsx("pre",{className:"bg-gray-800 p-2 rounded mt-2 overflow-x-auto text-xs",children:"-- First, check existing policies\nSELECT policyname, tablename, cmd, qual, with_check\nFROM pg_policies\nWHERE tablename = 'objects' AND schemaname = 'storage';\n\n-- Then, update the policies if needed\nALTER POLICY \"Users can upload videos to their own folder\"\nON storage.objects\nWITH CHECK (\n  bucket_id = 'videos' AND\n  auth.uid()::text = (storage.foldername(name))[1]\n);\n\nALTER POLICY \"Users can upload thumbnails to their own folder\"\nON storage.objects\nWITH CHECK (\n  bucket_id = 'thumbnails' AND\n  auth.uid()::text = (storage.foldername(name))[1]\n);"})]}),(ae.includes("violates foreign key constraint")||ae.includes("user profile does not exist"))&&t.jsxs("div",{className:"mt-2 text-sm",children:[t.jsx("p",{children:"This is likely because your user profile hasn't been properly created in the database."}),t.jsxs("p",{className:"mt-2",children:[t.jsx("strong",{children:"Solution:"})," Please try the following steps:"]}),t.jsxs("ol",{className:"list-decimal pl-5 mt-1 space-y-1",children:[t.jsx("li",{children:"Log out and log back in to trigger automatic profile creation"}),t.jsx("li",{children:"Refresh the page and try uploading again"}),t.jsx("li",{children:"If the issue persists, contact support"})]})]})]})]}),J&&!ae&&t.jsxs("div",{className:"bg-green-500/20 border border-green-500 rounded-md p-3 flex items-start",children:[t.jsx(N,{size:20,className:"text-green-500 mr-2 flex-shrink-0 mt-0.5"}),t.jsx("p",{className:"text-green-200",children:J})]}),te&&t.jsxs("div",{className:"bg-gray-700 rounded-full overflow-hidden",children:[t.jsx("div",{className:"bg-orange-500 h-2 transition-all duration-300",style:{width:`${se}%`}}),t.jsxs("p",{className:"text-center text-sm text-gray-400 mt-2",children:["Uploading: ",se,"%"]})]}),t.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[t.jsx(p,{variant:"ghost",type:"button",onClick:()=>e("/"),children:"Cancel"}),t.jsx(p,{variant:"primary",type:"submit",leftIcon:t.jsx(k,{size:18}),isLoading:te,disabled:te,children:"Upload Video"})]})]})]})},L=()=>{const e=r(),{user:s,isLoading:a,isApproved:i,profile:d}=l(),[c,u]=n.useState(!1);return n.useEffect((()=>{const e=setTimeout((()=>{u(!0)}),2e3);return()=>clearTimeout(e)}),[]),n.useEffect((()=>{!c||a||s||e("/")}),[s,a,e,c]),a||!c?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsxs("div",{className:"flex flex-col justify-center items-center h-64",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"}),t.jsx("p",{className:"text-gray-400 text-center",children:a?"Loading your account...":"Checking authentication..."}),t.jsx("p",{className:"text-gray-500 text-sm text-center mt-2",children:"Please wait while we verify your login status"})]})}):s?i?t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsx("h1",{className:"text-2xl md:text-3xl font-bold mb-6",children:"Upload Video"}),t.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:t.jsx(C,{})})]}):t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsx("h1",{className:"text-2xl md:text-3xl font-bold mb-6",children:"Upload Video"}),t.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:t.jsxs("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[t.jsx(o,{size:48,className:"text-yellow-500 mb-4"}),t.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Approval Required"}),t.jsx("p",{className:"text-gray-400 max-w-md mb-4",children:"Your account is pending approval. New users need to be approved before they can upload videos."}),t.jsx("p",{className:"text-gray-500 text-sm",children:"Please check back later or contact an administrator for assistance."})]})})]}):null};export{L as default};
