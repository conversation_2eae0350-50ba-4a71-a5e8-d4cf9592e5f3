<?php
/**
 * Local Authentication System Setup Script
 * This script sets up a SQLite database for local development
 */

class LocalAuthSetup {
    private $pdo;
    private $db_path;
    
    public function __construct() {
        $this->db_path = __DIR__ . '/bluefilmx_local.db';
        $this->connectSQLite();
    }
    
    private function connectSQLite() {
        try {
            $this->pdo = new PDO("sqlite:" . $this->db_path);
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            echo "✅ Connected to SQLite database: {$this->db_path}\n";
        } catch (PDOException $e) {
            die("❌ SQLite connection failed: " . $e->getMessage() . "\n");
        }
    }
    
    public function createAuthSchema() {
        echo "\n=== Creating Authentication Schema ===\n";
        
        $tables = [
            'roles' => "
                CREATE TABLE IF NOT EXISTS roles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(50) UNIQUE NOT NULL,
                    description TEXT,
                    permissions TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ",
            'users' => "
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    first_name VARCHAR(100),
                    last_name VARCHAR(100),
                    avatar_url TEXT,
                    is_email_verified BOOLEAN DEFAULT 0,
                    email_verified_at DATETIME,
                    password_reset_token VARCHAR(255),
                    password_reset_expires DATETIME,
                    last_login_at DATETIME,
                    failed_login_attempts INTEGER DEFAULT 0,
                    locked_until DATETIME,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ",
            'user_roles' => "
                CREATE TABLE IF NOT EXISTS user_roles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    role_id INTEGER NOT NULL,
                    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
                    UNIQUE(user_id, role_id)
                )
            ",
            'categories' => "
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL,
                    slug VARCHAR(100) UNIQUE NOT NULL,
                    description TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ",
            'videos' => "
                CREATE TABLE IF NOT EXISTS videos (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title VARCHAR(255) NOT NULL,
                    description TEXT,
                    thumbnail_url TEXT,
                    video_url TEXT,
                    duration INTEGER,
                    views INTEGER DEFAULT 0,
                    likes INTEGER DEFAULT 0,
                    category_id INTEGER,
                    uploaded_by INTEGER,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories(id),
                    FOREIGN KEY (uploaded_by) REFERENCES users(id)
                )
            ",
            'comments' => "
                CREATE TABLE IF NOT EXISTS comments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    video_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    user_name VARCHAR(100),
                    user_avatar TEXT,
                    content TEXT NOT NULL,
                    likes INTEGER DEFAULT 0,
                    is_liked BOOLEAN DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ",
            'comment_replies' => "
                CREATE TABLE IF NOT EXISTS comment_replies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    comment_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    user_name VARCHAR(100),
                    user_avatar TEXT,
                    content TEXT NOT NULL,
                    likes INTEGER DEFAULT 0,
                    is_liked BOOLEAN DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            "
        ];
        
        try {
            foreach ($tables as $table_name => $sql) {
                $this->pdo->exec($sql);
                echo "✅ Created table: $table_name\n";
            }
            
            echo "✅ Authentication schema created successfully\n";
            return true;
            
        } catch (Exception $e) {
            echo "❌ Failed to create auth schema: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    public function insertDefaultData() {
        echo "\n=== Inserting Default Data ===\n";
        
        try {
            // Insert roles
            $roles = [
                ['admin', 'Administrator with full access', 'all'],
                ['user', 'Regular user with basic access', 'read,write'],
                ['moderator', 'Moderator with content management access', 'read,write,moderate']
            ];
            
            $stmt = $this->pdo->prepare("
                INSERT OR IGNORE INTO roles (name, description, permissions) 
                VALUES (?, ?, ?)
            ");
            
            foreach ($roles as $role) {
                $stmt->execute($role);
                echo "✅ Inserted role: {$role[0]}\n";
            }
            
            // Insert categories
            $categories = [
                ['Action', 'action', 'Action movies and videos'],
                ['Comedy', 'comedy', 'Comedy movies and videos'],
                ['Drama', 'drama', 'Drama movies and videos'],
                ['Horror', 'horror', 'Horror movies and videos'],
                ['Sci-Fi', 'sci-fi', 'Science fiction movies and videos']
            ];
            
            $stmt = $this->pdo->prepare("
                INSERT OR IGNORE INTO categories (name, slug, description) 
                VALUES (?, ?, ?)
            ");
            
            foreach ($categories as $category) {
                $stmt->execute($category);
                echo "✅ Inserted category: {$category[0]}\n";
            }
            
            // Insert test users
            $users = [
                [
                    'username' => 'admin',
                    'email' => '<EMAIL>',
                    'password' => 'admin123',
                    'first_name' => 'Admin',
                    'last_name' => 'User',
                    'role' => 'admin'
                ],
                [
                    'username' => 'testuser',
                    'email' => '<EMAIL>',
                    'password' => 'testpassword123',
                    'first_name' => 'Test',
                    'last_name' => 'User',
                    'role' => 'user'
                ]
            ];
            
            $user_stmt = $this->pdo->prepare("
                INSERT OR IGNORE INTO users (username, email, password_hash, first_name, last_name, is_email_verified) 
                VALUES (?, ?, ?, ?, ?, 1)
            ");
            
            $role_stmt = $this->pdo->prepare("
                INSERT OR IGNORE INTO user_roles (user_id, role_id) 
                SELECT u.id, r.id FROM users u, roles r 
                WHERE u.username = ? AND r.name = ?
            ");
            
            foreach ($users as $user) {
                $password_hash = password_hash($user['password'], PASSWORD_DEFAULT);
                
                $user_stmt->execute([
                    $user['username'],
                    $user['email'],
                    $password_hash,
                    $user['first_name'],
                    $user['last_name']
                ]);
                
                $role_stmt->execute([$user['username'], $user['role']]);
                
                echo "✅ Created user: {$user['username']} ({$user['email']}) - Password: {$user['password']}\n";
            }
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ Failed to insert default data: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    public function updateDatabaseConfig() {
        echo "\n=== Updating Database Configuration ===\n";
        
        // Create a simple database config for SQLite
        $sqlite_config = "<?php\n";
        $sqlite_config .= "class Database {\n";
        $sqlite_config .= "    private static \$instance = null;\n";
        $sqlite_config .= "    private \$connection;\n";
        $sqlite_config .= "\n";
        $sqlite_config .= "    private function __construct() {\n";
        $sqlite_config .= "        try {\n";
        $sqlite_config .= "            \$this->connection = new PDO('sqlite:' . __DIR__ . '/../../bluefilmx_local.db');\n";
        $sqlite_config .= "            \$this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);\n";
        $sqlite_config .= "        } catch (PDOException \$e) {\n";
        $sqlite_config .= "            throw new Exception('Database connection failed: ' . \$e->getMessage());\n";
        $sqlite_config .= "        }\n";
        $sqlite_config .= "    }\n";
        $sqlite_config .= "\n";
        $sqlite_config .= "    public static function getInstance() {\n";
        $sqlite_config .= "        if (self::\$instance === null) {\n";
        $sqlite_config .= "            self::\$instance = new self();\n";
        $sqlite_config .= "        }\n";
        $sqlite_config .= "        return self::\$instance;\n";
        $sqlite_config .= "    }\n";
        $sqlite_config .= "\n";
        $sqlite_config .= "    public function getConnection() {\n";
        $sqlite_config .= "        return \$this->connection;\n";
        $sqlite_config .= "    }\n";
        $sqlite_config .= "\n";
        $sqlite_config .= "    public static function setCORSHeaders() {\n";
        $sqlite_config .= "        \$allowed_origins = ['http://localhost:3000', 'http://127.0.0.1:3000'];\n";
        $sqlite_config .= "        \$origin = \$_SERVER['HTTP_ORIGIN'] ?? '';\n";
        $sqlite_config .= "        if (in_array(\$origin, \$allowed_origins)) {\n";
        $sqlite_config .= "            header('Access-Control-Allow-Origin: ' . \$origin);\n";
        $sqlite_config .= "        }\n";
        $sqlite_config .= "        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');\n";
        $sqlite_config .= "        header('Access-Control-Allow-Headers: Content-Type, Authorization');\n";
        $sqlite_config .= "        header('Access-Control-Allow-Credentials: true');\n";
        $sqlite_config .= "    }\n";
        $sqlite_config .= "}\n";
        $sqlite_config .= "?>";
        
        // Backup original database.php
        $original_db = __DIR__ . '/namecheap-api/config/database.php';
        $backup_db = __DIR__ . '/namecheap-api/config/database.php.backup';
        
        if (file_exists($original_db) && !file_exists($backup_db)) {
            copy($original_db, $backup_db);
            echo "✅ Backed up original database.php\n";
        }
        
        file_put_contents($original_db, $sqlite_config);
        echo "✅ Updated database.php for SQLite\n";
        
        return true;
    }
    
    public function testConnection() {
        echo "\n=== Testing Database Connection ===\n";
        
        try {
            // Test basic queries
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM users");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "✅ Database connection: OK\n";
            echo "   Total users: {$result['count']}\n";
            
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM roles");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "✅ Roles table: OK ({$result['count']} roles)\n";
            
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM categories");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "✅ Categories table: OK ({$result['count']} categories)\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ Database test failed: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    public function run() {
        echo "BlueFilmX Local Authentication System Setup (SQLite)\n";
        echo "==================================================\n";
        
        $steps = [
            'createAuthSchema',
            'insertDefaultData',
            'updateDatabaseConfig',
            'testConnection'
        ];
        
        foreach ($steps as $step) {
            if (!$this->$step()) {
                echo "\n❌ Setup failed at step: $step\n";
                return false;
            }
        }
        
        echo "\n🎉 Local authentication system setup completed successfully!\n";
        echo "\nDatabase file: {$this->db_path}\n";
        echo "\nTest Users Created:\n";
        echo "1. Admin User:\n";
        echo "   Email: <EMAIL>\n";
        echo "   Password: admin123\n";
        echo "\n2. Test User:\n";
        echo "   Email: <EMAIL>\n";
        echo "   Password: testpassword123\n";
        echo "\nNext steps:\n";
        echo "1. Test the authentication system at http://localhost:3000\n";
        echo "2. Update frontend components to use new auth system\n";
        echo "3. Test all authentication flows\n";
        
        return true;
    }
}

// Run setup if called directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    try {
        $setup = new LocalAuthSetup();
        $setup->run();
    } catch (Exception $e) {
        echo "❌ Setup failed: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>