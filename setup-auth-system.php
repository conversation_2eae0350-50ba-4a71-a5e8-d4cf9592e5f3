<?php
/**
 * Authentication System Setup Script
 * This script sets up the new authentication system database and migrates existing users
 */

// Load environment variables
function loadEnv($path) {
    if (!file_exists($path)) {
        echo "Warning: .env file not found at $path\n";
        return;
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') === false) {
            continue;
        }
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        
        if (!array_key_exists($name, $_SERVER) && !array_key_exists($name, $_ENV)) {
            putenv(sprintf('%s=%s', $name, $value));
            $_ENV[$name] = $value;
            $_SERVER[$name] = $value;
        }
    }
}

// Load .env file
loadEnv(__DIR__ . '/.env');

class AuthSystemSetup {
    private $pdo;
    
    public function __construct() {
        // Use the existing database configuration
        require_once __DIR__ . '/namecheap-api/config/database.php';
        $database = new Database();
        $this->pdo = $database->getConnection();
    }
    
    public function validateEnvironment() {
        echo "=== Validating Environment ===\n";
        
        $required_vars = ['JWT_SECRET', 'DB_HOST', 'DB_NAME', 'DB_USER'];
        $missing = [];
        
        foreach ($required_vars as $var) {
            if (empty($_ENV[$var])) {
                $missing[] = $var;
            }
        }
        
        if (!empty($missing)) {
            echo "⚠️  Missing environment variables: " . implode(', ', $missing) . "\n";
            echo "ℹ️  Using default values where possible\n";
        } else {
            echo "✅ All required environment variables are set\n";
        }
        
        return true;
    }
    
    public function createAuthSchema() {
        echo "\n=== Creating Authentication Schema ===\n";
        
        try {
            // Read the auth schema file
            $schema_file = __DIR__ . '/auth-system/database/auth-schema.sql';
            if (!file_exists($schema_file)) {
                echo "❌ Auth schema file not found: $schema_file\n";
                return false;
            }
            
            $sql = file_get_contents($schema_file);
            
            // Split into individual statements
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            foreach ($statements as $statement) {
                if (empty($statement)) continue;
                
                try {
                    $this->pdo->exec($statement);
                } catch (PDOException $e) {
                    // Ignore table already exists errors
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "⚠️  SQL Warning: " . $e->getMessage() . "\n";
                    }
                }
            }
            
            echo "✅ Authentication schema created successfully\n";
            return true;
            
        } catch (Exception $e) {
            echo "❌ Failed to create auth schema: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    public function migrateExistingUsers() {
        echo "\n=== Migrating Existing Users ===\n";
        
        try {
            // Check if profiles table exists
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'profiles'");
            if ($stmt->rowCount() == 0) {
                echo "ℹ️  No existing profiles table found, skipping migration\n";
                return true;
            }
            
            // Get existing profiles
            $stmt = $this->pdo->query("SELECT * FROM profiles");
            $profiles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($profiles)) {
                echo "ℹ️  No existing users to migrate\n";
                return true;
            }
            
            $migrated = 0;
            foreach ($profiles as $profile) {
                try {
                    // Check if user already exists in new system
                    $check_stmt = $this->pdo->prepare("SELECT id FROM users WHERE email = ? OR username = ?");
                    $check_stmt->execute([$profile['email'] ?? '', $profile['username'] ?? '']);
                    
                    if ($check_stmt->rowCount() > 0) {
                        continue; // User already migrated
                    }
                    
                    // Insert into new users table
                    $insert_stmt = $this->pdo->prepare("
                        INSERT INTO users (id, username, email, password_hash, first_name, last_name, 
                                          avatar_url, is_email_verified, created_at, updated_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, 1, ?, ?)
                    ");
                    
                    $user_id = $profile['id'];
                    $username = $profile['username'] ?? 'user_' . $user_id;
                    $email = $profile['email'] ?? $username . '@example.com';
                    $password_hash = password_hash('defaultpassword123', PASSWORD_DEFAULT);
                    $first_name = $profile['first_name'] ?? '';
                    $last_name = $profile['last_name'] ?? '';
                    $avatar_url = $profile['avatar_url'] ?? null;
                    $created_at = $profile['created_at'] ?? date('Y-m-d H:i:s');
                    $updated_at = $profile['updated_at'] ?? date('Y-m-d H:i:s');
                    
                    $insert_stmt->execute([
                        $user_id, $username, $email, $password_hash, 
                        $first_name, $last_name, $avatar_url, 
                        $created_at, $updated_at
                    ]);
                    
                    // Assign default role
                    $role_stmt = $this->pdo->prepare("INSERT INTO user_roles (user_id, role_id) VALUES (?, 2)"); // User role
                    $role_stmt->execute([$user_id]);
                    
                    $migrated++;
                    
                } catch (Exception $e) {
                    echo "⚠️  Failed to migrate user {$profile['id']}: " . $e->getMessage() . "\n";
                }
            }
            
            echo "✅ Migrated $migrated users successfully\n";
            return true;
            
        } catch (Exception $e) {
            echo "❌ Migration failed: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    public function createTestUser() {
        echo "\n=== Creating Test User ===\n";
        
        try {
            // Check if test user already exists
            $stmt = $this->pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute(['<EMAIL>']);
            
            if ($stmt->rowCount() > 0) {
                echo "ℹ️  Test user already exists\n";
                return true;
            }
            
            // Create test user
            $stmt = $this->pdo->prepare("
                INSERT INTO users (username, email, password_hash, first_name, last_name, 
                                  is_email_verified, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, 1, NOW(), NOW())
            ");
            
            $password_hash = password_hash('testpassword123', PASSWORD_DEFAULT);
            $stmt->execute([
                'testuser',
                '<EMAIL>',
                $password_hash,
                'Test',
                'User'
            ]);
            
            $user_id = $this->pdo->lastInsertId();
            
            // Assign user role
            $role_stmt = $this->pdo->prepare("INSERT INTO user_roles (user_id, role_id) VALUES (?, 2)");
            $role_stmt->execute([$user_id]);
            
            echo "✅ Test user created successfully\n";
            echo "   Email: <EMAIL>\n";
            echo "   Password: testpassword123\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ Failed to create test user: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    public function testAuthentication() {
        echo "\n=== Testing Authentication System ===\n";
        
        try {
            // Test database connection
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM users");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "✅ Database connection: OK\n";
            echo "   Total users: {$result['count']}\n";
            
            // Test roles table
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM roles");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "✅ Roles table: OK ({$result['count']} roles)\n";
            
            // Test JWT secret
            $jwt_secret = $_ENV['JWT_SECRET'] ?? '';
            if (strlen($jwt_secret) >= 32) {
                echo "✅ JWT Secret: OK\n";
            } else {
                echo "⚠️  JWT Secret: Too short (should be at least 32 characters)\n";
            }
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ Authentication test failed: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    public function run() {
        echo "BlueFilmX Authentication System Setup\n";
        echo "====================================\n";
        
        $steps = [
            'validateEnvironment',
            'createAuthSchema',
            'migrateExistingUsers',
            'createTestUser',
            'testAuthentication'
        ];
        
        foreach ($steps as $step) {
            if (!$this->$step()) {
                echo "\n❌ Setup failed at step: $step\n";
                return false;
            }
        }
        
        echo "\n🎉 Authentication system setup completed successfully!\n";
        echo "\nNext steps:\n";
        echo "1. Test login with: <EMAIL> / testpassword123\n";
        echo "2. Update email configuration in .env file\n";
        echo "3. Test all authentication flows\n";
        echo "4. Update frontend components to use new auth system\n";
        
        return true;
    }
}

// Run setup if called directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    try {
        $setup = new AuthSystemSetup();
        $setup->run();
    } catch (Exception $e) {
        echo "❌ Setup failed: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>