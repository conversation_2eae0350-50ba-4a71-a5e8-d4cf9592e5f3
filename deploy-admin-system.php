<?php
/**
 * Deploy Admin System Script
 * This script should be uploaded to the server and run there
 * It will clear all accounts and set up the admin system
 */

// Database configuration - update these with your actual values
$host = 'localhost';
$dbname = 'bluerpcm_bluefilmx';
$username = 'bluerpcm_dbuser';
$password = 'Brafiifi100@';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    echo "🚀 Setting up Admin System...\n\n";
    
    // Step 1: Add admin fields to profiles table
    echo "📝 Adding admin fields to database...\n";
    
    // Add is_admin field
    try {
        $pdo->exec("ALTER TABLE profiles ADD COLUMN is_admin BOOLEAN DEFAULT FALSE");
        echo "   ✅ Added is_admin field\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "   ℹ️  is_admin field already exists\n";
        } else {
            throw $e;
        }
    }
    
    // Add is_approved field
    try {
        $pdo->exec("ALTER TABLE profiles ADD COLUMN is_approved BOOLEAN DEFAULT FALSE");
        echo "   ✅ Added is_approved field\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "   ℹ️  is_approved field already exists\n";
        } else {
            throw $e;
        }
    }
    
    // Create admin_counter table
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS admin_counter (
                id INT PRIMARY KEY AUTO_INCREMENT,
                admin_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "   ✅ Created admin_counter table\n";
    } catch (Exception $e) {
        echo "   ℹ️  admin_counter table already exists\n";
    }
    
    // Step 2: Clear all existing accounts
    echo "🗑️  Clearing all existing accounts...\n";
    
    // Clear only user account tables, preserve all other data
    $tables = [
        'verification',
        'session',
        'account',
        'profiles',
        'user'
    ];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->prepare("DELETE FROM $table");
            $stmt->execute();
            $count = $stmt->rowCount();
            echo "   ✅ Cleared $table table ($count records)\n";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), "doesn't exist") === false) {
                echo "   ⚠️  Warning clearing $table: " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Step 3: Reset admin counter
    echo "🔄 Resetting admin counter...\n";
    $pdo->exec("DELETE FROM admin_counter");
    $pdo->exec("INSERT INTO admin_counter (admin_count) VALUES (0)");
    echo "   ✅ Admin counter reset to 0\n";
    
    // Step 4: Show final status
    echo "\n✅ Admin system setup completed successfully!\n\n";
    echo "📋 System Configuration:\n";
    echo "   • First 2 signups will automatically become admins\n";
    echo "   • All subsequent signups require admin approval\n";
    echo "   • All existing accounts have been cleared\n\n";
    
    // Show current status
    echo "📊 Current System Status:\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM profiles");
    $userCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM profiles WHERE is_admin = TRUE");
    $adminCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM profiles WHERE is_approved = FALSE AND is_admin = FALSE");
    $pendingCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT admin_count FROM admin_counter LIMIT 1");
    $adminCounterValue = $stmt->fetch()['admin_count'];
    
    echo "   👥 Total Users: $userCount\n";
    echo "   👑 Admins: $adminCount\n";
    echo "   ⏳ Pending Approval: $pendingCount\n";
    echo "   🔢 Admin Counter: $adminCounterValue\n\n";
    
    echo "🎉 Setup complete! You can now start registering new users.\n";
    echo "   The first two users to register will automatically become admins.\n";
    
} catch (Exception $e) {
    echo "❌ Setup failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>