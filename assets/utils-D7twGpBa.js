import{r as e,a as t}from"./react-core-B9nwsbCA.js";import{c as r}from"./state-4gBW_4Nx.js";var n=e;
/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,a=n.useEffect,s=n.useLayoutEffect,c=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!o(e,r)}catch(n){return!0}}"undefined"==typeof window||void 0===window.document||window.document.createElement;void 0!==n.useSyncExternalStore&&n.useSyncExternalStore;var l=Object.prototype.hasOwnProperty;const d=new WeakMap,h=()=>{},p=h(),g=Object,f=e=>e===p,m=e=>"function"==typeof e,w=(e,t)=>({...e,...t}),v={},y={},b="undefined",E=typeof window!=b,S=typeof document!=b,L=E&&"Deno"in window;let O=!0;const[k,x]=E&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[h,h],R={isOnline:()=>O,isVisible:()=>{const e=S&&document.visibilityState;return f(e)||"hidden"!==e}},C={initFocus:e=>(S&&document.addEventListener("visibilitychange",e),k("focus",e),()=>{S&&document.removeEventListener("visibilitychange",e),x("focus",e)}),initReconnect:e=>{const t=()=>{O=!0,e()},r=()=>{O=!1};return k("online",t),k("offline",r),()=>{x("online",t),x("offline",r)}}};t.useId;const U=!E||L,$=U?e.useEffect:e.useLayoutEffect,P="undefined"!=typeof navigator&&navigator.connection,T=!U&&P&&(["slow-2g","2g"].includes(P.effectiveType)||P.saveData),j=new WeakMap,A=(e,t)=>g.prototype.toString.call(e)===`[object ${t}]`;let I=0;const M=e=>{const t=typeof e,r=A(e,"Date"),n=A(e,"RegExp"),o=A(e,"Object");let i,a;if(g(e)!==e||r||n)i=r?e.toJSON():"symbol"==t?e.toString():"string"==t?JSON.stringify(e):""+e;else{if(i=j.get(e),i)return i;if(i=++I+"~",j.set(e,i),Array.isArray(e)){for(i="@",a=0;a<e.length;a++)i+=M(e[a])+",";j.set(e,i)}if(o){i="#";const t=g.keys(e).sort();for(;!f(a=t.pop());)f(e[a])||(i+=a+":"+M(e[a])+",");j.set(e,i)}}return i},F=e=>{if(m(e))try{e=e()}catch(r){e=""}const t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?M(e):"",t]};let V=0;const _=()=>++V;async function W(...e){const[t,r,n,o]=e,i=w({populateCache:!0,throwOnError:!0},"boolean"==typeof o?{revalidate:o}:o||{});let a=i.populateCache;const s=i.rollbackOnError;let c=i.optimisticData;const u=i.throwOnError;if(m(r)){const e=r,n=[],o=t.keys();for(const r of o)!/^\$(inf|sub)\$/.test(r)&&e(t.get(r)._k)&&n.push(r);return Promise.all(n.map(l))}return l(r);async function l(r){const[o]=F(r);if(!o)return;const[l,h]=((e,t)=>{const r=d.get(e);return[()=>!f(t)&&e.get(t)||v,n=>{if(!f(t)){const o=e.get(t);t in y||(y[t]=o),r[5](t,w(o,n),o||v)}},r[6],()=>!f(t)&&t in y?y[t]:!f(t)&&e.get(t)||v]})(t,o),[g,b,E,S]=d.get(t),L=()=>{const e=g[o];return(m(i.revalidate)?i.revalidate(l().data,r):!1!==i.revalidate)&&(delete E[o],delete S[o],e&&e[0])?e[0](2).then((()=>l().data)):l().data};if(e.length<3)return L();let O,k=n;const x=_();b[o]=[x,0];const R=!f(c),C=l(),U=C.data,$=C._c,P=f($)?U:$;if(R&&(c=m(c)?c(P,U):c,h({data:c,_c:P})),m(k))try{k=k(P)}catch(T){O=T}if(k&&m(k.then)){if(k=await k.catch((e=>{O=e})),x!==b[o][0]){if(O)throw O;return k}O&&R&&(e=>"function"==typeof s?s(e):!1!==s)(O)&&(a=!0,h({data:P,_c:p}))}if(a&&!O)if(m(a)){const e=a(k,P);h({data:e,error:p,_c:p})}else h({data:k,error:p,_c:p});if(b[o][1]=_(),Promise.resolve(L()).then((()=>{h({_c:p})})),!O)return k;if(u)throw O}}const N=(e,t)=>{for(const r in e)e[r][0]&&e[r][0](t)},D=(e,t)=>{if(!d.has(e)){const r=w(C,t),n=Object.create(null),o=W.bind(p,e);let i=h;const a=Object.create(null),s=(e,t)=>{const r=a[e]||[];return a[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},c=(t,r,n)=>{e.set(t,r);const o=a[t];if(o)for(const e of o)e(r,n)},u=()=>{if(!d.has(e)&&(d.set(e,[n,Object.create(null),Object.create(null),Object.create(null),o,c,s]),!U)){const t=r.initFocus(setTimeout.bind(p,N.bind(p,n,0))),o=r.initReconnect(setTimeout.bind(p,N.bind(p,n,1)));i=()=>{t&&t(),o&&o(),d.delete(e)}}};return u(),[e,o,u,i]}return[e,d.get(e)[4]]},z=function e(t,r){var n,o;if(t===r)return!0;if(t&&r&&(n=t.constructor)===r.constructor){if(n===Date)return t.getTime()===r.getTime();if(n===RegExp)return t.toString()===r.toString();if(n===Array){if((o=t.length)===r.length)for(;o--&&e(t[o],r[o]););return-1===o}if(!n||"object"==typeof t){for(n in o=0,t){if(l.call(t,n)&&++o&&!l.call(r,n))return!1;if(!(n in r)||!e(t[n],r[n]))return!1}return Object.keys(r).length===o}}return t!=t&&r!=r},[B,q]=D(new Map),J=w({onLoadingSlow:h,onSuccess:h,onError:h,onErrorRetry:(e,t,r,n,o)=>{const i=r.errorRetryCount,a=o.retryCount,s=~~((Math.random()+.5)*(1<<(a<8?a:8)))*r.errorRetryInterval;!f(i)&&a>i||setTimeout(n,s,o)},onDiscarded:h,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:T?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:T?5e3:3e3,compare:z,isPaused:()=>!1,cache:B,mutate:q,fallback:{}},R),G=e.createContext({}),H=E&&window.__SWR_DEVTOOLS_USE__;(H?window.__SWR_DEVTOOLS_USE__:[]).concat((e=>(t,r,n)=>e(t,r&&((...e)=>{const[n]=F(t),[,,,o]=d.get(B);if(n.startsWith("$inf$"))return r(...e);const i=o[n];return f(i)?r(...e):(delete o[n],i)}),n))),H&&(window.__SWR_DEVTOOLS_REACT__=t),t.use;const Y=g.defineProperty((t=>{const{value:r}=t,n=e.useContext(G),o=m(r),i=e.useMemo((()=>o?r(n):r),[o,n,r]),a=e.useMemo((()=>o?i:((e,t)=>{const r=w(e,t);if(t){const{use:n,fallback:o}=e,{use:i,fallback:a}=t;n&&i&&(r.use=n.concat(i)),o&&a&&(r.fallback=w(o,a))}return r})(n,i)),[o,n,i]),s=i&&i.provider,c=e.useRef(p);s&&!c.current&&(c.current=D(s(a.cache||B),i));const u=c.current;return u&&(a.cache=u[0],a.mutate=u[1]),$((()=>{if(u)return u[2]&&u[2](),u[3]}),[]),e.createElement(G.Provider,w(t,{value:a}))}),"defaultValue",{value:J}),Q=e=>{if(!e)return"";const t=e.trim().replace(/[\r\n\t]/g,"").replace(/%0A/g,"");if(t.includes("supabase.co/storage/v1/object/public/")||t.includes("bluefilmx.com/media/")||t.startsWith("http://")||t.startsWith("https://"))return t;if(t.startsWith("/storage/")||t.startsWith("storage/")){return`https://vsnsglgyapexhwyfylic.supabase.co/${t.startsWith("/")?t.slice(1):t}`}if(t.startsWith("/media/")||t.startsWith("media/")){return`http://www.bluefilmx.com${t.startsWith("/")?t:`/${t}`}`}return t},K=e=>{const t=Q(e);return t||Z()},X=e=>{const t=Q(e);return t||""},Z=()=>"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='640' height='360' viewBox='0 0 640 360'%3E%3Crect width='640' height='360' fill='%23374151'/%3E%3Cg transform='translate(320,180)'%3E%3Ccircle cx='0' cy='0' r='30' fill='%236B7280'/%3E%3Cpolygon points='-10,-15 -10,15 20,0' fill='%23F3F4F6'/%3E%3C/g%3E%3Ctext x='320' y='320' font-family='Arial, sans-serif' font-size='18' fill='%239CA3AF' text-anchor='middle'%3ENo Thumbnail%3C/text%3E%3C/svg%3E",ee=async(e,t)=>{const{priority:r="high",maxPreload:n=6,includeNextPage:o=!1}=t||{},i=e.slice(0,n).map((async(e,t)=>{if(e.thumbnailUrl){const n=document.createElement("link");n.rel="preload",n.as="image";const o=t<3?640:320;n.href=re(K(e.thumbnailUrl),o),"high"===r&&t<3?n.setAttribute("fetchpriority","high"):n.setAttribute("fetchpriority","low"),document.head.appendChild(n)}}));await Promise.allSettled(i)},te=e=>{if(!e)return"";const t=e.replace(/([^:]\/)\/+/g,"$1");return t.startsWith("http://")&&"https:"===window.location.protocol?t.replace("http://","https://"):t},re=(e,t,r)=>{const n=Q(e);if(!n||!t)return n;if(n.includes("supabase.co/storage/v1/object/public/"))try{const e=new URL(n);e.searchParams.set("width",t.toString());const o=t<=320?"75":t<=640?"80":"85";return e.searchParams.set("quality",o),r&&"auto"!==r&&e.searchParams.set("format",r),e.toString()}catch(o){return n}return n},ne=()=>{const e=document.createElement("canvas");e.width=1,e.height=1;try{if(0===e.toDataURL("image/avif").indexOf("data:image/avif"))return"avif";if(0===e.toDataURL("image/webp").indexOf("data:image/webp"))return"webp"}catch(t){}return"auto"},oe=e=>re(e,40,"auto");const ie=new class{constructor(e="https://www.bluefilmx.com/api"){this.baseUrl=e}async request(e,t={}){const r=`${this.baseUrl}${e}`,n={headers:{"Content-Type":"application/json",...t.headers},credentials:"include"},o=await fetch(r,{...n,...t});if(!o.ok){const e=await o.text();throw new Error(`API Error: ${o.status} - ${e}`)}return o.json()}async getVideos(e={}){const t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),e.category&&"all"!==e.category&&t.append("category",e.category),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),e.order&&t.append("order",e.order);const r=`/videos.php?${t.toString()}`;return this.request(r)}async getVideo(e){return this.request(`/videos.php/${e}`)}async createVideo(e){return this.request("/videos.php",{method:"POST",body:JSON.stringify(e)})}async updateVideo(e,t){return this.request(`/videos/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteVideo(e){return this.request(`/videos/${e}`,{method:"DELETE"})}async getCategories(){return this.request("/categories")}async login(e,t){return this.request("/auth.php?action=login",{method:"POST",body:JSON.stringify({username:e,password:t})})}async register(e,t){return this.request("/auth.php?action=register",{method:"POST",body:JSON.stringify({username:e,password:t})})}async logout(){return this.request("/auth.php?action=logout",{method:"POST"})}async getCurrentUser(){return this.request("/auth.php")}async uploadFile(e,t){const r=new FormData;return r.append("file",e),r.append("type",t),this.request("/upload.php",{method:"POST",body:r,headers:{}})}},ae=r(((e,t)=>({user:null,isLoading:!0,isApproved:!1,signUp:async(t,r)=>{try{const n=await ie.register(t,r);if(!n.success||!n.data.user)throw new Error("Registration failed");e({user:n.data.user,isApproved:n.data.user.is_approved||!1,isLoading:!1})}catch(n){throw n}},signIn:async(t,r)=>{try{const n=await ie.login(t,r);if(!n.success||!n.data.user)throw new Error("Login failed");e({user:n.data.user,isApproved:n.data.user.is_approved||!1,isLoading:!1})}catch(n){throw n}},signOut:async()=>{try{await ie.logout(),e({user:null,isApproved:!1,isLoading:!1})}catch(t){e({user:null,isApproved:!1,isLoading:!1})}},loadUser:async()=>{try{e({isLoading:!0});const t=await ie.getCurrentUser();t.success&&t.data.user?e({user:t.data.user,isApproved:t.data.user.is_approved||!1,isLoading:!1}):e({user:null,isApproved:!1,isLoading:!1})}catch(t){e({user:null,isApproved:!1,isLoading:!1})}}})));"undefined"!=typeof window&&ae.getState().loadUser();const se=(e="default")=>{const t=e.split("").reduce(((e,t)=>e+t.charCodeAt(0)),0),r=(e,t)=>e[t%e.length],n=[`mouth=${r(["smile","tongue","twinkle","vomit"],t)}`,`eyes=${r(["happy","hearts","stars","wink","winkWacky"],t+1)}`,`top=${r(["longHair","shortHair","eyepatch","hat","hijab","turban","bigHair","bob","bun"],t+2)}`,`accessories=${r(["kurt","prescription01","prescription02","round","sunglasses","wayfarers"],t+3)}`,`hairColor=${r(["auburn","black","blonde","brown","pastel","platinum","red","blue","pink"],t+4)}`,`facialHair=${r(["medium","light","majestic","fancy","magnum"],t+5)}`,`clothes=${r(["blazer","sweater","hoodie","overall","shirtCrewNeck"],t+6)}`,`fabric=${r(["denim","graphicShirt","stripes","dots"],t+7)}`,`backgroundColor=${r(["b6e3f4","c0aede","ffd5dc","ffdfbf","d1d4f9","c0e8d5"],t+8)}`].join("&");return`https://avatars.dicebear.com/api/avataaars/${encodeURIComponent(e)}.svg?${n}`},ce=()=>`https://avatars.dicebear.com/api/bottts/fallback.svg?${["backgroundColor=b6e3f4","colors=blue","mouthChance=100","sidesChance=100","topChance=100"].join("&")}`,ue=()=>{const e=[],t=[];try{const n=localStorage.getItem("sb-lfnxllcoixgfkasdjtnj-auth-token");if(n)try{const r=JSON.parse(n);r.expires_at&&new Date(1e3*r.expires_at)<new Date&&(e.push("Expired authentication token found"),t.push("Clear authentication data and re-login"))}catch(r){e.push("Corrupted authentication data"),t.push("Clear authentication storage")}const o=localStorage.getItem("app-version"),i="1.0.0";o&&o!==i&&(e.push(`Version mismatch: stored ${o}, current ${i}`),t.push("Clear cached data for new version"));let a=0;for(let e in localStorage)localStorage.hasOwnProperty(e)&&(a+=localStorage[e].length);a>5242880&&(e.push("Large localStorage usage detected"),t.push("Consider clearing non-essential cached data"));const s=localStorage.getItem("user-preferences-storage");if(s)try{const r=JSON.parse(s);r.state&&Object.keys(r.state.watchHistory||{}).length>1e3&&(e.push("Large watch history detected"),t.push("Consider clearing old watch history"))}catch(r){e.push("Corrupted user preferences data"),t.push("Reset user preferences")}}catch(n){e.push("Error checking browser state"),t.push("Clear all browser data")}return{hasConflicts:e.length>0,conflicts:e,recommendations:t}},le=new Map,de=()=>({size:le.size,keys:Array.from(le.keys())}),he=()=>({version:"1.0.0",timestamp:Date.now(),buildId:"dev"}),pe=async()=>{try{if("caches"in window){const e=await caches.keys();await Promise.all(e.map((e=>caches.delete(e))))}["user-preferences-storage","disclaimerAccepted","recentSearches"].forEach((e=>{try{localStorage.removeItem(e)}catch(t){}})),(()=>{try{const e=he();localStorage.setItem("app-version",e.version),localStorage.setItem("app-build-id",e.buildId),localStorage.setItem("version-updated",e.timestamp.toString())}catch(e){}})()}catch(e){}},ge=async()=>{try{return!(()=>{try{return localStorage.getItem("app-version")===he().version}catch(e){return!1}})()&&(await pe(),!0)}catch(e){return!1}},fe=e=>{let t;return t=window.setInterval((async()=>{try{const t=await fetch("/version.json?"+Date.now(),{cache:"no-cache"});if(t.ok){const r=await t.json(),n=he().version;r.version!==n&&e()}}catch(t){}}),3e5),()=>{t&&clearInterval(t)}},me=()=>{window.addEventListener("unhandledrejection",(e=>{const t=e.reason;if(t&&"object"==typeof t&&"message"in t){const r=String(t.message);if(r.includes("Cache")||r.includes("service-worker")||r.includes("Failed to execute 'put' on 'Cache'"))return void e.preventDefault()}})),window.addEventListener("error",(e=>{const t=e.error;t&&t.message&&(t.message.includes("Cache")||t.message.includes("service-worker")||t.message.includes("Failed to execute 'put' on 'Cache'"))&&e.preventDefault()}))},we=e=>e>=1e6?`${(e/1e6).toFixed(1)}M`:e>=1e3?`${(e/1e3).toFixed(1)}k`:e.toString(),ve=e=>{const t=Math.floor(e/3600),r=Math.floor(e%3600/60),n=Math.floor(e%60);return t>0?`${t}:${r.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}`:`${r}:${n.toString().padStart(2,"0")}`},ye=(e=640,t=360,r="No Image")=>`data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='${e}' height='${t}' viewBox='0 0 ${e} ${t}'%3E%3Crect width='${e}' height='${t}' fill='%23333'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial, sans-serif' font-size='24' fill='%23fff' text-anchor='middle' dominant-baseline='middle'%3E${r}%3C/text%3E%3C/svg%3E`,be=()=>ye(640,360,"No Thumbnail"),Ee=[/^(.*?)(?:\s*[-:]\s*(?:Part|Episode|Ep\.?|#|S\d+E)\s*(\d+))(?:\s*[-:]\s*(.*))?$/i,/^(.*?)(?:\s*\((?:Part|Episode|Ep\.?|#|S\d+E)\s*(\d+)\))(?:\s*[-:]\s*(.*))?$/i,/^(.*?)\s+(\d+)$/i,/^(\d+)(?:\.|\s*-\s*)\s*(.*)$/i,/^(.*?)(?::\s*)(.*)(?:\s+)(\d+)(?:\s*[-:]\s*(.*))?$/i,/^(.*?)(?:\s*[\[\{]\s*(?:Part|Episode|Ep\.?|#)?\s*(\d+)\s*[\]\}])(?:\s*[-:]\s*(.*))?$/i,/^(.*?)(?:\s+(?:Season|S)\s*(\d+)\s*(?:Episode|Ep\.?|E)\s*(\d+))(?:\s*[-:]\s*(.*))?$/i,/^(.*?)(?:\s+S(\d+)E(\d+))(?:\s*[-:]\s*(.*))?$/i];function Se(e){const t=e.replace(/\s+/g," ").trim();for(let n=0;n<Ee.length;n++){const e=Ee[n],r=t.match(e);if(r){if(0===n||1===n||5===n){return{baseTitle:r[1].trim(),partNumber:parseInt(r[2],10)}}if(2===n){return{baseTitle:r[1].trim(),partNumber:parseInt(r[2],10)}}if(3===n){return{baseTitle:r[2].trim(),partNumber:parseInt(r[1],10)}}if(4===n){return{baseTitle:r[1].trim(),partNumber:parseInt(r[3],10)}}if(6===n||7===n){const e=r[1].trim(),t=parseInt(r[2],10),n=parseInt(r[3]||"0",10);return{baseTitle:e,partNumber:n>0?n:t}}}}const r=t.match(/^(\d+)$/);return r?{baseTitle:"Numbered Series",partNumber:parseInt(r[1],10)}:null}function Le(e){let t=e.toLowerCase();return["the","a","an","and","or","but","in","on","at","to","for","with","by","of"].forEach((e=>{t=t.replace(new RegExp(`\\b${e}\\b`,"g"),"")})),t=t.replace(/[^\w\s]/g,"").replace(/\s+/g," ").trim(),t}function Oe(e,t){const r=Se(e),n=Se(t);if(!r||!n)return!1;const o=Le(r.baseTitle),i=Le(n.baseTitle);return o===i||ke(o,i,.7)}function ke(e,t,r){if(e.length<5||t.length<5){return 1-xe(e,t)/Math.max(e.length,t.length)>=.9}if(e.includes(t)||t.includes(e))return!0;return 1-xe(e,t)/Math.max(e.length,t.length)>=r}function xe(e,t){const r=e.length,n=t.length,o=Array(r+1).fill(null).map((()=>Array(n+1).fill(0)));for(let i=0;i<=r;i++)o[i][0]=i;for(let i=0;i<=n;i++)o[0][i]=i;for(let i=1;i<=r;i++)for(let r=1;r<=n;r++){const n=e[i-1]===t[r-1]?0:1;o[i][r]=Math.min(o[i-1][r]+1,o[i][r-1]+1,o[i-1][r-1]+n)}return o[r][n]}function Re(e){if(!e||0===e.length)return[];const t=new Map,r=[],n=[],o=[];for(const s of e){Se(s.title)?n.push(s):o.push(s)}for(const s of n){const e=Se(s.title);let r=!1;for(const[n,o]of t.entries())if(ke(Le(e.baseTitle),Le(n),.7)){o.push(s),r=!0;break}r||t.set(e.baseTitle,[s])}for(const s of o){let e=!1;for(const[r,n]of t.entries()){const t=Le(s.title),o=Le(r);if(t.includes(o)||o.includes(t)||ke(t,o,.6)){n.push(s),e=!0;break}}e||r.push(s)}const i=new Map;for(const s of r){const e=Le(s.title);i.has(e)?i.get(e).push(s):i.set(e,[s])}const a=[];for(const[s,c]of t.entries())c.length>1?(c.sort(((e,t)=>{const r=Se(e.title),n=Se(t.title);return r&&n?r.partNumber-n.partNumber:r?-1:n?1:e.title.localeCompare(t.title)})),a.push({baseTitle:s,videos:c,totalVideos:c.length})):a.push(c[0]);for(const[s,c]of i.entries())c.length>1?a.push({baseTitle:c[0].title,videos:c,totalVideos:c.length}):a.push(c[0]);return a}const Ce=()=>{try{if("undefined"!=typeof sessionStorage){const e=sessionStorage.getItem("isMobileDevice");if(null!==e)return"true"===e}}catch(e){}return(()=>{if("undefined"==typeof window)return!1;const t=navigator.userAgent||navigator.vendor||window.opera,r="ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0,n=window.innerWidth<=768,o=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(t)||r&&n;try{"undefined"!=typeof sessionStorage&&sessionStorage.setItem("isMobileDevice",String(o))}catch(e){}return o})()},Ue=()=>/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),$e=()=>{const e=navigator.onLine,t=navigator.connection||navigator.mozConnection||navigator.webkitConnection,r=t?.effectiveType||"unknown";return{isOnline:e,connectionType:r,isSlowConnection:"slow-2g"===r||"2g"===r}},Pe=e=>{const t=(()=>{const e=Ue(),t=navigator.connection||navigator.mozConnection||navigator.webkitConnection,r=t&&("slow-2g"===t.effectiveType||"2g"===t.effectiveType);return{chunkSize:e?r?524288:1048576:2097152,maxRetries:e?5:3,timeout:e?6e4:3e4,maxConcurrentChunks:e?1:3,compressionQuality:e?.7:.8,maxFileSize:e?62914560:104857600}})(),r=$e();if(!r.isOnline)return{isValid:!1,error:"No internet connection. Please check your network and try again."};if(e.size>t.maxFileSize){return{isValid:!1,error:`File is too large. Maximum size is ${Math.round(t.maxFileSize/1048576)}MB. Your file is ${Math.round(e.size/1048576)}MB.`}}return r.isSlowConnection&&e.size>10485760?{isValid:!1,error:"File is too large for your current connection speed. Please try with a smaller file or better connection."}:{isValid:!0}},Te=async(e,t=0)=>{try{(()=>{const{user:e}=ae.getState();if(!e)throw new Error("Authentication required for thumbnail generation")})()}catch(r){throw r}if(!(()=>{if(!/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))return!0;const e=navigator.deviceMemory;return!(e&&e<2)})())throw new Error("Insufficient device memory for thumbnail generation. Please try uploading without a custom thumbnail.");return new Promise(((n,o)=>{const i=/Android/i.test(navigator.userAgent),a=document.createElement("video");let s;a.preload="metadata",a.muted=!0,a.playsInline=!0,a.crossOrigin="anonymous",i&&(a.controls=!1,a.autoplay=!1,a.style.maxWidth="320px",a.style.maxHeight="240px");try{s=URL.createObjectURL(e),a.src=s}catch(r){return void o(new Error("Failed to create video URL: "+r))}const c=setTimeout((()=>{URL.revokeObjectURL(s),o(new Error("Thumbnail generation timed out"))}),i?15e3:1e4);a.onloadedmetadata=()=>{try{const{user:e}=ae.getState();if(!e)return clearTimeout(c),URL.revokeObjectURL(s),void o(new Error("Authentication lost during thumbnail generation"));t>a.duration&&(t=a.duration/2),i?setTimeout((()=>{try{const{user:e}=ae.getState();if(!e)return clearTimeout(c),URL.revokeObjectURL(s),void o(new Error("Authentication lost during thumbnail generation"));a.currentTime=t}catch(e){clearTimeout(c),URL.revokeObjectURL(s),o(new Error("Failed to seek video on Android: "+e))}}),100):a.currentTime=t}catch(r){clearTimeout(c),URL.revokeObjectURL(s),o(new Error("Failed to seek video: "+r))}},a.onseeked=()=>{try{clearTimeout(c);const{user:e}=ae.getState();if(!e)return URL.revokeObjectURL(s),void o(new Error("Authentication lost during thumbnail generation"));const t=()=>{try{const{user:e}=ae.getState();if(!e)return URL.revokeObjectURL(s),void o(new Error("Authentication lost during thumbnail generation"));const t=document.createElement("canvas"),r=a.videoWidth||640,c=a.videoHeight||480;if(i){const e=800,n=r/c;r>c?(t.width=Math.min(r,e),t.height=t.width/n):(t.height=Math.min(c,e),t.width=t.height*n)}else t.width=r,t.height=c;const u=t.getContext("2d");if(!u)return URL.revokeObjectURL(s),void o(new Error("Failed to get canvas context"));u.drawImage(a,0,0,t.width,t.height);const l=i?.7:.8;t.toBlob((e=>{e?(URL.revokeObjectURL(s),n(e)):(URL.revokeObjectURL(s),o(new Error("Failed to generate thumbnail")))}),"image/jpeg",l)}catch(r){clearTimeout(c),URL.revokeObjectURL(s),o(new Error("Error during thumbnail generation: "+r))}};i?setTimeout(t,100):t()}catch(r){clearTimeout(c),URL.revokeObjectURL(s),o(new Error("Error during thumbnail generation: "+r))}},a.onerror=e=>{clearTimeout(c),URL.revokeObjectURL(s),o(new Error("Error loading video: "+e))};try{a.load()}catch(r){clearTimeout(c),URL.revokeObjectURL(s),o(new Error("Failed to load video: "+r))}}))},je=async(e,t=3)=>new Promise(((r,n)=>{const o=document.createElement("video");o.preload="metadata",o.muted=!0;const i=URL.createObjectURL(e);o.src=i,o.onloadedmetadata=async()=>{try{const n=[],a=o.duration;for(let r=0;r<t;r++){const o=a*(r+1)/(t+1),i=await Te(e,o);n.push(i)}URL.revokeObjectURL(i),r(n)}catch(a){URL.revokeObjectURL(i),n(a)}},o.onerror=()=>{URL.revokeObjectURL(i),n(new Error("Error loading video"))},o.load()})),Ae=()=>/Android/i.test(navigator.userAgent),Ie=e=>{const t=Ae(),r=t?62914560:52428800;if(e.size>r)return{isValid:!1,error:`File is too large. Maximum size is ${r/1048576}MB. Your file is ${(e.size/1048576).toFixed(2)}MB.`};const n=["video/mp4","video/webm","video/quicktime","video/x-msvideo","video/3gpp","video/x-ms-wmv","video/avi","video/mov","video/x-flv","video/x-matroska","video/3gp","video/mp2t","video/x-m4v"].includes(e.type)||e.type.startsWith("video/");if(!n&&t){const t=[".mp4",".webm",".mov",".avi",".3gp",".mkv",".flv",".wmv"],r=e.name.toLowerCase();if(!t.some((e=>r.endsWith(e))))return{isValid:!1,error:"Unsupported video format. Please use MP4, WebM, MOV, AVI, or 3GP."}}else if(!n)return{isValid:!1,error:"Unsupported video format. Please use MP4, WebM, MOV, or AVI."};return{isValid:!0}},Me=e=>{const t=Ae();if(e.size>5242880)return{isValid:!1,error:`Thumbnail is too large. Maximum size is 5MB. Your file is ${(e.size/1048576).toFixed(2)}MB.`};const r=["image/jpeg","image/jpg","image/png","image/webp","image/gif"].includes(e.type)||e.type.startsWith("image/");if(!r&&t){const t=[".jpg",".jpeg",".png",".webp",".gif"],r=e.name.toLowerCase();if(!t.some((e=>r.endsWith(e))))return{isValid:!1,error:"Unsupported image format. Please use JPG, PNG, WebP, or GIF."}}else if(!r)return{isValid:!1,error:"Unsupported image format. Please use JPG, PNG, WebP, or GIF."};return{isValid:!0}},Fe=()=>{const e=[];window.File&&window.FileReader&&window.FileList&&window.Blob||e.push("File API");const t=document.createElement("canvas");t.getContext&&t.getContext("2d")||e.push("Canvas 2D");return document.createElement("video").canPlayType||e.push("Video element"),window.URL&&window.URL.createObjectURL||e.push("Object URLs"),{isSupported:0===e.length,missingFeatures:e}},Ve=async(e,t)=>new Promise(((r,n)=>{const{user:o}=ae.getState();if(!o)return void n(new Error("User not authenticated"));let i=!1;const a=((e,t=1e3)=>{let r,n=!1;const o=()=>{r&&clearInterval(r),n=!1};return n||(n=!0,r=setInterval((()=>{const{user:t}=ae.getState();t||(o(),e())}),t)),o})((()=>{i||(i=!0,t?.(),n(new Error("Authentication lost during operation")))}));e().then((e=>{i||(i=!0,a(),r(e))})).catch((e=>{i||(i=!0,a(),n(e))}))})),_e=async e=>new Promise(((t,r)=>{const{user:n}=ae.getState();n?setTimeout((()=>{const{user:e}=ae.getState();e?t():r(new Error("Authentication lost during delay"))}),e):r(new Error("User not authenticated"))})),We=()=>{try{const{user:e,profile:t}=ae.getState();if(e&&t){const r={userId:e.id,userEmail:e.email,profileId:t.id,timestamp:Date.now()};localStorage.setItem("auth_backup",JSON.stringify(r))}}catch(e){}};export{Fe as A,Ae as B,We as C,Ie as D,Me as E,_e as F,Ve as G,je as H,Ce as I,Y as S,re as a,oe as b,ye as c,be as d,ve as e,te as f,ne as g,we as h,K as i,X as j,se as k,ce as l,de as m,ie as n,ge as o,ue as p,fe as q,Se as r,me as s,Oe as t,ae as u,Re as v,ee as w,Pe as x,$e as y,Ue as z};
