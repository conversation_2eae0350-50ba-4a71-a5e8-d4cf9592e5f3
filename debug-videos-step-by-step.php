<?php
// Step-by-step debug for videos API
$debug = [];

try {
    $debug['step1'] = 'Starting videos debug';
    
    // Test headers
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
    header('Access-Control-Allow-Credentials: true');
    $debug['step2'] = 'Headers set';
    
    // Test database connection
    require_once 'config/database.php';
    $debug['step3'] = 'Database config loaded';
    
    $database = new Database();
    $db = $database->getConnection();
    $debug['step4'] = 'Database connection created';
    
    // Test simple query
    $testQuery = "SELECT COUNT(*) as count FROM videos";
    $testStmt = $db->prepare($testQuery);
    $testStmt->execute();
    $result = $testStmt->fetch();
    $debug['step5'] = 'Database query successful';
    $debug['video_count'] = $result['count'];
    
    // Test video query with limit
    $videoQuery = "SELECT id, title, views FROM videos ORDER BY created_at DESC LIMIT 2";
    $videoStmt = $db->prepare($videoQuery);
    $videoStmt->execute();
    $videos = $videoStmt->fetchAll(PDO::FETCH_ASSOC);
    $debug['step6'] = 'Video query successful';
    $debug['sample_videos'] = $videos;
    
    echo json_encode([
        'success' => true,
        'message' => 'Videos debug completed successfully',
        'debug' => $debug
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug' => $debug,
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
