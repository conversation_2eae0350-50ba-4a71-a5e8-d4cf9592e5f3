/**
 * Enhanced API Client
 * Provides secure communication with the authentication backend
 */

import { User, SignUpData } from '../stores/authStore';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  session_id: string;
  expires_in: number;
}

export interface RegisterResponse {
  user: User;
  message: string;
}

export interface RefreshTokenResponse {
  token: string;
  expires_in: number;
}

class ApiClient {
  private baseURL: string;
  private token: string | null = null;
  
  constructor() {
    // Use environment variable or fallback to current domain
    this.baseURL = import.meta.env.VITE_API_URL || '/api';
    console.log('🌐 API Client initialized with baseURL:', this.baseURL);
    console.log('🔧 CORS FIX ACTIVE - Build 17512424-CORS-FIX');
  }
  
  /**
   * Set authentication token for requests
   */
  setToken(token: string | null) {
    this.token = token;
  }
  
  /**
   * Get current authentication token
   */
  getToken(): string | null {
    return this.token;
  }
  
  /**
   * Make HTTP request with enhanced error handling
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    console.log('🌐 API Request:', { url, endpoint, baseURL: this.baseURL });

    const defaultHeaders: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    // Add authorization header if token exists
    if (this.token) {
      defaultHeaders['Authorization'] = `Bearer ${this.token}`;
    }
    
    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
      // Remove credentials to avoid CORS wildcard conflict
      // credentials: 'include',
    };
    
    try {
      const response = await fetch(url, config);
      
      // Handle different content types
      let data;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        const text = await response.text();
        data = { message: text };
      }
      
      // Handle HTTP errors
      if (!response.ok) {
        return {
          success: false,
          error: data.error || data.message || `HTTP ${response.status}: ${response.statusText}`,
          data: data
        };
      }
      
      return {
        success: true,
        data: data.data || data,
        message: data.message
      };
      
    } catch (error) {
      console.error('API Request failed:', error);
      
      if (error instanceof TypeError && error.message.includes('fetch')) {
        return {
          success: false,
          error: 'Network error. Please check your connection and try again.'
        };
      }
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred'
      };
    }
  }
  
  /**
   * User Registration
   */
  async register(userData: SignUpData): Promise<ApiResponse<RegisterResponse>> {
    return this.request<RegisterResponse>('/auth.php', {
      method: 'POST',
      body: JSON.stringify({
        action: 'register',
        ...userData
      })
    });
  }
  
  /**
   * User Login
   */
  async login(identifier: string, password: string): Promise<ApiResponse<LoginResponse>> {
    const response = await this.request<LoginResponse>('/auth.php', {
      method: 'POST',
      body: JSON.stringify({
        action: 'login',
        identifier,
        password
      })
    });
    
    // Set token if login successful
    if (response.success && response.data?.token) {
      this.setToken(response.data.token);
    }
    
    return response;
  }
  
  /**
   * User Logout
   */
  async logout(sessionId?: string): Promise<ApiResponse> {
    const response = await this.request('/auth.php', {
      method: 'POST',
      body: JSON.stringify({
        action: 'logout',
        session_id: sessionId
      })
    });
    
    // Clear token on logout
    this.setToken(null);
    
    return response;
  }
  
  /**
   * Get Current User
   */
  async getCurrentUser(): Promise<ApiResponse<{ user: User }>> {
    return this.request<{ user: User }>('/auth.php', {
      method: 'GET'
    });
  }
  
  /**
   * Email Verification
   */
  async verifyEmail(token: string): Promise<ApiResponse> {
    return this.request('/auth.php', {
      method: 'POST',
      body: JSON.stringify({
        action: 'verify_email',
        token
      })
    });
  }
  
  /**
   * Request Password Reset
   */
  async requestPasswordReset(email: string): Promise<ApiResponse> {
    return this.request('/auth.php', {
      method: 'POST',
      body: JSON.stringify({
        action: 'request_password_reset',
        email
      })
    });
  }
  
  /**
   * Reset Password
   */
  async resetPassword(token: string, password: string): Promise<ApiResponse> {
    return this.request('/auth.php', {
      method: 'POST',
      body: JSON.stringify({
        action: 'reset_password',
        token,
        password
      })
    });
  }
  
  /**
   * Update User Profile
   */
  async updateProfile(data: Partial<User>): Promise<ApiResponse> {
    return this.request('/auth.php', {
      method: 'POST',
      body: JSON.stringify({
        action: 'update_profile',
        ...data
      })
    });
  }
  
  /**
   * Change Password
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse> {
    return this.request('/auth.php', {
      method: 'POST',
      body: JSON.stringify({
        action: 'change_password',
        current_password: currentPassword,
        new_password: newPassword
      })
    });
  }
  
  /**
   * Refresh Authentication Token
   */
  async refreshToken(token: string): Promise<ApiResponse<RefreshTokenResponse>> {
    const response = await this.request<RefreshTokenResponse>('/auth.php', {
      method: 'POST',
      body: JSON.stringify({
        action: 'refresh_token',
        token
      })
    });
    
    // Update token if refresh successful
    if (response.success && response.data?.token) {
      this.setToken(response.data.token);
    }
    
    return response;
  }
  
  /**
   * Delete User Account
   */
  async deleteAccount(): Promise<ApiResponse> {
    const response = await this.request('/auth.php', {
      method: 'POST',
      body: JSON.stringify({
        action: 'delete_account'
      })
    });
    
    // Clear token on account deletion
    if (response.success) {
      this.setToken(null);
    }
    
    return response;
  }
  
  /**
   * Upload File (with authentication)
   */
  async uploadFile(file: File, onProgress?: (progress: number) => void): Promise<ApiResponse> {
    const formData = new FormData();
    formData.append('file', file);
    
    const headers: HeadersInit = {};
    
    // Add authorization header if token exists
    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }
    
    try {
      const xhr = new XMLHttpRequest();
      
      return new Promise((resolve) => {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable && onProgress) {
            const progress = (event.loaded / event.total) * 100;
            onProgress(progress);
          }
        });
        
        xhr.addEventListener('load', () => {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve({
              success: xhr.status === 200,
              data: response.data || response,
              error: response.error || (xhr.status !== 200 ? `HTTP ${xhr.status}` : undefined)
            });
          } catch (error) {
            resolve({
              success: false,
              error: 'Invalid response format'
            });
          }
        });
        
        xhr.addEventListener('error', () => {
          resolve({
            success: false,
            error: 'Upload failed'
          });
        });
        
        xhr.open('POST', `${this.baseURL}/upload.php`);
        
        // Set headers
        Object.entries(headers).forEach(([key, value]) => {
          xhr.setRequestHeader(key, value);
        });
        
        xhr.withCredentials = true;
        xhr.send(formData);
      });
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }
  
  /**
   * Get Videos with pagination and filtering
   */
  async getVideos(params: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
    sort?: string;
    order?: string;
  } = {}): Promise<ApiResponse<{
    videos: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }>> {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.category && params.category !== 'all') searchParams.append('category', params.category);
    if (params.search) searchParams.append('search', params.search);
    if (params.sort) searchParams.append('sort', params.sort);
    if (params.order) searchParams.append('order', params.order);

    const endpoint = `/videos-simple.php?${searchParams.toString()}`;
    return this.request(endpoint);
  }

  /**
   * Get Single Video
   */
  async getVideo(id: string): Promise<ApiResponse<any>> {
    const endpoint = `/videos-simple.php?id=${id}`;
    console.log('🎬 API Client - getVideo called:', { id, endpoint, baseURL: this.baseURL });

    try {
      const result = await this.request(endpoint);
      console.log('🎬 API Client - getVideo response:', { success: result.success, hasData: !!result.data });
      return result;
    } catch (error) {
      console.error('🚨 API Client - getVideo error:', error);
      throw error;
    }
  }

  /**
   * Generic authenticated request for other endpoints
   */
  async authenticatedRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, options);
  }
}

// Create and export singleton instance
export const apiClient = new ApiClient();

// Export class for testing or custom instances
export { ApiClient };