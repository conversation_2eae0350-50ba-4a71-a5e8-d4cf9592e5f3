<?php
/**
 * MySQL Authentication API for cPanel Deployment
 * Converts Node.js auth-server.js functionality to PHP
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/config/database.php';

class MySQLAuth {
    private $db;
    
    public function __construct() {
        $database = Database::getInstance();
        $this->db = $database->getConnection();
    }
    
    public function signUp($email, $password, $name) {
        try {
            // Check if user already exists
            $stmt = $this->db->prepare("SELECT id FROM user WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                return ['success' => false, 'error' => 'User already exists'];
            }
            
            // Check if username already exists in profiles
            $username = $this->generateUsername($email, $name);
            $stmt = $this->db->prepare("SELECT id FROM profiles WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->fetch()) {
                $username = $username . '_' . substr($this->generateId(), 0, 6);
            }
            
            // Generate user ID and timestamps
            $userId = $this->generateId();
            $profileId = $this->generateId();
            $timestamp = time() * 1000; // Convert to milliseconds
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            // Get current admin count
            $adminCount = $this->getAdminCount();
            $isAdmin = $adminCount < 2; // First two users become admins
            $isApproved = $isAdmin; // Admins are auto-approved, others need approval
            
            // Start transaction
            $this->db->beginTransaction();
            
            // Insert user
            $stmt = $this->db->prepare("
                INSERT INTO user (id, email, emailVerified, name, createdAt, updatedAt) 
                VALUES (?, ?, 0, ?, ?, ?)
            ");
            $stmt->execute([$userId, $email, $name, $timestamp, $timestamp]);
            
            // Insert account with password
            $accountId = $this->generateId();
            $stmt = $this->db->prepare("
                INSERT INTO account (id, accountId, providerId, userId, password, createdAt, updatedAt) 
                VALUES (?, ?, 'credential', ?, ?, ?, ?)
            ");
            $stmt->execute([$accountId, $email, $userId, $hashedPassword, $timestamp, $timestamp]);
            
            // Insert profile with admin status
            $stmt = $this->db->prepare("
                INSERT INTO profiles (id, username, password_hash, is_admin, is_approved, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, NOW(), NOW())
            ");
            $stmt->execute([$profileId, $username, $hashedPassword, $isAdmin, $isApproved]);
            
            // Update admin counter if user is admin
            if ($isAdmin) {
                $this->incrementAdminCount();
            }
            
            $this->db->commit();
            
            $message = $isAdmin ? 'Registration successful! You have been granted admin privileges.' : 
                      'Registration successful! Your account is pending admin approval.';
            
            return [
                'success' => true, 
                'user' => [
                    'id' => $userId, 
                    'email' => $email, 
                    'name' => $name,
                    'username' => $username,
                    'is_admin' => $isAdmin,
                    'is_approved' => $isApproved
                ],
                'message' => $message
            ];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('SignUp error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Registration failed'];
        }
    }
    
    public function signIn($email, $password) {
        try {
            // Get user, password, and profile info
            $stmt = $this->db->prepare("
                SELECT u.id, u.email, u.name, a.password, p.username, p.is_admin, p.is_approved 
                FROM user u 
                JOIN account a ON u.id = a.userId 
                LEFT JOIN profiles p ON u.id = p.id
                WHERE u.email = ? AND a.providerId = 'credential'
            ");
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user || !password_verify($password, $user['password'])) {
                return ['success' => false, 'error' => 'Invalid credentials'];
            }
            
            // Check if user is approved (admins are always approved)
            if (!$user['is_admin'] && !$user['is_approved']) {
                return ['success' => false, 'error' => 'Your account is pending admin approval'];
            }
            
            // Create session
            $sessionId = $this->generateId();
            $token = $this->generateToken();
            $timestamp = time() * 1000;
            $expiresAt = $timestamp + (7 * 24 * 60 * 60 * 1000); // 7 days
            
            $stmt = $this->db->prepare("
                INSERT INTO session (id, expiresAt, token, createdAt, updatedAt, userId, ipAddress, userAgent) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $sessionId, $expiresAt, $token, $timestamp, $timestamp, 
                $user['id'], $_SERVER['REMOTE_ADDR'] ?? '', $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
            
            return [
                'success' => true, 
                'token' => $token,
                'user' => [
                    'id' => $user['id'], 
                    'email' => $user['email'], 
                    'name' => $user['name'],
                    'username' => $user['username'],
                    'is_admin' => (bool)$user['is_admin'],
                    'is_approved' => (bool)$user['is_approved']
                ]
            ];
            
        } catch (Exception $e) {
            error_log('SignIn error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Login failed'];
        }
    }
    
    public function getSession($token) {
        try {
            $stmt = $this->db->prepare("
                SELECT s.userId, s.expiresAt, u.email, u.name, p.username, p.is_admin, p.is_approved 
                FROM session s 
                JOIN user u ON s.userId = u.id 
                LEFT JOIN profiles p ON u.id = p.id
                WHERE s.token = ? AND s.expiresAt > ?
            ");
            $stmt->execute([$token, time() * 1000]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$session) {
                return ['success' => false, 'error' => 'Invalid or expired session'];
            }
            
            return [
                'success' => true,
                'user' => [
                    'id' => $session['userId'], 
                    'email' => $session['email'], 
                    'name' => $session['name'],
                    'username' => $session['username'],
                    'is_admin' => (bool)$session['is_admin'],
                    'is_approved' => (bool)$session['is_approved']
                ]
            ];
            
        } catch (Exception $e) {
            error_log('GetSession error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Session validation failed'];
        }
    }
    
    public function signOut($token) {
        try {
            $stmt = $this->db->prepare("DELETE FROM session WHERE token = ?");
            $stmt->execute([$token]);
            return ['success' => true];
        } catch (Exception $e) {
            error_log('SignOut error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Logout failed'];
        }
    }
    
    public function approveUser($userId) {
        try {
            // Verify admin permission would go here
            $stmt = $this->db->prepare("UPDATE profiles SET is_approved = TRUE WHERE id = ?");
            $stmt->execute([$userId]);
            
            if ($stmt->rowCount() > 0) {
                return ['success' => true, 'message' => 'User approved successfully'];
            } else {
                return ['success' => false, 'error' => 'User not found'];
            }
        } catch (Exception $e) {
            error_log('ApproveUser error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to approve user'];
        }
    }
    
    public function getPendingUsers() {
        try {
            $stmt = $this->db->prepare("
                SELECT p.id, p.username, u.email, p.created_at 
                FROM profiles p
                JOIN user u ON p.id = u.id
                WHERE p.is_approved = FALSE AND p.is_admin = FALSE
                ORDER BY p.created_at DESC
            ");
            $stmt->execute();
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return ['success' => true, 'users' => $users];
        } catch (Exception $e) {
            error_log('GetPendingUsers error: ' . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to get pending users'];
        }
    }
    
    private function generateId() {
        return bin2hex(random_bytes(16));
    }
    
    private function generateToken() {
        return bin2hex(random_bytes(32));
    }
    
    private function generateUsername($email, $name) {
        // Try to create username from name first, then email
        $username = '';
        if ($name) {
            $username = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $name));
        }
        if (empty($username)) {
            $username = strtolower(explode('@', $email)[0]);
            $username = preg_replace('/[^a-zA-Z0-9]/', '', $username);
        }
        return substr($username, 0, 20); // Limit length
    }
    
    private function getAdminCount() {
        try {
            $stmt = $this->db->query("SELECT admin_count FROM admin_counter LIMIT 1");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? $result['admin_count'] : 0;
        } catch (Exception $e) {
            // If table doesn't exist, assume 0 admins
            return 0;
        }
    }
    
    private function incrementAdminCount() {
        try {
            $this->db->exec("UPDATE admin_counter SET admin_count = admin_count + 1, updated_at = CURRENT_TIMESTAMP");
        } catch (Exception $e) {
            // If table doesn't exist, create it
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS admin_counter (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    admin_count INT DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            $this->db->exec("INSERT INTO admin_counter (admin_count) VALUES (1)");
        }
    }
}

// Handle requests
try {
    $auth = new MySQLAuth();
    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true) ?? [];
    $action = $_GET['action'] ?? '';
    
    switch ($method) {
        case 'POST':
            switch ($action) {
                case 'signup':
                    $result = $auth->signUp($input['email'] ?? '', $input['password'] ?? '', $input['name'] ?? '');
                    break;
                case 'signin':
                    $result = $auth->signIn($input['email'] ?? '', $input['password'] ?? '');
                    break;
                case 'signout':
                    $token = $input['token'] ?? $_SERVER['HTTP_AUTHORIZATION'] ?? '';
                    $token = str_replace('Bearer ', '', $token);
                    $result = $auth->signOut($token);
                    break;
                case 'approve-user':
                    $result = $auth->approveUser($input['user_id'] ?? '');
                    break;
                case 'get-pending-users':
                    $result = $auth->getPendingUsers();
                    break;
                default:
                    $result = ['success' => false, 'error' => 'Invalid action'];
            }
            break;
            
        case 'GET':
            if ($action === 'session') {
                $token = $_GET['token'] ?? $_SERVER['HTTP_AUTHORIZATION'] ?? '';
                $token = str_replace('Bearer ', '', $token);
                $result = $auth->getSession($token);
            } else {
                $result = ['success' => false, 'error' => 'Invalid action'];
            }
            break;
            
        default:
            $result = ['success' => false, 'error' => 'Method not allowed'];
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    error_log('Auth API error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}
?>