import{r as e,a as t}from"./react-core-B9nwsbCA.js";import{c as r}from"./state-4gBW_4Nx.js";var o=e;
/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useState,i=o.useEffect,s=o.useLayoutEffect,c=o.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!n(e,r)}catch(o){return!0}}"undefined"==typeof window||void 0===window.document||window.document.createElement;void 0!==o.useSyncExternalStore&&o.useSyncExternalStore;var l=Object.prototype.hasOwnProperty;const d=new WeakMap,h=()=>{},p=h(),g=Object,f=e=>e===p,m=e=>"function"==typeof e,w=(e,t)=>({...e,...t}),v={},y={},b="undefined",E=typeof window!=b,S=typeof document!=b,L=E&&"Deno"in window;let O=!0;const[k,R]=E&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[h,h],U={isOnline:()=>O,isVisible:()=>{const e=S&&document.visibilityState;return f(e)||"hidden"!==e}},x={initFocus:e=>(S&&document.addEventListener("visibilitychange",e),k("focus",e),()=>{S&&document.removeEventListener("visibilitychange",e),R("focus",e)}),initReconnect:e=>{const t=()=>{O=!0,e()},r=()=>{O=!1};return k("online",t),k("offline",r),()=>{R("online",t),R("offline",r)}}};t.useId;const $=!E||L,C=$?e.useEffect:e.useLayoutEffect,j="undefined"!=typeof navigator&&navigator.connection,T=!$&&j&&(["slow-2g","2g"].includes(j.effectiveType)||j.saveData),P=new WeakMap,I=(e,t)=>g.prototype.toString.call(e)===`[object ${t}]`;let A=0;const M=e=>{const t=typeof e,r=I(e,"Date"),o=I(e,"RegExp"),n=I(e,"Object");let a,i;if(g(e)!==e||r||o)a=r?e.toJSON():"symbol"==t?e.toString():"string"==t?JSON.stringify(e):""+e;else{if(a=P.get(e),a)return a;if(a=++A+"~",P.set(e,a),Array.isArray(e)){for(a="@",i=0;i<e.length;i++)a+=M(e[i])+",";P.set(e,a)}if(n){a="#";const t=g.keys(e).sort();for(;!f(i=t.pop());)f(e[i])||(a+=i+":"+M(e[i])+",");P.set(e,a)}}return a},F=e=>{if(m(e))try{e=e()}catch(r){e=""}const t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?M(e):"",t]};let _=0;const N=()=>++_;async function W(...e){const[t,r,o,n]=e,a=w({populateCache:!0,throwOnError:!0},"boolean"==typeof n?{revalidate:n}:n||{});let i=a.populateCache;const s=a.rollbackOnError;let c=a.optimisticData;const u=a.throwOnError;if(m(r)){const e=r,o=[],n=t.keys();for(const r of n)!/^\$(inf|sub)\$/.test(r)&&e(t.get(r)._k)&&o.push(r);return Promise.all(o.map(l))}return l(r);async function l(r){const[n]=F(r);if(!n)return;const[l,h]=((e,t)=>{const r=d.get(e);return[()=>!f(t)&&e.get(t)||v,o=>{if(!f(t)){const n=e.get(t);t in y||(y[t]=n),r[5](t,w(n,o),n||v)}},r[6],()=>!f(t)&&t in y?y[t]:!f(t)&&e.get(t)||v]})(t,n),[g,b,E,S]=d.get(t),L=()=>{const e=g[n];return(m(a.revalidate)?a.revalidate(l().data,r):!1!==a.revalidate)&&(delete E[n],delete S[n],e&&e[0])?e[0](2).then((()=>l().data)):l().data};if(e.length<3)return L();let O,k=o;const R=N();b[n]=[R,0];const U=!f(c),x=l(),$=x.data,C=x._c,j=f(C)?$:C;if(U&&(c=m(c)?c(j,$):c,h({data:c,_c:j})),m(k))try{k=k(j)}catch(T){O=T}if(k&&m(k.then)){if(k=await k.catch((e=>{O=e})),R!==b[n][0]){if(O)throw O;return k}O&&U&&(e=>"function"==typeof s?s(e):!1!==s)(O)&&(i=!0,h({data:j,_c:p}))}if(i&&!O)if(m(i)){const e=i(k,j);h({data:e,error:p,_c:p})}else h({data:k,error:p,_c:p});if(b[n][1]=N(),Promise.resolve(L()).then((()=>{h({_c:p})})),!O)return k;if(u)throw O}}const D=(e,t)=>{for(const r in e)e[r][0]&&e[r][0](t)},V=(e,t)=>{if(!d.has(e)){const r=w(x,t),o=Object.create(null),n=W.bind(p,e);let a=h;const i=Object.create(null),s=(e,t)=>{const r=i[e]||[];return i[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},c=(t,r,o)=>{e.set(t,r);const n=i[t];if(n)for(const e of n)e(r,o)},u=()=>{if(!d.has(e)&&(d.set(e,[o,Object.create(null),Object.create(null),Object.create(null),n,c,s]),!$)){const t=r.initFocus(setTimeout.bind(p,D.bind(p,o,0))),n=r.initReconnect(setTimeout.bind(p,D.bind(p,o,1)));a=()=>{t&&t(),n&&n(),d.delete(e)}}};return u(),[e,n,u,a]}return[e,d.get(e)[4]]},q=function e(t,r){var o,n;if(t===r)return!0;if(t&&r&&(o=t.constructor)===r.constructor){if(o===Date)return t.getTime()===r.getTime();if(o===RegExp)return t.toString()===r.toString();if(o===Array){if((n=t.length)===r.length)for(;n--&&e(t[n],r[n]););return-1===n}if(!o||"object"==typeof t){for(o in n=0,t){if(l.call(t,o)&&++n&&!l.call(r,o))return!1;if(!(o in r)||!e(t[o],r[o]))return!1}return Object.keys(r).length===n}}return t!=t&&r!=r},[B,z]=V(new Map),J=w({onLoadingSlow:h,onSuccess:h,onError:h,onErrorRetry:(e,t,r,o,n)=>{const a=r.errorRetryCount,i=n.retryCount,s=~~((Math.random()+.5)*(1<<(i<8?i:8)))*r.errorRetryInterval;!f(a)&&i>a||setTimeout(o,s,n)},onDiscarded:h,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:T?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:T?5e3:3e3,compare:q,isPaused:()=>!1,cache:B,mutate:z,fallback:{}},U),G=e.createContext({}),H=E&&window.__SWR_DEVTOOLS_USE__;(H?window.__SWR_DEVTOOLS_USE__:[]).concat((e=>(t,r,o)=>e(t,r&&((...e)=>{const[o]=F(t),[,,,n]=d.get(B);if(o.startsWith("$inf$"))return r(...e);const a=n[o];return f(a)?r(...e):(delete n[o],a)}),o))),H&&(window.__SWR_DEVTOOLS_REACT__=t),t.use;const Y=g.defineProperty((t=>{const{value:r}=t,o=e.useContext(G),n=m(r),a=e.useMemo((()=>n?r(o):r),[n,o,r]),i=e.useMemo((()=>n?a:((e,t)=>{const r=w(e,t);if(t){const{use:o,fallback:n}=e,{use:a,fallback:i}=t;o&&a&&(r.use=o.concat(a)),n&&i&&(r.fallback=w(n,i))}return r})(o,a)),[n,o,a]),s=a&&a.provider,c=e.useRef(p);s&&!c.current&&(c.current=V(s(i.cache||B),a));const u=c.current;return u&&(i.cache=u[0],i.mutate=u[1]),C((()=>{if(u)return u[2]&&u[2](),u[3]}),[]),e.createElement(G.Provider,w(t,{value:i}))}),"defaultValue",{value:J});const K=new class{constructor(e="https://www.bluefilmx.com/api"){this.baseUrl=e}async request(e,t={}){const r=`${this.baseUrl}${e}`;try{const e={headers:{"Content-Type":"application/json",...t.headers},credentials:"include"},o=await fetch(r,{...e,...t});if(!o.ok){const e=await o.text();throw new Error(`API Error: ${o.status} - ${e}`)}return await o.json()}catch(o){if(o instanceof TypeError&&o.message.includes("fetch"))throw new Error("Network error during upload. Please check your internet connection and try again.");throw o}}async getVideos(e={}){const t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),e.category&&"all"!==e.category&&t.append("category",e.category),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),e.order&&t.append("order",e.order);const r=`/videos.php?${t.toString()}`;return this.request(r)}async getVideo(e){return this.request(`/videos.php/${e}`)}async createVideo(e){return this.request("/videos.php",{method:"POST",body:JSON.stringify(e)})}async updateVideo(e,t){return this.request(`/videos.php/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteVideo(e){return this.request(`/videos.php/${e}`,{method:"DELETE"})}async getCategories(){return this.request("/categories.php")}async login(e,t){return this.request("/auth.php?action=login",{method:"POST",body:JSON.stringify({username:e,password:t})})}async register(e,t){return this.request("/auth.php?action=register",{method:"POST",body:JSON.stringify({username:e,password:t})})}async logout(){return this.request("/auth.php?action=logout",{method:"POST"})}async getCurrentUser(){try{return await this.request("/auth.php")}catch(e){if(e instanceof Error&&e.message.includes("401"))throw new Error("Not authenticated");throw e}}async getFavorites(){return this.request("/favorites.php")}async addToFavorites(e){return this.request("/favorites.php",{method:"POST",body:JSON.stringify({video_id:e})})}async removeFromFavorites(e){return this.request("/favorites.php",{method:"DELETE",body:JSON.stringify({video_id:e})})}async uploadFile(e,t){try{const r=new FormData;r.append("file",e),r.append("type",t);return await this.request("/upload.php",{method:"POST",body:r,headers:{}})}catch(r){throw r}}},Q=e=>{if(!e)return"";let t=e.trim().replace(/[\r\n\t]/g,"").replace(/%0A/g,"").replace(/%0D/g,"").replace(/%09/g,"");if(t.includes("supabase.co/storage/v1/object/public/")||t.includes("bluefilmx.com/media/")||t.includes("premium34.web-hosting.com")||t.startsWith("http://")||t.startsWith("https://"))return t;if(t.startsWith("/storage/")||t.startsWith("storage/")){return`https://vsnsglgyapexhwyfylic.supabase.co/${t.startsWith("/")?t.slice(1):t}`}if(t.startsWith("/media/")||t.startsWith("media/")){return`https://www.bluefilmx.com${t.startsWith("/")?t:`/${t}`}`}return t.includes("://")||t.startsWith("/")?t:`https://www.bluefilmx.com/media/${t}`},X=e=>{const t=Q(e);return t||ee()},Z=e=>{const t=Q(e);return t||""},ee=()=>"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='640' height='360' viewBox='0 0 640 360'%3E%3Crect width='640' height='360' fill='%23374151'/%3E%3Cg transform='translate(320,180)'%3E%3Ccircle cx='0' cy='0' r='30' fill='%236B7280'/%3E%3Cpolygon points='-10,-15 -10,15 20,0' fill='%23F3F4F6'/%3E%3C/g%3E%3Ctext x='320' y='320' font-family='Arial, sans-serif' font-size='18' fill='%239CA3AF' text-anchor='middle'%3ENo Thumbnail%3C/text%3E%3C/svg%3E",te=async(e,t)=>{const{priority:r="high",maxPreload:o=6,includeNextPage:n=!1}=t||{},a=e.slice(0,o).map((async(e,t)=>{if(e.thumbnailUrl){const o=document.createElement("link");o.rel="preload",o.as="image";const n=t<3?640:320;o.href=oe(X(e.thumbnailUrl),n),"high"===r&&t<3?o.setAttribute("fetchpriority","high"):o.setAttribute("fetchpriority","low"),document.head.appendChild(o)}}));await Promise.allSettled(a)},re=e=>{if(!e)return"";let t=Q(e);return t=t.replace(/([^:]\/)\/+/g,"$1"),t.startsWith("http://")&&"https:"===window.location.protocol?t.replace("http://","https://"):t},oe=(e,t,r)=>{const o=Q(e);if(!o||!t)return o;if(o.includes("supabase.co/storage/v1/object/public/"))try{const e=new URL(o);e.searchParams.set("width",t.toString());const n=t<=320?"75":t<=640?"80":"85";return e.searchParams.set("quality",n),r&&"auto"!==r&&e.searchParams.set("format",r),e.toString()}catch(n){return o}return o},ne=()=>{const e=document.createElement("canvas");e.width=1,e.height=1;try{if(0===e.toDataURL("image/avif").indexOf("data:image/avif"))return"avif";if(0===e.toDataURL("image/webp").indexOf("data:image/webp"))return"webp"}catch(t){}return"auto"},ae=e=>oe(e,40,"auto"),ie=r(((e,t)=>({user:null,isLoading:!0,isApproved:!1,signUp:async(t,r)=>{try{const o=await K.register(t,r);if(!o.success||!o.data.user)throw new Error("Registration failed");e({user:o.data.user,isApproved:o.data.user.is_approved||!1,isLoading:!1})}catch(o){throw o}},signIn:async(t,r)=>{try{const o=await K.login(t,r);if(!o.success||!o.data.user)throw new Error("Login failed");e({user:o.data.user,isApproved:o.data.user.is_approved||!1,isLoading:!1})}catch(o){throw o}},signOut:async()=>{try{e({user:null,isApproved:!1,isLoading:!1}),await K.logout(),"undefined"!=typeof window&&(localStorage.removeItem("user-preferences"),localStorage.removeItem("upload-progress"))}catch(t){}},loadUser:async()=>{try{e({isLoading:!0});const t=await K.getCurrentUser();t.success&&t.data.user?e({user:t.data.user,isApproved:t.data.user.is_approved||!1,isLoading:!1}):e({user:null,isApproved:!1,isLoading:!1})}catch(t){const r=t instanceof Error?t.message:String(t);r.includes("401")||r.includes("Unauthorized")||r.includes("Not authenticated"),e({user:null,isApproved:!1,isLoading:!1})}}})));"undefined"!=typeof window&&ie.getState().loadUser();const se=(e="default")=>{const t=e.split("").reduce(((e,t)=>e+t.charCodeAt(0)),0),r=(e,t)=>e[t%e.length],o=[`mouth=${r(["smile","tongue","twinkle","vomit"],t)}`,`eyes=${r(["happy","hearts","stars","wink","winkWacky"],t+1)}`,`top=${r(["longHair","shortHair","eyepatch","hat","hijab","turban","bigHair","bob","bun"],t+2)}`,`accessories=${r(["kurt","prescription01","prescription02","round","sunglasses","wayfarers"],t+3)}`,`hairColor=${r(["auburn","black","blonde","brown","pastel","platinum","red","blue","pink"],t+4)}`,`facialHair=${r(["medium","light","majestic","fancy","magnum"],t+5)}`,`clothes=${r(["blazer","sweater","hoodie","overall","shirtCrewNeck"],t+6)}`,`fabric=${r(["denim","graphicShirt","stripes","dots"],t+7)}`,`backgroundColor=${r(["b6e3f4","c0aede","ffd5dc","ffdfbf","d1d4f9","c0e8d5"],t+8)}`].join("&");return`https://avatars.dicebear.com/api/avataaars/${encodeURIComponent(e)}.svg?${o}`},ce=()=>`https://avatars.dicebear.com/api/bottts/fallback.svg?${["backgroundColor=b6e3f4","colors=blue","mouthChance=100","sidesChance=100","topChance=100"].join("&")}`,ue=()=>{const e=[],t=[];try{const o=localStorage.getItem("sb-lfnxllcoixgfkasdjtnj-auth-token");if(o)try{const r=JSON.parse(o);r.expires_at&&new Date(1e3*r.expires_at)<new Date&&(e.push("Expired authentication token found"),t.push("Clear authentication data and re-login"))}catch(r){e.push("Corrupted authentication data"),t.push("Clear authentication storage")}const n=localStorage.getItem("app-version"),a="1.0.0";n&&n!==a&&(e.push(`Version mismatch: stored ${n}, current ${a}`),t.push("Clear cached data for new version"));let i=0;for(let e in localStorage)localStorage.hasOwnProperty(e)&&(i+=localStorage[e].length);i>5242880&&(e.push("Large localStorage usage detected"),t.push("Consider clearing non-essential cached data"));const s=localStorage.getItem("user-preferences-storage");if(s)try{const r=JSON.parse(s);r.state&&Object.keys(r.state.watchHistory||{}).length>1e3&&(e.push("Large watch history detected"),t.push("Consider clearing old watch history"))}catch(r){e.push("Corrupted user preferences data"),t.push("Reset user preferences")}}catch(o){e.push("Error checking browser state"),t.push("Clear all browser data")}return{hasConflicts:e.length>0,conflicts:e,recommendations:t}},le=new Map,de=()=>({size:le.size,keys:Array.from(le.keys())}),he=()=>({version:"1.0.0",timestamp:Date.now(),buildId:"dev"}),pe=async()=>{try{if("caches"in window){const e=await caches.keys();await Promise.all(e.map((e=>caches.delete(e))))}["user-preferences-storage","disclaimerAccepted","recentSearches"].forEach((e=>{try{localStorage.removeItem(e)}catch(t){}})),(()=>{try{const e=he();localStorage.setItem("app-version",e.version),localStorage.setItem("app-build-id",e.buildId),localStorage.setItem("version-updated",e.timestamp.toString())}catch(e){}})()}catch(e){}},ge=async()=>{try{return!(()=>{try{return localStorage.getItem("app-version")===he().version}catch(e){return!1}})()&&(await pe(),!0)}catch(e){return!1}},fe=e=>{let t;return t=window.setInterval((async()=>{try{const t=await fetch("/version.json?"+Date.now(),{cache:"no-cache"});if(t.ok){const r=await t.json(),o=he().version;r.version!==o&&e()}}catch(t){}}),3e5),()=>{t&&clearInterval(t)}},me=()=>{window.addEventListener("unhandledrejection",(e=>{const t=e.reason;if(t&&"object"==typeof t&&"message"in t){const r=String(t.message);if(r.includes("Cache")||r.includes("service-worker")||r.includes("Failed to execute 'put' on 'Cache'"))return void e.preventDefault()}})),window.addEventListener("error",(e=>{const t=e.error;t&&t.message&&(t.message.includes("Cache")||t.message.includes("service-worker")||t.message.includes("Failed to execute 'put' on 'Cache'"))&&e.preventDefault()}))},we=e=>e>=1e6?`${(e/1e6).toFixed(1)}M`:e>=1e3?`${(e/1e3).toFixed(1)}k`:e.toString(),ve=e=>{const t=Math.floor(e/3600),r=Math.floor(e%3600/60),o=Math.floor(e%60);return t>0?`${t}:${r.toString().padStart(2,"0")}:${o.toString().padStart(2,"0")}`:`${r}:${o.toString().padStart(2,"0")}`},ye=(e=640,t=360,r="No Image")=>`data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='${e}' height='${t}' viewBox='0 0 ${e} ${t}'%3E%3Crect width='${e}' height='${t}' fill='%23333'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial, sans-serif' font-size='24' fill='%23fff' text-anchor='middle' dominant-baseline='middle'%3E${r}%3C/text%3E%3C/svg%3E`,be=()=>ye(640,360,"No Thumbnail"),Ee=()=>{try{if("undefined"!=typeof sessionStorage){const e=sessionStorage.getItem("isMobileDevice");if(null!==e)return"true"===e}}catch(e){}return(()=>{if("undefined"==typeof window)return!1;const t=navigator.userAgent||navigator.vendor||window.opera,r="ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0,o=window.innerWidth<=768,n=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(t)||r&&o;try{"undefined"!=typeof sessionStorage&&sessionStorage.setItem("isMobileDevice",String(n))}catch(e){}return n})()},Se=[/^(.*?)(?:\s*[-:]\s*(?:Part|Episode|Ep\.?|#|S\d+E)\s*(\d+))(?:\s*[-:]\s*(.*))?$/i,/^(.*?)(?:\s*\((?:Part|Episode|Ep\.?|#|S\d+E)\s*(\d+)\))(?:\s*[-:]\s*(.*))?$/i,/^(.*?)\s+(\d+)$/i,/^(\d+)(?:\.|\s*-\s*)\s*(.*)$/i,/^(.*?)(?::\s*)(.*)(?:\s+)(\d+)(?:\s*[-:]\s*(.*))?$/i,/^(.*?)(?:\s*[\[\{]\s*(?:Part|Episode|Ep\.?|#)?\s*(\d+)\s*[\]\}])(?:\s*[-:]\s*(.*))?$/i,/^(.*?)(?:\s+(?:Season|S)\s*(\d+)\s*(?:Episode|Ep\.?|E)\s*(\d+))(?:\s*[-:]\s*(.*))?$/i,/^(.*?)(?:\s+S(\d+)E(\d+))(?:\s*[-:]\s*(.*))?$/i];function Le(e){if(!e||"string"!=typeof e)return null;const t=e.replace(/\s+/g," ").trim();if(!t)return null;for(let o=0;o<Se.length;o++){const e=Se[o],r=t.match(e);if(r){if(0===o||1===o||5===o){return{baseTitle:r[1].trim(),partNumber:parseInt(r[2],10)}}if(2===o){return{baseTitle:r[1].trim(),partNumber:parseInt(r[2],10)}}if(3===o){return{baseTitle:r[2].trim(),partNumber:parseInt(r[1],10)}}if(4===o){return{baseTitle:r[1].trim(),partNumber:parseInt(r[3],10)}}if(6===o||7===o){const e=r[1].trim(),t=parseInt(r[2],10),o=parseInt(r[3]||"0",10);return{baseTitle:e,partNumber:o>0?o:t}}}}const r=t.match(/^(\d+)$/);return r?{baseTitle:"Numbered Series",partNumber:parseInt(r[1],10)}:null}function Oe(e){let t=e.toLowerCase();return["the","a","an","and","or","but","in","on","at","to","for","with","by","of"].forEach((e=>{t=t.replace(new RegExp(`\\b${e}\\b`,"g"),"")})),t=t.replace(/[^\w\s]/g,"").replace(/\s+/g," ").trim(),t}function ke(e,t,r){if(e.length<5||t.length<5){return 1-Re(e,t)/Math.max(e.length,t.length)>=.9}if(e.includes(t)||t.includes(e))return!0;return 1-Re(e,t)/Math.max(e.length,t.length)>=r}function Re(e,t){const r=e.length,o=t.length,n=Array(r+1).fill(null).map((()=>Array(o+1).fill(0)));for(let a=0;a<=r;a++)n[a][0]=a;for(let a=0;a<=o;a++)n[0][a]=a;for(let a=1;a<=r;a++)for(let r=1;r<=o;r++){const o=e[a-1]===t[r-1]?0:1;n[a][r]=Math.min(n[a-1][r]+1,n[a][r-1]+1,n[a-1][r-1]+o)}return n[r][o]}function Ue(e){if(!e||0===e.length)return[];const t=new Map,r=[],o=[],n=[];for(const s of e){Le(s.title)?o.push(s):n.push(s)}for(const s of o){const e=Le(s.title);let r=!1;for(const[o,n]of t.entries())if(ke(Oe(e.baseTitle),Oe(o),.7)){n.push(s),r=!0;break}r||t.set(e.baseTitle,[s])}for(const s of n){let e=!1;for(const[r,o]of t.entries()){const t=Oe(s.title),n=Oe(r);if(t.includes(n)||n.includes(t)||ke(t,n,.6)){o.push(s),e=!0;break}}e||r.push(s)}const a=new Map;for(const s of r){const e=Oe(s.title);a.has(e)?a.get(e).push(s):a.set(e,[s])}const i=[];for(const[s,c]of t.entries())c.length>1?(c.sort(((e,t)=>{const r=Le(e.title),o=Le(t.title);return r&&o?r.partNumber-o.partNumber:r?-1:o?1:e.title.localeCompare(t.title)})),i.push({baseTitle:s,videos:c,totalVideos:c.length})):i.push(c[0]);for(const[s,c]of a.entries())c.length>1?i.push({baseTitle:c[0].title,videos:c,totalVideos:c.length}):i.push(c[0]);return i}const xe=async(e,t=0)=>{try{(()=>{const{user:e}=ie.getState();if(!e)throw new Error("Authentication required for thumbnail generation")})()}catch(r){throw r}if(!(()=>{if(!/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))return!0;const e=navigator.deviceMemory;return!(e&&e<2)})())throw new Error("Insufficient device memory for thumbnail generation. Please try uploading without a custom thumbnail.");return new Promise(((o,n)=>{const a=/Android/i.test(navigator.userAgent),i=document.createElement("video");let s;i.preload="metadata",i.muted=!0,i.playsInline=!0,i.crossOrigin="anonymous",a&&(i.controls=!1,i.autoplay=!1,i.style.maxWidth="320px",i.style.maxHeight="240px");try{s=URL.createObjectURL(e),i.src=s}catch(r){return void n(new Error("Failed to create video URL: "+r))}const c=setTimeout((()=>{URL.revokeObjectURL(s),n(new Error("Thumbnail generation timed out"))}),a?15e3:1e4);i.onloadedmetadata=()=>{try{const{user:e}=ie.getState();if(!e)return clearTimeout(c),URL.revokeObjectURL(s),void n(new Error("Authentication lost during thumbnail generation"));t>i.duration&&(t=i.duration/2),a?setTimeout((()=>{try{const{user:e}=ie.getState();if(!e)return clearTimeout(c),URL.revokeObjectURL(s),void n(new Error("Authentication lost during thumbnail generation"));i.currentTime=t}catch(e){clearTimeout(c),URL.revokeObjectURL(s),n(new Error("Failed to seek video on Android: "+e))}}),100):i.currentTime=t}catch(r){clearTimeout(c),URL.revokeObjectURL(s),n(new Error("Failed to seek video: "+r))}},i.onseeked=()=>{try{clearTimeout(c);const{user:e}=ie.getState();if(!e)return URL.revokeObjectURL(s),void n(new Error("Authentication lost during thumbnail generation"));const t=()=>{try{const{user:e}=ie.getState();if(!e)return URL.revokeObjectURL(s),void n(new Error("Authentication lost during thumbnail generation"));const t=document.createElement("canvas"),r=i.videoWidth||640,c=i.videoHeight||480;if(a){const e=800,o=r/c;r>c?(t.width=Math.min(r,e),t.height=t.width/o):(t.height=Math.min(c,e),t.width=t.height*o)}else t.width=r,t.height=c;const u=t.getContext("2d");if(!u)return URL.revokeObjectURL(s),void n(new Error("Failed to get canvas context"));u.drawImage(i,0,0,t.width,t.height);const l=a?.7:.8;t.toBlob((e=>{e?(URL.revokeObjectURL(s),o(e)):(URL.revokeObjectURL(s),n(new Error("Failed to generate thumbnail")))}),"image/jpeg",l)}catch(r){clearTimeout(c),URL.revokeObjectURL(s),n(new Error("Error during thumbnail generation: "+r))}};a?setTimeout(t,100):t()}catch(r){clearTimeout(c),URL.revokeObjectURL(s),n(new Error("Error during thumbnail generation: "+r))}},i.onerror=e=>{clearTimeout(c),URL.revokeObjectURL(s),n(new Error("Error loading video: "+e))};try{i.load()}catch(r){clearTimeout(c),URL.revokeObjectURL(s),n(new Error("Failed to load video: "+r))}}))},$e=async(e,t=3)=>new Promise(((r,o)=>{const n=document.createElement("video");n.preload="metadata",n.muted=!0;const a=URL.createObjectURL(e);n.src=a,n.onloadedmetadata=async()=>{try{const o=[],i=n.duration;for(let r=0;r<t;r++){const n=i*(r+1)/(t+1),a=await xe(e,n);o.push(a)}URL.revokeObjectURL(a),r(o)}catch(i){URL.revokeObjectURL(a),o(i)}},n.onerror=()=>{URL.revokeObjectURL(a),o(new Error("Error loading video"))},n.load()})),Ce=()=>/Android/i.test(navigator.userAgent),je=e=>{const t=Ce(),r=t?62914560:52428800;if(e.size>r)return{isValid:!1,error:`File is too large. Maximum size is ${r/1048576}MB. Your file is ${(e.size/1048576).toFixed(2)}MB.`};const o=["video/mp4","video/webm","video/quicktime","video/x-msvideo","video/3gpp","video/x-ms-wmv","video/avi","video/mov","video/x-flv","video/x-matroska","video/3gp","video/mp2t","video/x-m4v"].includes(e.type)||e.type.startsWith("video/");if(!o&&t){const t=[".mp4",".webm",".mov",".avi",".3gp",".mkv",".flv",".wmv"],r=e.name.toLowerCase();if(!t.some((e=>r.endsWith(e))))return{isValid:!1,error:"Unsupported video format. Please use MP4, WebM, MOV, AVI, or 3GP."}}else if(!o)return{isValid:!1,error:"Unsupported video format. Please use MP4, WebM, MOV, or AVI."};return{isValid:!0}},Te=e=>{const t=Ce();if(e.size>5242880)return{isValid:!1,error:`Thumbnail is too large. Maximum size is 5MB. Your file is ${(e.size/1048576).toFixed(2)}MB.`};const r=["image/jpeg","image/jpg","image/png","image/webp","image/gif"].includes(e.type)||e.type.startsWith("image/");if(!r&&t){const t=[".jpg",".jpeg",".png",".webp",".gif"],r=e.name.toLowerCase();if(!t.some((e=>r.endsWith(e))))return{isValid:!1,error:"Unsupported image format. Please use JPG, PNG, WebP, or GIF."}}else if(!r)return{isValid:!1,error:"Unsupported image format. Please use JPG, PNG, WebP, or GIF."};return{isValid:!0}},Pe=()=>{const e=[];window.File&&window.FileReader&&window.FileList&&window.Blob||e.push("File API");const t=document.createElement("canvas");t.getContext&&t.getContext("2d")||e.push("Canvas 2D");return document.createElement("video").canPlayType||e.push("Video element"),window.URL&&window.URL.createObjectURL||e.push("Object URLs"),{isSupported:0===e.length,missingFeatures:e}},Ie=async(e,t)=>new Promise(((r,o)=>{const{user:n}=ie.getState();if(!n)return void o(new Error("User not authenticated"));let a=!1;const i=((e,t=1e3)=>{let r,o=!1;const n=()=>{r&&clearInterval(r),o=!1};return o||(o=!0,r=setInterval((()=>{const{user:t}=ie.getState();t||(n(),e())}),t)),n})((()=>{a||(a=!0,t?.(),o(new Error("Authentication lost during operation")))}));e().then((e=>{a||(a=!0,i(),r(e))})).catch((e=>{a||(a=!0,i(),o(e))}))})),Ae=async e=>new Promise(((t,r)=>{const{user:o}=ie.getState();o?setTimeout((()=>{const{user:e}=ie.getState();e?t():r(new Error("Authentication lost during delay"))}),e):r(new Error("User not authenticated"))})),Me=()=>{try{const{user:e,profile:t}=ie.getState();if(e&&t){const r={userId:e.id,userEmail:e.email,profileId:t.id,timestamp:Date.now()};localStorage.setItem("auth_backup",JSON.stringify(r))}}catch(e){}};export{Ae as A,Ie as B,$e as C,Ee as D,Q as E,Y as S,oe as a,ae as b,ye as c,be as d,ve as e,re as f,ne as g,we as h,K as i,X as j,Z as k,se as l,ce as m,de as n,ge as o,ue as p,fe as q,Ue as r,me as s,te as t,ie as u,Pe as v,Ce as w,Me as x,je as y,Te as z};
