<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Local Logout Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .logout-btn {
            background: #dc3545;
        }
        .logout-btn:hover {
            background: #c82333;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>🔐 Local Logout Test</h1>
    <p>This page tests the logout functionality locally to debug the issue.</p>

    <div class="test-section">
        <h3>1. Check Current API Configuration</h3>
        <button onclick="checkAPIConfig()">Check API Base URL</button>
        <div id="api-config-result"></div>
    </div>

    <div class="test-section">
        <h3>2. Test Logout API Call</h3>
        <button class="logout-btn" onclick="testLogout()">Test Logout API</button>
        <div id="logout-result"></div>
    </div>

    <div class="test-section">
        <h3>3. Test Local Auth Store</h3>
        <button onclick="testAuthStore()">Test Auth Store Logout</button>
        <div id="auth-store-result"></div>
    </div>

    <div class="test-section">
        <h3>4. Console Logs</h3>
        <div id="console-logs" style="background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; white-space: pre-wrap;"></div>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const logContainer = document.getElementById('console-logs');
        
        function addLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            logContainer.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        console.log = function(...args) {
            addLog('log', args.join(' '));
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addLog('error', args.join(' '));
            originalError.apply(console, args);
        };

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function checkAPIConfig() {
            // This would normally import from the actual API file
            // For now, we'll just show what we know
            const apiBaseUrl = 'https://www.bluefilmx.com/api';
            showResult('api-config-result', 
                `API Base URL: ${apiBaseUrl}<br>` +
                `Current Location: ${window.location.origin}<br>` +
                `Note: API is pointing to production, not local development server`, 
                'info'
            );
        }

        async function testLogout() {
            try {
                console.log('Testing logout API call...');
                
                const response = await fetch('https://www.bluefilmx.com/api/auth.php?action=logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include' // Important for session cookies
                });
                
                console.log('Logout response status:', response.status);
                
                const data = await response.json();
                console.log('Logout response data:', data);
                
                if (response.ok) {
                    showResult('logout-result', 
                        `✅ Logout API call successful<br>` +
                        `Status: ${response.status}<br>` +
                        `Response: ${JSON.stringify(data)}`, 
                        'success'
                    );
                } else {
                    showResult('logout-result', 
                        `❌ Logout API call failed<br>` +
                        `Status: ${response.status}<br>` +
                        `Response: ${JSON.stringify(data)}`, 
                        'error'
                    );
                }
            } catch (error) {
                console.error('Logout API error:', error);
                showResult('logout-result', 
                    `❌ Logout API call error: ${error.message}`, 
                    'error'
                );
            }
        }

        function testAuthStore() {
            // Simulate what the auth store does
            console.log('Testing auth store logout simulation...');
            
            try {
                // Clear localStorage items (like the real auth store does)
                if (typeof window !== 'undefined') {
                    localStorage.removeItem('user-preferences');
                    localStorage.removeItem('upload-progress');
                    console.log('Cleared localStorage items');
                }
                
                // Simulate state clearing
                console.log('Simulated clearing user state');
                
                showResult('auth-store-result', 
                    `✅ Auth store logout simulation completed<br>` +
                    `- Cleared localStorage items<br>` +
                    `- Simulated state clearing`, 
                    'success'
                );
            } catch (error) {
                console.error('Auth store simulation error:', error);
                showResult('auth-store-result', 
                    `❌ Auth store simulation error: ${error.message}`, 
                    'error'
                );
            }
        }

        // Initialize
        console.log('Local logout test page loaded');
        console.log('Current URL:', window.location.href);
    </script>
</body>
</html>