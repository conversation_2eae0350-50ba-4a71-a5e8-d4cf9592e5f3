<?php
// Simple test for favorites API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo json_encode([
        'success' => true,
        'message' => 'Favorites API test successful',
        'method' => $_SERVER['REQUEST_METHOD'],
        'session_id' => session_id(),
        'session_started' => session_status() === PHP_SESSION_ACTIVE
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
