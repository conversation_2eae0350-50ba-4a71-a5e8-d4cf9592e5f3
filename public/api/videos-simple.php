<?php
/**
 * Simple Videos API - Serves videos directly from file system
 * No database required
 */

// Enable CORS with specific origin for credentials support
$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
$allowed_origins = [
    'https://bluefilmx.com',
    'https://www.bluefilmx.com',
    'http://localhost:3000',
    'http://localhost:5173'
];

if (in_array($origin, $allowed_origins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: https://bluefilmx.com');
}

header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * Load real video data from uploaded files
 */
function loadRealVideoData() {
    $videosDir = '../media/videos/';
    $thumbnailsDir = '../media/thumbnails/';
    $baseUrl = 'https://bluefilmx.com/media/';
    
    $videos = [];
    
    if (!is_dir($videosDir)) {
        return $videos;
    }
    
    $videoFiles = array_diff(scandir($videosDir), array('.', '..'));
    $thumbnailFiles = array_diff(scandir($thumbnailsDir), array('.', '..'));

    // Filter out invalid thumbnail files
    $thumbnailFiles = array_filter($thumbnailFiles, function($file) {
        // Skip system files and directories
        if (in_array($file, ['__MACOSX', 'tumbo', '.DS_Store'])) return false;
        if (is_dir('../media/thumbnails/' . $file)) return false;

        // Only include image files
        $ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
        return in_array($ext, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
    });
    
    // Create a mapping of video files to thumbnails
    $thumbnailMap = [];
    foreach ($thumbnailFiles as $thumb) {
        $thumbnailMap[] = $thumb;
    }
    
    $id = 1;
    foreach ($videoFiles as $index => $videoFile) {
        // Skip hidden files and non-video files
        if (strpos($videoFile, '.') === 0) continue;
        
        $videoExt = strtolower(pathinfo($videoFile, PATHINFO_EXTENSION));
        if (!in_array($videoExt, ['mp4', 'mov', 'avi', 'mkv', 'webm'])) continue;
        
        // Get file size for duration estimation (rough estimate)
        $filePath = $videosDir . $videoFile;
        $fileSize = file_exists($filePath) ? filesize($filePath) : 0;
        $estimatedDuration = max(30, min(600, intval($fileSize / 50000))); // Rough estimate
        
        // Generate a clean title from filename
        $title = $videoFile;
        $title = preg_replace('/^\d+-/', '', $title); // Remove timestamp prefix
        $title = preg_replace('/^[a-f0-9-]+_/', '', $title); // Remove UUID prefix
        $title = preg_replace('/\.[^.]+$/', '', $title); // Remove extension
        $title = str_replace(['_', '-'], ' ', $title);
        $title = ucwords(strtolower($title));
        
        // Add some emojis to make titles more appealing
        $emojis = ['🔥', '💦', '🍑', '🍆', '💋', '😈', '🥵', '💕', '🌶️', '⚡'];
        $title .= ' ' . $emojis[array_rand($emojis)];
        
        // Assign thumbnail (cycle through available thumbnails)
        $thumbnailFile = isset($thumbnailMap[$index % count($thumbnailMap)]) ? 
                        $thumbnailMap[$index % count($thumbnailMap)] : 
                        (count($thumbnailMap) > 0 ? $thumbnailMap[0] : null);
        
        $videos[] = [
            'id' => (string)$id,
            'title' => $title,
            'description' => $title,
            'thumbnail_url' => $thumbnailFile ? $baseUrl . 'thumbnails/' . $thumbnailFile : null,
            'video_url' => $baseUrl . 'videos/' . $videoFile,
            'duration' => $estimatedDuration,
            'views' => rand(100, 5000),
            'likes' => rand(10, 200),
            'is_hd' => true,
            'category' => ['hot', 'trending', 'new'][rand(0, 2)],
            'tags' => 'adult, entertainment, exclusive',
            'user_id' => '1',
            'created_at' => date('c', file_exists($filePath) ? filemtime($filePath) : time()),
            'updated_at' => date('c', file_exists($filePath) ? filemtime($filePath) : time()),
            'username' => 'BlueFilmUser',
            'user_avatar' => 'https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=150'
        ];
        
        $id++;
        
        // Limit to first 100 videos for performance
        if ($id > 100) break;
    }
    
    return $videos;
}

// Get request parameters
$id = isset($_GET['id']) ? $_GET['id'] : null;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
$category = isset($_GET['category']) ? $_GET['category'] : null;
$search = isset($_GET['search']) ? $_GET['search'] : null;
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'created_at';
$order = isset($_GET['order']) ? strtoupper($_GET['order']) : 'DESC';

try {
    // Load real video data from your extracted files
    $allVideos = loadRealVideoData();

    // If requesting a single video by ID
    if ($id) {
        $video = null;
        foreach ($allVideos as $v) {
            if ($v['id'] === $id) {
                $video = $v;
                break;
            }
        }

        if ($video) {
            // Convert boolean and numeric fields
            $video['is_hd'] = (bool)$video['is_hd'];
            $video['views'] = (int)$video['views'];
            $video['likes'] = (int)$video['likes'];
            $video['duration'] = (int)$video['duration'];

            echo json_encode([
                'success' => true,
                'data' => $video
            ]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Video not found']);
        }
        exit;
    }

    // Filter by category if specified
    $filteredVideos = $allVideos;
    if ($category && $category !== 'all') {
        $filteredVideos = array_filter($allVideos, function($video) use ($category) {
            return $video['category'] === $category;
        });
    }

    // Filter by search if specified
    if ($search) {
        $filteredVideos = array_filter($filteredVideos, function($video) use ($search) {
            return stripos($video['title'], $search) !== false ||
                   stripos($video['description'], $search) !== false ||
                   stripos($video['tags'], $search) !== false;
        });
    }

    // Sort videos
    if ($sort && in_array($sort, ['created_at', 'views', 'likes', 'title', 'duration'])) {
        usort($filteredVideos, function($a, $b) use ($sort, $order) {
            $valueA = $a[$sort];
            $valueB = $b[$sort];

            if ($sort === 'created_at') {
                $valueA = strtotime($valueA);
                $valueB = strtotime($valueB);
            }

            if ($order === 'DESC') {
                return $valueB <=> $valueA;
            } else {
                return $valueA <=> $valueB;
            }
        });
    }

    $total = count($filteredVideos);
    $offset = ($page - 1) * $limit;
    $videos = array_slice($filteredVideos, $offset, $limit);

    // Convert boolean and numeric fields
    foreach ($videos as &$video) {
        $video['is_hd'] = (bool)$video['is_hd'];
        $video['views'] = (int)$video['views'];
        $video['likes'] = (int)$video['likes'];
        $video['duration'] = (int)$video['duration'];
    }

    echo json_encode([
        'success' => true,
        'data' => [
            'videos' => array_values($videos),
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]
    ]);

} catch (Exception $e) {
    error_log("Error fetching videos: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Failed to fetch videos: ' . $e->getMessage()]);
}
?>
