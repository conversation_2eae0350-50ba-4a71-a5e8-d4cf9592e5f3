{"name": "bluefilmx-streaming-platform", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "dev:auth": "node auth-server.js", "dev:full": "concurrently \"npm run dev\" \"npm run dev:auth\"", "build": "vite build", "postbuild": "cp dist/index.html dist/404.html && npm run generate-version", "generate-version": "node scripts/generate-version.js", "lint": "eslint .", "preview": "vite preview", "vercel-build": "vite build && npm run postbuild", "deploy": "npx vercel --prod", "deploy:namecheap": "./scripts/deploy-namecheap.sh", "deploy:api": "./scripts/deploy-api.sh", "test:api": "./scripts/test-api.sh", "migrate:storage": "node scripts/migrate-storage.js", "test": "vitest run", "clear-cache": "node scripts/clear-cache.js"}, "dependencies": {"@types/better-sqlite3": "^7.6.13", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-window": "^1.8.8", "better-auth": "^1.2.10", "better-sqlite3": "^12.1.0", "concurrently": "^9.2.0", "cors": "^2.8.5", "express": "^5.1.0", "lucide-react": "^0.344.0", "mysql2": "^3.14.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-intersection-observer": "^9.16.0", "react-router-dom": "^6.22.3", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "swr": "^2.3.3", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^24.0.4", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^26.1.0", "postcss": "^8.4.35", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.4.1", "terser": "^5.39.0", "typescript": "^5.8.3", "typescript-eslint": "^8.3.0", "vercel": "^41.6.2", "vite": "^5.4.2", "vitest": "^3.1.3"}}