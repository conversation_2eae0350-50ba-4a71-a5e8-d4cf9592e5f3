import{j as e}from"./react-core-B9nwsbCA.js";import{L as a}from"./router-BDggJ1ol.js";const s=["5ac4509c-ed57-49fc-a160-2ed92f72d95e","ef3f0da2-836d-4370-a3f1-14a4f81950d7","30ac95c9-2e7e-4254-a858-f919fa319799","cbf8d650-bac5-4b50-821e-d6770493fe85","1fbb411b-211a-4344-98a1-c5f43739f643"],t=()=>e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-3xl mx-auto",children:[e.jsx("h1",{className:"text-3xl font-bold mb-6 text-white",children:"Test Video Player"}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6 mb-8",children:[e.jsx("h2",{className:"text-xl font-bold mb-4 text-white",children:"About This Page"}),e.jsx("p",{className:"text-gray-300 mb-4",children:"This page allows you to test the native video player with different videos from the database. The player has been configured to be purely native and support fullscreen functionality."}),e.jsx("p",{className:"text-gray-300 mb-4",children:"Click on any of the video IDs below to test the player with that video."})]}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsx("h2",{className:"text-xl font-bold mb-4 text-white",children:"Available Test Videos"}),e.jsx("div",{className:"grid grid-cols-1 gap-4",children:s.map((s=>e.jsxs(a,{to:`/test-video/${s}`,className:"bg-blue-700 hover:bg-blue-600 text-white px-6 py-4 rounded-lg transition-colors flex items-center justify-between group",children:[e.jsx("span",{className:"font-mono",children:s}),e.jsx("span",{className:"bg-blue-800 group-hover:bg-blue-700 px-3 py-1 rounded-md transition-colors",children:"Test"})]},s)))})]})]})});export{t as default};
