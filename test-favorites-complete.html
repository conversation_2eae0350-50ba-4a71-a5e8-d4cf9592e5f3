<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favorites Complete Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #2a2a2a;
        }
        .success {
            border-color: #4ade80;
            background-color: #1a2e1a;
        }
        .error {
            border-color: #ef4444;
            background-color: #2e1a1a;
        }
        .loading {
            border-color: #fbbf24;
            background-color: #2e2a1a;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
        button:disabled {
            background-color: #6b7280;
            cursor: not-allowed;
        }
        input {
            background-color: #374151;
            color: white;
            border: 1px solid #6b7280;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px;
            width: 300px;
        }
        pre {
            background-color: #111;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .api-test {
            background-color: #374151;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #4ade80; }
        .status-error { background-color: #ef4444; }
        .status-loading { background-color: #fbbf24; }
    </style>
</head>
<body>
    <h1>❤️ Favorites Complete Fix Test</h1>
    <p>This page tests the complete favorites functionality including API and frontend integration.</p>

    <div class="test-section">
        <h3>Step 1: Login Test</h3>
        <input type="text" id="username" placeholder="Username" value="username">
        <input type="password" id="password" placeholder="Password" value="password123">
        <button onclick="testLogin()">Login</button>
        <div id="login-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 2: Test Favorites API</h3>
        <button onclick="testFavoritesAPI()">Test Get Favorites</button>
        <button onclick="testAddFavorite()" id="addFavoriteBtn" disabled>Add Test Favorite</button>
        <button onclick="testRemoveFavorite()" id="removeFavoriteBtn" disabled>Remove Test Favorite</button>
        <div id="api-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 3: Test Frontend Integration</h3>
        <button onclick="testFavoritesPage()">Open Favorites Page</button>
        <button onclick="testFavoriteButtons()">Test Favorite Buttons</button>
        <div id="frontend-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 4: API Test Results</h3>
        <div id="detailed-results"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://www.bluefilmx.com/api';
        let currentUser = null;
        let testVideoId = null;

        function setResult(elementId, message, type = 'loading') {
            const element = document.getElementById(elementId);
            element.className = `test-section ${type}`;
            element.innerHTML = message;
        }

        function addStatusIndicator(status) {
            return `<span class="status-indicator status-${status}"></span>`;
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            setResult('login-result', `${addStatusIndicator('loading')}Logging in as ${username}...`, 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth.php?action=login`, {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (data.success && data.data.user) {
                    currentUser = data.data.user;
                    setResult('login-result', `
                        ${addStatusIndicator('success')}<strong>✅ Login Successful!</strong><br>
                        <strong>User ID:</strong> ${currentUser.id}<br>
                        <strong>Username:</strong> ${currentUser.username}<br>
                        <strong>Approved:</strong> ${currentUser.is_approved ? 'Yes' : 'No'}
                    `, 'success');
                    
                    // Enable API test buttons
                    document.getElementById('addFavoriteBtn').disabled = false;
                    document.getElementById('removeFavoriteBtn').disabled = false;
                    
                    // Get a test video ID
                    await getTestVideoId();
                } else {
                    throw new Error('Login failed: ' + (data.error || 'Unknown error'));
                }

            } catch (error) {
                setResult('login-result', `
                    ${addStatusIndicator('error')}<strong>❌ Login Failed:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        async function getTestVideoId() {
            try {
                const response = await fetch(`${API_BASE_URL}/videos.php?limit=1`, {
                    credentials: 'include',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();
                if (data.success && data.data.videos.length > 0) {
                    testVideoId = data.data.videos[0].id;
                    console.log('Test video ID:', testVideoId);
                }
            } catch (error) {
                console.error('Error getting test video ID:', error);
            }
        }

        async function testFavoritesAPI() {
            setResult('api-result', `${addStatusIndicator('loading')}Testing Favorites API...`, 'loading');
            
            if (!currentUser) {
                setResult('api-result', `${addStatusIndicator('error')}Please login first`, 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/favorites.php`, {
                    credentials: 'include',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();
                
                if (data.success) {
                    setResult('api-result', `
                        ${addStatusIndicator('success')}<strong>✅ Favorites API Working!</strong><br>
                        <strong>Favorites Count:</strong> ${data.data.count}<br>
                        <strong>Videos:</strong> ${data.data.videos.length}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, 'success');
                    
                    updateDetailedResults('get_favorites', true, data);
                } else {
                    throw new Error(data.error || 'API failed');
                }

            } catch (error) {
                setResult('api-result', `
                    ${addStatusIndicator('error')}<strong>❌ Favorites API Failed:</strong><br>
                    ${error.message}
                `, 'error');
                updateDetailedResults('get_favorites', false, { error: error.message });
            }
        }

        async function testAddFavorite() {
            if (!testVideoId) {
                alert('No test video ID available');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/favorites.php`, {
                    method: 'POST',
                    credentials: 'include',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ video_id: testVideoId })
                });

                const data = await response.json();
                updateDetailedResults('add_favorite', data.success, data);
                
                if (data.success) {
                    alert('✅ Video added to favorites!');
                    testFavoritesAPI(); // Refresh the list
                } else {
                    alert('❌ Failed to add favorite: ' + data.error);
                }

            } catch (error) {
                updateDetailedResults('add_favorite', false, { error: error.message });
                alert('❌ Error adding favorite: ' + error.message);
            }
        }

        async function testRemoveFavorite() {
            if (!testVideoId) {
                alert('No test video ID available');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/favorites.php`, {
                    method: 'DELETE',
                    credentials: 'include',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ video_id: testVideoId })
                });

                const data = await response.json();
                updateDetailedResults('remove_favorite', data.success, data);
                
                if (data.success) {
                    alert('✅ Video removed from favorites!');
                    testFavoritesAPI(); // Refresh the list
                } else {
                    alert('❌ Failed to remove favorite: ' + data.error);
                }

            } catch (error) {
                updateDetailedResults('remove_favorite', false, { error: error.message });
                alert('❌ Error removing favorite: ' + error.message);
            }
        }

        function testFavoritesPage() {
            setResult('frontend-result', `${addStatusIndicator('loading')}Opening Favorites page...`, 'loading');
            
            const favoritesWindow = window.open('https://www.bluefilmx.com/favorites', '_blank');
            
            setTimeout(() => {
                setResult('frontend-result', `
                    ${addStatusIndicator('success')}<strong>✅ Favorites Page Opened</strong><br>
                    Check the opened tab for:<br>
                    • No "supabase is not defined" errors<br>
                    • Page loads without crashing<br>
                    • Shows favorites or "No Favorites Yet" message<br>
                    • No JavaScript console errors
                `, 'success');
            }, 1000);
        }

        function testFavoriteButtons() {
            setResult('frontend-result', `
                ${addStatusIndicator('success')}<strong>🔗 Testing Favorite Buttons</strong><br>
                1. Go to any video page<br>
                2. Look for heart/favorite buttons<br>
                3. Try clicking them<br>
                4. Check if they work without errors<br>
                <br>
                <a href="https://www.bluefilmx.com/" target="_blank" style="color: #60a5fa;">Open Homepage to Test</a>
            `, 'success');
        }

        function updateDetailedResults(test, success, data) {
            const resultsDiv = document.getElementById('detailed-results');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultHtml = `
                <div class="api-test">
                    <strong>${addStatusIndicator(success ? 'success' : 'error')}${test.toUpperCase()}</strong> 
                    <span style="color: #9ca3af;">${timestamp}</span><br>
                    <strong>Status:</strong> ${success ? '✅ Success' : '❌ Failed'}<br>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
            
            resultsDiv.innerHTML = resultHtml + resultsDiv.innerHTML;
        }

        // Auto-check auth status on page load
        window.addEventListener('load', () => {
            console.log('🧪 Favorites Complete Fix Test Page Loaded');
            console.log('📋 BuildId: 17497325 - Full favorites functionality');
        });
    </script>
</body>
</html>
