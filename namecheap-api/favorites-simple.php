<?php
require_once 'config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if (!isset($_SESSION)) {
    session_start();
}

$userId = $_SESSION['user_id'] ?? null;
$method = $_SERVER['REQUEST_METHOD'];

// Check authentication
if (!$userId) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Authentication required']);
    exit();
}

// Get database connection
try {
    $database = new Database();
    $db = $database->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit();
}

// Handle different methods
if ($method === 'GET') {
    // Get user favorites
    try {
        $favQuery = "SELECT video_id FROM favorites WHERE user_id = ? ORDER BY created_at DESC";
        $favStmt = $db->prepare($favQuery);
        $favStmt->execute([$userId]);
        $favoriteIds = $favStmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($favoriteIds)) {
            echo json_encode([
                'success' => true,
                'data' => [
                    'videos' => [],
                    'count' => 0
                ]
            ]);
            exit();
        }
        
        // Get video details
        $placeholders = str_repeat('?,', count($favoriteIds) - 1) . '?';
        $query = "
            SELECT 
                v.*,
                p.username,
                p.avatar_url as user_avatar
            FROM videos v
            LEFT JOIN profiles p ON v.user_id = p.id
            WHERE v.id IN ($placeholders)
            ORDER BY v.created_at DESC
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute($favoriteIds);
        $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Convert boolean fields
        foreach ($videos as &$video) {
            $video['is_hd'] = (bool)$video['is_hd'];
            $video['views'] = (int)$video['views'];
            $video['likes'] = (int)$video['likes'];
            $video['duration'] = (int)$video['duration'];
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'videos' => $videos,
                'count' => count($videos)
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to fetch favorites: ' . $e->getMessage()]);
    }
    
} elseif ($method === 'POST') {
    // Add to favorites
    $input = json_decode(file_get_contents('php://input'), true);
    $videoId = $input['video_id'] ?? null;
    
    if (!$videoId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Video ID required']);
        exit();
    }
    
    try {
        // Check if video exists
        $checkVideoQuery = "SELECT id FROM videos WHERE id = ?";
        $checkVideoStmt = $db->prepare($checkVideoQuery);
        $checkVideoStmt->execute([$videoId]);
        
        if (!$checkVideoStmt->fetch()) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Video not found']);
            exit();
        }
        
        // Check if already in favorites
        $checkQuery = "SELECT id FROM favorites WHERE user_id = ? AND video_id = ?";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->execute([$userId, $videoId]);
        
        if ($checkStmt->fetch()) {
            echo json_encode(['success' => true, 'data' => ['message' => 'Video already in favorites']]);
            exit();
        }
        
        // Add to favorites
        $favoriteId = sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
        
        $insertQuery = "INSERT INTO favorites (id, user_id, video_id, created_at) VALUES (?, ?, ?, NOW())";
        $insertStmt = $db->prepare($insertQuery);
        $insertStmt->execute([$favoriteId, $userId, $videoId]);
        
        echo json_encode(['success' => true, 'data' => ['message' => 'Video added to favorites']]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to add to favorites: ' . $e->getMessage()]);
    }
    
} elseif ($method === 'DELETE') {
    // Remove from favorites
    $input = json_decode(file_get_contents('php://input'), true);
    $videoId = $input['video_id'] ?? null;
    
    if (!$videoId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Video ID required']);
        exit();
    }
    
    try {
        $query = "DELETE FROM favorites WHERE user_id = ? AND video_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$userId, $videoId]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode(['success' => true, 'data' => ['message' => 'Video removed from favorites']]);
        } else {
            echo json_encode(['success' => true, 'data' => ['message' => 'Video was not in favorites']]);
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to remove from favorites: ' . $e->getMessage()]);
    }
    
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
}
?>
