<?php
/**
 * Simple Videos API endpoint
 * Handles basic video operations
 */

require_once 'config/database.php';

// Set CORS headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Start session
if (!isset($_SESSION)) {
    session_start();
}

// Helper functions
function getCurrentUser() {
    return $_SESSION['user_id'] ?? null;
}

function requireAuth() {
    $userId = getCurrentUser();
    if (!$userId) {
        http_response_code(401);
        echo json_encode(['success' => false, 'error' => 'Authentication required']);
        exit();
    }
    return $userId;
}

function generateUUID() {
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

// Get database connection
try {
    $database = new Database();
    $db = $database->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

// For testing, allow GET requests
if ($method === 'GET') {
    echo json_encode([
        'success' => true,
        'message' => 'Videos API is working',
        'method' => $method,
        'authenticated' => !empty(getCurrentUser())
    ]);
    exit();
}

if ($method === 'POST') {
    // Create video
    $userId = requireAuth();
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['title']) || !isset($input['video_url'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Title and video URL are required']);
        exit();
    }
    
    try {
        $query = "
            INSERT INTO videos (
                id, title, description, thumbnail_url, video_url, 
                duration, category, user_id, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
        ";
        
        $stmt = $db->prepare($query);
        
        $videoId = generateUUID();
        $stmt->execute([
            $videoId,
            $input['title'],
            $input['description'] ?? null,
            $input['thumbnail_url'] ?? null,
            $input['video_url'],
            $input['duration'] ?? 0,
            $input['category'] ?? 'uncategorized',
            $userId
        ]);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $videoId,
                'message' => 'Video created successfully'
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Error creating video: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to create video: ' . $e->getMessage()]);
    }
    
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
}
?>
