<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Playback Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #2a2a2a;
        }
        .success {
            border-color: #4ade80;
            background-color: #1a2e1a;
        }
        .error {
            border-color: #ef4444;
            background-color: #2e1a1a;
        }
        .loading {
            border-color: #fbbf24;
            background-color: #2e2a1a;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
        video {
            width: 100%;
            max-width: 800px;
            height: auto;
            background-color: #000;
            border-radius: 5px;
        }
        pre {
            background-color: #111;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .video-info {
            background-color: #374151;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .log-error { color: #ef4444; }
        .log-success { color: #4ade80; }
        .log-info { color: #60a5fa; }
    </style>
</head>
<body>
    <h1>🎬 Video Playback Debug Test</h1>
    <p>This page tests video playback issues and diagnoses format/encoding problems.</p>

    <div class="test-section">
        <h3>Step 1: Get Latest Video</h3>
        <button onclick="getLatestVideo()">Get Latest Uploaded Video</button>
        <div id="video-info-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 2: Test Video Accessibility</h3>
        <button onclick="testVideoAccess()" id="testAccessBtn" disabled>Test Video URL Access</button>
        <div id="video-access-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 3: Video Player Test</h3>
        <button onclick="testVideoPlayer()" id="testPlayerBtn" disabled>Test Video Player</button>
        <div id="video-player-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 4: Browser Compatibility</h3>
        <button onclick="testBrowserSupport()">Test Browser Video Support</button>
        <div id="browser-support-result"></div>
    </div>

    <div class="test-section">
        <h3>Step 5: Debug Logs</h3>
        <button onclick="clearLogs()">Clear Logs</button>
        <div id="debug-logs"></div>
    </div>

    <script>
        let currentVideo = null;
        let logs = [];

        function addLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push({ type, message, timestamp });
            updateLogsDisplay();
        }

        function updateLogsDisplay() {
            const container = document.getElementById('debug-logs');
            const recentLogs = logs.slice(-20);
            
            container.innerHTML = recentLogs.map(log => 
                `<div class="log-entry log-${log.type}">[${log.timestamp}] ${log.message}</div>`
            ).join('');
        }

        function clearLogs() {
            logs = [];
            updateLogsDisplay();
        }

        function setResult(elementId, message, type = 'loading') {
            const element = document.getElementById(elementId);
            element.className = `test-section ${type}`;
            element.innerHTML = message;
        }

        async function getLatestVideo() {
            setResult('video-info-result', 'Getting latest uploaded video...', 'loading');
            addLog('info', 'Fetching latest video from API');
            
            try {
                const response = await fetch('https://www.bluefilmx.com/api/videos.php?limit=1', {
                    credentials: 'include',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();
                addLog('info', `API response: ${JSON.stringify(data).substring(0, 100)}...`);
                
                if (data.success && data.data.videos && data.data.videos.length > 0) {
                    currentVideo = data.data.videos[0];
                    
                    setResult('video-info-result', `
                        <strong>✅ Latest Video Found!</strong><br>
                        <div class="video-info">
                            <strong>Title:</strong> ${currentVideo.title}<br>
                            <strong>Video URL:</strong> ${currentVideo.video_url}<br>
                            <strong>Thumbnail URL:</strong> ${currentVideo.thumbnail_url || 'None'}<br>
                            <strong>Duration:</strong> ${currentVideo.duration || 0} seconds<br>
                            <strong>Views:</strong> ${currentVideo.views}<br>
                            <strong>Created:</strong> ${currentVideo.created_at}<br>
                            <strong>User:</strong> ${currentVideo.username || 'Unknown'}
                        </div>
                    `, 'success');
                    
                    // Enable next test
                    document.getElementById('testAccessBtn').disabled = false;
                    document.getElementById('testPlayerBtn').disabled = false;
                    
                    addLog('success', `Video loaded: ${currentVideo.title}`);
                } else {
                    throw new Error('No videos found in API response');
                }

            } catch (error) {
                addLog('error', `Failed to get video: ${error.message}`);
                setResult('video-info-result', `
                    <strong>❌ Failed to Get Video:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        async function testVideoAccess() {
            if (!currentVideo) {
                alert('Please get a video first');
                return;
            }

            setResult('video-access-result', 'Testing video URL accessibility...', 'loading');
            addLog('info', `Testing video URL: ${currentVideo.video_url}`);
            
            try {
                // Test HEAD request to check if video is accessible
                const response = await fetch(currentVideo.video_url, {
                    method: 'HEAD',
                    mode: 'cors'
                });

                addLog('info', `Video URL response: ${response.status} ${response.statusText}`);
                
                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                });

                if (response.ok) {
                    setResult('video-access-result', `
                        <strong>✅ Video URL Accessible!</strong><br>
                        <div class="video-info">
                            <strong>Status:</strong> ${response.status} ${response.statusText}<br>
                            <strong>Content-Type:</strong> ${headers['content-type'] || 'Not specified'}<br>
                            <strong>Content-Length:</strong> ${headers['content-length'] ? (parseInt(headers['content-length']) / 1024 / 1024).toFixed(2) + ' MB' : 'Not specified'}<br>
                            <strong>Accept-Ranges:</strong> ${headers['accept-ranges'] || 'Not specified'}<br>
                            <strong>Cache-Control:</strong> ${headers['cache-control'] || 'Not specified'}<br>
                            <strong>CORS Headers:</strong> ${headers['access-control-allow-origin'] || 'None detected'}
                        </div>
                        <pre>${JSON.stringify(headers, null, 2)}</pre>
                    `, 'success');
                    
                    addLog('success', 'Video URL is accessible');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                addLog('error', `Video access failed: ${error.message}`);
                setResult('video-access-result', `
                    <strong>❌ Video URL Not Accessible:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        async function testVideoPlayer() {
            if (!currentVideo) {
                alert('Please get a video first');
                return;
            }

            setResult('video-player-result', 'Testing video player...', 'loading');
            addLog('info', 'Creating video player element');
            
            try {
                const videoElement = document.createElement('video');
                videoElement.controls = true;
                videoElement.preload = 'metadata';
                videoElement.style.width = '100%';
                videoElement.style.maxWidth = '600px';
                videoElement.style.height = 'auto';

                let loadSuccess = false;
                let errorMessage = '';

                // Set up event listeners
                videoElement.addEventListener('loadedmetadata', () => {
                    addLog('success', `Video metadata loaded - Duration: ${videoElement.duration}s, Video size: ${videoElement.videoWidth}x${videoElement.videoHeight}`);
                    loadSuccess = true;
                });

                videoElement.addEventListener('canplay', () => {
                    addLog('success', 'Video can start playing');
                });

                videoElement.addEventListener('error', (e) => {
                    const error = videoElement.error;
                    let errorText = 'Unknown error';
                    
                    if (error) {
                        switch (error.code) {
                            case error.MEDIA_ERR_ABORTED:
                                errorText = 'Video playback aborted';
                                break;
                            case error.MEDIA_ERR_NETWORK:
                                errorText = 'Network error during video loading';
                                break;
                            case error.MEDIA_ERR_DECODE:
                                errorText = 'Video format not supported by this browser';
                                break;
                            case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                                errorText = 'Video source not supported';
                                break;
                        }
                        errorText += ` (Code: ${error.code})`;
                    }
                    
                    errorMessage = errorText;
                    addLog('error', `Video error: ${errorText}`);
                });

                // Set the video source
                videoElement.src = currentVideo.video_url;
                addLog('info', `Set video source: ${currentVideo.video_url}`);

                // Wait for load or error
                await new Promise((resolve) => {
                    setTimeout(() => {
                        if (loadSuccess) {
                            setResult('video-player-result', `
                                <strong>✅ Video Player Working!</strong><br>
                                <div class="video-info">
                                    <strong>Duration:</strong> ${videoElement.duration}s<br>
                                    <strong>Dimensions:</strong> ${videoElement.videoWidth}x${videoElement.videoHeight}<br>
                                    <strong>Ready State:</strong> ${videoElement.readyState}<br>
                                    <strong>Network State:</strong> ${videoElement.networkState}
                                </div>
                                <br>
                                ${videoElement.outerHTML}
                            `, 'success');
                        } else {
                            setResult('video-player-result', `
                                <strong>❌ Video Player Failed:</strong><br>
                                <strong>Error:</strong> ${errorMessage || 'Video failed to load within timeout'}<br>
                                <strong>Ready State:</strong> ${videoElement.readyState}<br>
                                <strong>Network State:</strong> ${videoElement.networkState}<br>
                                <br>
                                <strong>Attempted Video Element:</strong><br>
                                ${videoElement.outerHTML}
                            `, 'error');
                        }
                        resolve();
                    }, 5000); // 5 second timeout
                });

            } catch (error) {
                addLog('error', `Video player test failed: ${error.message}`);
                setResult('video-player-result', `
                    <strong>❌ Video Player Test Failed:</strong><br>
                    ${error.message}
                `, 'error');
            }
        }

        function testBrowserSupport() {
            setResult('browser-support-result', 'Testing browser video format support...', 'loading');
            addLog('info', 'Testing browser video format support');
            
            const video = document.createElement('video');
            const formats = [
                { type: 'video/mp4; codecs="avc1.42E01E"', name: 'MP4 (H.264)' },
                { type: 'video/mp4; codecs="avc1.4D401E"', name: 'MP4 (H.264 Main)' },
                { type: 'video/mp4; codecs="avc1.64001E"', name: 'MP4 (H.264 High)' },
                { type: 'video/webm; codecs="vp8"', name: 'WebM (VP8)' },
                { type: 'video/webm; codecs="vp9"', name: 'WebM (VP9)' },
                { type: 'video/ogg; codecs="theora"', name: 'Ogg (Theora)' },
                { type: 'video/mp4', name: 'MP4 (Generic)' },
                { type: 'video/webm', name: 'WebM (Generic)' }
            ];

            let supportInfo = '<strong>📋 Browser Video Format Support:</strong><br><br>';
            
            formats.forEach(format => {
                const support = video.canPlayType(format.type);
                let supportText = '';
                let supportClass = '';
                
                switch (support) {
                    case 'probably':
                        supportText = '✅ Probably';
                        supportClass = 'log-success';
                        break;
                    case 'maybe':
                        supportText = '⚠️ Maybe';
                        supportClass = 'log-info';
                        break;
                    default:
                        supportText = '❌ No';
                        supportClass = 'log-error';
                        break;
                }
                
                supportInfo += `<span class="${supportClass}">${format.name}: ${supportText}</span><br>`;
                addLog(support === 'probably' ? 'success' : support === 'maybe' ? 'info' : 'error', 
                      `${format.name}: ${supportText}`);
            });

            // Browser info
            supportInfo += '<br><strong>🌐 Browser Information:</strong><br>';
            supportInfo += `<strong>User Agent:</strong> ${navigator.userAgent}<br>`;
            supportInfo += `<strong>Platform:</strong> ${navigator.platform}<br>`;
            
            setResult('browser-support-result', supportInfo, 'success');
        }

        // Auto-start tests
        window.addEventListener('load', () => {
            addLog('info', '🧪 Video Playback Debug Test Page Loaded');
            setTimeout(() => {
                getLatestVideo();
            }, 500);
            setTimeout(() => {
                testBrowserSupport();
            }, 1000);
        });
    </script>
</body>
</html>
