<?php
// Step-by-step debug for favorites API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Credentials: true');

$debug = [];

try {
    $debug['step1'] = 'Headers set';
    
    // Test session
    if (!isset($_SESSION)) {
        session_start();
    }
    $debug['step2'] = 'Session started';
    $debug['session_id'] = session_id();
    $debug['session_data'] = $_SESSION ?? [];
    
    // Test database connection
    require_once 'config/database.php';
    $debug['step3'] = 'Database config loaded';
    
    $database = new Database();
    $db = $database->getConnection();
    $debug['step4'] = 'Database connection created';
    
    // Test simple query
    $testQuery = "SELECT COUNT(*) as count FROM profiles";
    $testStmt = $db->prepare($testQuery);
    $testStmt->execute();
    $result = $testStmt->fetch();
    $debug['step5'] = 'Database query successful';
    $debug['profile_count'] = $result['count'];
    
    // Test auth
    $userId = $_SESSION['user_id'] ?? null;
    $debug['step6'] = 'Auth check complete';
    $debug['user_id'] = $userId;
    $debug['is_authenticated'] = !empty($userId);
    
    if ($userId) {
        // Test favorites table
        $favQuery = "SELECT COUNT(*) as count FROM favorites WHERE user_id = ?";
        $favStmt = $db->prepare($favQuery);
        $favStmt->execute([$userId]);
        $favResult = $favStmt->fetch();
        $debug['step7'] = 'Favorites query successful';
        $debug['user_favorites_count'] = $favResult['count'];
    } else {
        $debug['step7'] = 'Skipped (not authenticated)';
        $debug['user_favorites_count'] = 'N/A';
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'All steps completed successfully',
        'debug' => $debug
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug' => $debug,
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
