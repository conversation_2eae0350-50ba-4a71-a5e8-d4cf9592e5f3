<?php
/**
 * Rate Limiter
 * Provides rate limiting functionality to prevent abuse
 */

class RateLimiter {
    private $db;
    private $defaultLimits = [
        'login' => ['attempts' => 5, 'window' => 900], // 5 attempts per 15 minutes
        'register' => ['attempts' => 3, 'window' => 3600], // 3 attempts per hour
        'password_reset' => ['attempts' => 3, 'window' => 3600], // 3 attempts per hour
        'email_verification' => ['attempts' => 5, 'window' => 3600], // 5 attempts per hour
        'api' => ['attempts' => 100, 'window' => 3600] // 100 requests per hour
    ];
    
    public function __construct($database) {
        $this->db = $database;
        $this->createRateLimitTable();
    }
    
    /**
     * Create rate limit table if it doesn't exist
     */
    private function createRateLimitTable() {
        $sql = "
            CREATE TABLE IF NOT EXISTS rate_limits (
                id INT AUTO_INCREMENT PRIMARY KEY,
                identifier VARCHAR(255) NOT NULL,
                action VARCHAR(50) NOT NULL,
                attempts INT DEFAULT 1,
                window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_identifier_action (identifier, action),
                INDEX idx_window_start (window_start)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $this->db->exec($sql);
    }
    
    /**
     * Check if action is allowed for identifier
     */
    public function isAllowed($identifier, $action, $customLimits = null) {
        $limits = $customLimits ?: ($this->defaultLimits[$action] ?? $this->defaultLimits['api']);
        
        // Clean old entries
        $this->cleanOldEntries($action, $limits['window']);
        
        // Get current attempts
        $stmt = $this->db->prepare("
            SELECT attempts, window_start 
            FROM rate_limits 
            WHERE identifier = ? AND action = ? 
            AND window_start > DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        
        $stmt->execute([$identifier, $action, $limits['window']]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            return true; // No previous attempts
        }
        
        return $result['attempts'] < $limits['attempts'];
    }
    
    /**
     * Record an attempt
     */
    public function recordAttempt($identifier, $action) {
        $stmt = $this->db->prepare("
            INSERT INTO rate_limits (identifier, action, attempts, window_start) 
            VALUES (?, ?, 1, NOW()) 
            ON DUPLICATE KEY UPDATE 
                attempts = attempts + 1,
                last_attempt = NOW()
        ");
        
        return $stmt->execute([$identifier, $action]);
    }
    
    /**
     * Get remaining attempts
     */
    public function getRemainingAttempts($identifier, $action, $customLimits = null) {
        $limits = $customLimits ?: ($this->defaultLimits[$action] ?? $this->defaultLimits['api']);
        
        $stmt = $this->db->prepare("
            SELECT attempts 
            FROM rate_limits 
            WHERE identifier = ? AND action = ? 
            AND window_start > DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        
        $stmt->execute([$identifier, $action, $limits['window']]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            return $limits['attempts'];
        }
        
        return max(0, $limits['attempts'] - $result['attempts']);
    }
    
    /**
     * Get time until reset
     */
    public function getTimeUntilReset($identifier, $action, $customLimits = null) {
        $limits = $customLimits ?: ($this->defaultLimits[$action] ?? $this->defaultLimits['api']);
        
        $stmt = $this->db->prepare("
            SELECT window_start 
            FROM rate_limits 
            WHERE identifier = ? AND action = ? 
            AND window_start > DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        
        $stmt->execute([$identifier, $action, $limits['window']]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            return 0;
        }
        
        $windowStart = new DateTime($result['window_start']);
        $windowEnd = $windowStart->add(new DateInterval('PT' . $limits['window'] . 'S'));
        $now = new DateTime();
        
        return max(0, $windowEnd->getTimestamp() - $now->getTimestamp());
    }
    
    /**
     * Reset attempts for identifier and action
     */
    public function resetAttempts($identifier, $action) {
        $stmt = $this->db->prepare("
            DELETE FROM rate_limits 
            WHERE identifier = ? AND action = ?
        ");
        
        return $stmt->execute([$identifier, $action]);
    }
    
    /**
     * Clean old entries
     */
    private function cleanOldEntries($action, $window) {
        $stmt = $this->db->prepare("
            DELETE FROM rate_limits 
            WHERE action = ? AND window_start < DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        
        $stmt->execute([$action, $window]);
    }
    
    /**
     * Get client identifier (IP + User Agent hash)
     */
    public static function getClientIdentifier() {
        $ip = self::getClientIP();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        return hash('sha256', $ip . '|' . $userAgent);
    }
    
    /**
     * Get client IP address
     */
    public static function getClientIP() {
        $headers = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];
        
        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Check if IP is whitelisted
     */
    public function isWhitelisted($ip) {
        $whitelist = [
            '127.0.0.1',
            '::1',
            // Add your trusted IPs here
        ];
        
        return in_array($ip, $whitelist);
    }
    
    /**
     * Block IP address
     */
    public function blockIP($ip, $duration = 3600) {
        $stmt = $this->db->prepare("
            INSERT INTO rate_limits (identifier, action, attempts, window_start) 
            VALUES (?, 'blocked', 999999, DATE_SUB(NOW(), INTERVAL ? SECOND))
            ON DUPLICATE KEY UPDATE 
                attempts = 999999,
                window_start = DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        
        return $stmt->execute([$ip, -$duration, -$duration]);
    }
    
    /**
     * Check if IP is blocked
     */
    public function isBlocked($ip) {
        $stmt = $this->db->prepare("
            SELECT attempts 
            FROM rate_limits 
            WHERE identifier = ? AND action = 'blocked' 
            AND window_start > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        
        $stmt->execute([$ip]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result && $result['attempts'] > 1000;
    }
    
    /**
     * Get rate limit info for response headers
     */
    public function getRateLimitHeaders($identifier, $action, $customLimits = null) {
        $limits = $customLimits ?: ($this->defaultLimits[$action] ?? $this->defaultLimits['api']);
        $remaining = $this->getRemainingAttempts($identifier, $action, $customLimits);
        $resetTime = time() + $this->getTimeUntilReset($identifier, $action, $customLimits);
        
        return [
            'X-RateLimit-Limit' => $limits['attempts'],
            'X-RateLimit-Remaining' => $remaining,
            'X-RateLimit-Reset' => $resetTime,
            'X-RateLimit-Window' => $limits['window']
        ];
    }
}
?>