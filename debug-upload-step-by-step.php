<?php
// Step-by-step debug for upload API
$debug = [];

try {
    $debug['step1'] = 'Starting upload debug';
    
    // Test headers
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
    header('Access-Control-Allow-Credentials: true');
    $debug['step2'] = 'Headers set';
    
    // Test session
    if (!isset($_SESSION)) {
        session_start();
    }
    $debug['step3'] = 'Session started';
    $debug['session_id'] = session_id();
    $debug['session_data'] = $_SESSION ?? [];
    
    // Test method
    $method = $_SERVER['REQUEST_METHOD'];
    $debug['step4'] = 'Method checked: ' . $method;
    
    // Test auth
    $userId = $_SESSION['user_id'] ?? null;
    $debug['step5'] = 'Auth checked';
    $debug['user_id'] = $userId;
    $debug['is_authenticated'] = !empty($userId);
    
    // Test file upload (if POST)
    if ($method === 'POST') {
        $debug['step6'] = 'POST request detected';
        $debug['files'] = $_FILES ?? [];
        $debug['post'] = $_POST ?? [];
    } else {
        $debug['step6'] = 'Non-POST request';
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Upload debug completed successfully',
        'debug' => $debug
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug' => $debug,
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
