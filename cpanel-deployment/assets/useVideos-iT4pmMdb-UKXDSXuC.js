import{V as a,h as t}from"./main-Dmn9Lr65.js";function e(e="all",o=1,s=20){const[i,n]=a.useState([]),[r,l]=a.useState(!0),[c,u]=a.useState(null),[d,g]=a.useState({currentPage:1,totalPages:1,totalCount:0,pageSize:s}),f=a.useCallback((async()=>{try{l(!0),u(null);const a=await t.getVideos({page:o,limit:s,category:"all"===e||""===e?void 0:e,sort:"created_at",order:"DESC"});if(!a.success||!a.data.videos)throw new Error("Failed to fetch videos");n(a.data.videos),g({currentPage:a.data.pagination.page,totalPages:a.data.pagination.pages,totalCount:a.data.pagination.total,pageSize:a.data.pagination.limit})}catch(a){const t=a instanceof Error?a.message:"Failed to fetch videos";u(t)}finally{l(!1)}}),[e,o,s]);return a.useEffect((()=>{f()}),[f]),{videos:i,isLoading:r,error:c,pagination:d,refetch:a.useCallback((()=>{f()}),[f])}}function o(e){const[o,s]=a.useState(null),[i,n]=a.useState(!0),[r,l]=a.useState(null),c=a.useCallback((async()=>{if(e)try{n(!0),l(null);const a=await t.getVideo(e);if(!a.success||!a.data)throw new Error("Video not found");s(a.data)}catch(a){const t=a instanceof Error?a.message:"Failed to fetch video";l(t)}finally{n(!1)}}),[e]);return a.useEffect((()=>{c()}),[c]),{video:o,isLoading:i,error:r,refetch:a.useCallback((()=>{c()}),[c])}}export{e,o};
