import mysql from 'mysql2/promise';

// Create MySQL database connection
export const db = mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASS || '',
  database: process.env.DB_NAME || 'bluefilmx_db',
  charset: 'utf8mb4'
});

// Create tables for BetterAuth
export async function initializeDatabase() {
  const connection = await db;
  
  // Users table
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS user (
      id VARCHAR(255) PRIMARY KEY,
      email VARCHAR(255) UNIQUE NOT NULL,
      emailVerified BOOLEAN NOT NULL DEFAULT FALSE,
      name VA<PERSON>HA<PERSON>(255),
      createdAt BIGINT NOT NULL,
      updatedAt BIGINT NOT NULL,
      image TEXT
    )
  `);

  // Accounts table for OAuth providers
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS account (
      id VARCHAR(255) PRIMARY KEY,
      accountId VARCHAR(255) NOT NULL,
      providerId VARCHAR(255) NOT NULL,
      userId VARCHAR(255) NOT NULL,
      accessToken TEXT,
      refreshToken TEXT,
      idToken TEXT,
      accessTokenExpiresAt BIGINT,
      refreshTokenExpiresAt BIGINT,
      scope TEXT,
      password VARCHAR(255),
      createdAt BIGINT NOT NULL,
      updatedAt BIGINT NOT NULL,
      FOREIGN KEY (userId) REFERENCES user (id) ON DELETE CASCADE
    )
  `);

  // Sessions table
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS session (
      id VARCHAR(255) PRIMARY KEY,
      expiresAt BIGINT NOT NULL,
      token VARCHAR(255) UNIQUE NOT NULL,
      createdAt BIGINT NOT NULL,
      updatedAt BIGINT NOT NULL,
      ipAddress VARCHAR(45),
      userAgent TEXT,
      userId VARCHAR(255) NOT NULL,
      FOREIGN KEY (userId) REFERENCES user (id) ON DELETE CASCADE
    )
  `);

  // Verification table for email verification
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS verification (
      id VARCHAR(255) PRIMARY KEY,
      identifier VARCHAR(255) NOT NULL,
      value VARCHAR(255) NOT NULL,
      expiresAt BIGINT NOT NULL,
      createdAt BIGINT,
      updatedAt BIGINT
    )
  `);

  console.log('Database initialized successfully');
}

// Initialize database on import
initializeDatabase().catch(console.error);