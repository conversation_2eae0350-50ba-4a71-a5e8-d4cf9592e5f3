<?php
/**
 * Manual Deployment Script for Admin System
 * Copy this entire script and save it as 'manual-deploy.php' on your server
 * Then run: php manual-deploy.php
 */

// Database configuration - UPDATE THESE VALUES
$host = 'localhost';
$dbname = 'bluerpcm_bluefilm';
$username = 'bluerpcm_dbuser';
$password = 'your_database_password_here'; // UPDATE THIS!

echo "🚀 Starting Admin System Deployment...\n\n";

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ Database connection successful\n";
    
    // Start transaction
    $pdo->beginTransaction();
    
    // 1. Add admin fields to profiles table
    echo "📝 Adding admin fields to profiles table...\n";
    
    // Check if columns already exist
    $stmt = $pdo->query("SHOW COLUMNS FROM profiles LIKE 'is_admin'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("ALTER TABLE profiles ADD COLUMN is_admin BOOLEAN DEFAULT FALSE");
        echo "   ✅ Added is_admin column\n";
    } else {
        echo "   ℹ️  is_admin column already exists\n";
    }
    
    $stmt = $pdo->query("SHOW COLUMNS FROM profiles LIKE 'is_approved'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("ALTER TABLE profiles ADD COLUMN is_approved BOOLEAN DEFAULT FALSE");
        echo "   ✅ Added is_approved column\n";
    } else {
        echo "   ℹ️  is_approved column already exists\n";
    }
    
    // 2. Create admin_counter table
    echo "📝 Creating admin_counter table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_counter (
            id INT PRIMARY KEY DEFAULT 1,
            count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Reset admin counter
    $pdo->exec("DELETE FROM admin_counter");
    $pdo->exec("INSERT INTO admin_counter (id, count) VALUES (1, 0)");
    echo "   ✅ Admin counter reset to 0\n";
    
    // 3. Clear all existing data
    echo "🗑️  Clearing existing data...\n";
    
    // Clear only user account tables, preserve all other data
    $tables_to_clear = [
        'verification',
        'session',
        'account',
        'profiles',
        'user'
    ];
    
    foreach ($tables_to_clear as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            
            if ($count > 0) {
                $pdo->exec("DELETE FROM $table");
                echo "   ✅ Cleared $table ($count records)\n";
            } else {
                echo "   ℹ️  $table was already empty\n";
            }
        } catch (Exception $e) {
            echo "   ⚠️  Could not clear $table: " . $e->getMessage() . "\n";
        }
    }
    
    // Commit transaction
    $pdo->commit();
    
    echo "\n✅ Admin system setup completed successfully!\n\n";
    
    // Display system status
    echo "📋 System Configuration:\n";
    echo "   • First 2 signups will automatically become admins\n";
    echo "   • All subsequent signups require admin approval\n";
    echo "   • All existing accounts have been cleared\n\n";
    
    // Show current stats
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM user");
    $userCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM profiles WHERE is_admin = 1");
    $adminCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM profiles WHERE is_approved = 0");
    $pendingCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT count FROM admin_counter WHERE id = 1");
    $adminCounterResult = $stmt->fetch();
    $adminCounterValue = $adminCounterResult ? $adminCounterResult['count'] : 0;
    
    echo "📊 Current System Status:\n";
    echo "   • Total Users: $userCount\n";
    echo "   • Admins: $adminCount\n";
    echo "   • Pending Approval: $pendingCount\n";
    echo "   • Admin Counter: $adminCounterValue\n\n";
    
    echo "🎉 Ready for new signups! The first 2 users will become admins automatically.\n";
    
} catch (Exception $e) {
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollback();
    }
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "\n💡 Troubleshooting:\n";
    echo "   1. Check database credentials at the top of this script\n";
    echo "   2. Ensure database user has proper permissions\n";
    echo "   3. Check if database '$dbname' exists\n";
    exit(1);
}

echo "\n🔧 Next Steps:\n";
echo "   1. Have your first admin register at: https://bluefilmx.com\n";
echo "   2. Have your second admin register\n";
echo "   3. Test with a third user (should require approval)\n";
echo "   4. Access admin panel to manage users\n\n";

echo "✨ Deployment complete!\n";
?>