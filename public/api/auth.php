<?php
/**
 * Simple Authentication API for Namecheap Static Hosting
 * Works with MySQL database
 */

// Enable CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once 'config.php';

// Get database connection
$pdo = getDbConnection();
if (!$pdo) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit();
}

// Get request method and action
$method = $_SERVER['REQUEST_METHOD'];

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true) ?? [];

// Get action from URL parameter or request body
$action = $_GET['action'] ?? $input['action'] ?? '';

// Debug logging (remove in production)
error_log("Auth API - Method: $method, Action: $action, Input: " . json_encode($input));

// Route requests
switch ($method) {
    case 'POST':
        handlePostRequest($pdo, $action, $input);
        break;
    case 'GET':
        handleGetRequest($pdo, $action);
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        break;
}

function handlePostRequest($pdo, $action, $input) {
    switch ($action) {
        case 'signin':
        case 'login':
            signIn($pdo, $input);
            break;
        case 'signup':
        case 'register':
            signUp($pdo, $input);
            break;
        case 'signout':
        case 'logout':
            signOut($pdo, $input);
            break;
        default:
            error_log("Auth API - Invalid POST action: '$action'");
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => "Invalid action: '$action'"]);
            break;
    }
}

function handleGetRequest($pdo, $action) {
    switch ($action) {
        case 'session':
            getSession($pdo);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
            break;
    }
}

function signIn($pdo, $input) {
    $email = $input['email'] ?? '';
    $password = $input['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Email and password are required']);
        return;
    }
    
    try {
        // Find user by email
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user || !password_verify($password, $user['password_hash'])) {
            http_response_code(401);
            echo json_encode(['success' => false, 'error' => 'Invalid credentials']);
            return;
        }
        
        // Generate session token
        $token = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', strtotime('+7 days'));
        
        // Store session
        $stmt = $pdo->prepare("INSERT INTO user_sessions (user_id, token, expires_at) VALUES (?, ?, ?)");
        $stmt->execute([$user['id'], $token, $expiresAt]);
        
        // Return success response
        echo json_encode([
            'success' => true,
            'token' => $token,
            'user' => [
                'id' => $user['id'],
                'email' => $user['email'],
                'name' => $user['name'],
                'emailVerified' => (bool)$user['email_verified'],
                'createdAt' => $user['created_at'],
                'updatedAt' => $user['updated_at']
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Sign in failed']);
    }
}

function signUp($pdo, $input) {
    $email = $input['email'] ?? '';
    $password = $input['password'] ?? '';
    $username = $input['username'] ?? '';
    $firstName = $input['first_name'] ?? '';
    $lastName = $input['last_name'] ?? '';

    // Create display name from first/last name or use username
    $name = trim($firstName . ' ' . $lastName) ?: $username;

    if (empty($email) || empty($password) || empty($username)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Email, password, and username are required']);
        return;
    }
    
    try {
        // Check if user already exists (email or username)
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? OR username = ?");
        $stmt->execute([$email, $username]);
        if ($stmt->fetch()) {
            http_response_code(409);
            echo json_encode(['success' => false, 'error' => 'User with this email or username already exists']);
            return;
        }
        
        // Create user
        $userId = 'user_' . uniqid();
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        $now = date('Y-m-d H:i:s');
        
        $stmt = $pdo->prepare("INSERT INTO users (id, username, email, name, password_hash, email_verified, created_at, updated_at) VALUES (?, ?, ?, ?, ?, 0, ?, ?)");
        $stmt->execute([$userId, $username, $email, $name, $passwordHash, $now, $now]);
        
        // Generate session token
        $token = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', strtotime('+7 days'));
        
        // Store session
        $stmt = $pdo->prepare("INSERT INTO user_sessions (user_id, token, expires_at) VALUES (?, ?, ?)");
        $stmt->execute([$userId, $token, $expiresAt]);
        
        // Return success response
        echo json_encode([
            'success' => true,
            'token' => $token,
            'user' => [
                'id' => $userId,
                'username' => $username,
                'email' => $email,
                'name' => $name,
                'emailVerified' => false,
                'createdAt' => $now,
                'updatedAt' => $now
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Sign up failed']);
    }
}

function signOut($pdo, $input) {
    $token = $input['token'] ?? '';
    
    if (empty($token)) {
        echo json_encode(['success' => true]); // Already signed out
        return;
    }
    
    try {
        // Delete session
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE token = ?");
        $stmt->execute([$token]);
        
        echo json_encode(['success' => true]);
    } catch (Exception $e) {
        echo json_encode(['success' => true]); // Don't fail on signout
    }
}

function getSession($pdo) {
    $token = $_GET['token'] ?? '';
    
    if (empty($token)) {
        http_response_code(401);
        echo json_encode(['success' => false, 'error' => 'No token provided']);
        return;
    }
    
    try {
        // Find valid session
        $stmt = $pdo->prepare("
            SELECT s.*, u.* 
            FROM user_sessions s 
            JOIN users u ON s.user_id = u.id 
            WHERE s.token = ? AND s.expires_at > NOW()
        ");
        $stmt->execute([$token]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            http_response_code(401);
            echo json_encode(['success' => false, 'error' => 'Invalid or expired session']);
            return;
        }
        
        echo json_encode([
            'success' => true,
            'user' => [
                'id' => $result['id'],
                'email' => $result['email'],
                'name' => $result['name'],
                'emailVerified' => (bool)$result['email_verified'],
                'createdAt' => $result['created_at'],
                'updatedAt' => $result['updated_at']
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Session validation failed']);
    }
}
?>
