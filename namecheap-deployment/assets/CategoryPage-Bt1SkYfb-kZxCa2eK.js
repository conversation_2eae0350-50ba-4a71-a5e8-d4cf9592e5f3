import{q as e,K as s,w as t,V as a,J as r,X as o,p as i,H as l,k as d}from"./main-Dmn9Lr65.js";import{e as n}from"./useVideos-iT4pmMdb-UKXDSXuC.js";import{i as c}from"./VideoGrid-Bp65TBX0-K1IpAx6B.js";const m=()=>{const{slug:m}=e(),x=s(),[g,h]=t(),b=parseInt(g.get("page")||"1"),[j,p]=a.useState(b);a.useEffect((()=>{const e=parseInt(g.get("page")||"1");e!==j&&p(e)}),[g,j]);const{videos:u,pagination:v,isLoading:N,error:f}=n(m||"",j,24),{getCategoryBySlug:y,fetchCategories:w}=r();a.useEffect((()=>{w()}),[w]);const C=m?y(m):void 0,P=()=>{x("/",{replace:!0})};return f&&!u.length?o.jsx("div",{className:"container mx-auto px-4 py-16",children:o.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto",children:[o.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Error Loading Videos"}),o.jsx("p",{className:"text-red-200",children:f})]})}):o.jsx("div",{className:"pb-10",children:o.jsxs("div",{className:"container mx-auto px-4",children:[o.jsxs("div",{className:"flex items-center mb-6 pt-4",children:[o.jsx("button",{onClick:P,className:"mr-4 p-2 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors","aria-label":"Go back",children:o.jsx(i,{size:20,className:"text-white"})}),o.jsx("h1",{className:"text-2xl font-bold text-white",children:C?C.name:"Category Not Found"})]}),o.jsx("div",{className:"md:hidden my-4",children:o.jsx("div",{className:"relative search-bar-container px-1",children:o.jsx(l,{className:"w-full mobile-search-bar homepage-search"})})}),f&&o.jsxs("div",{className:"bg-red-500/20 border border-red-500 text-white p-4 rounded-lg mb-6",children:[o.jsx("h3",{className:"font-bold mb-2",children:"Error Loading Videos"}),o.jsx("p",{children:f})]}),!N&&u.length>0&&o.jsxs("div",{className:"flex justify-between items-center mb-4 text-sm text-gray-400",children:[o.jsxs("div",{children:["Showing ",24*(j-1)+1,"-",Math.min(24*j,v.totalCount)," of ",v.totalCount," videos"]}),o.jsxs("div",{children:["Page ",j," of ",v.totalPages]})]}),o.jsx("div",{className:"mt-6",children:o.jsx(c,{title:`${C?.name||"Category"} Videos`,videos:u,onVideoClick:e=>{x(`/video/${e.id}`)},isLoading:N,className:"mb-8"})}),!N&&u.length>0&&v.totalPages>1&&o.jsx("div",{className:"mt-12 mb-8",children:o.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[o.jsxs("div",{className:"text-center text-gray-400 text-sm",children:["Page ",j," of ",v.totalPages," • ",v.totalCount," total videos"]}),o.jsx(d,{currentPage:v.currentPage,totalPages:v.totalPages,onPageChange:e=>{p(e),h({page:e.toString()}),window.scrollTo({top:0,behavior:"smooth"})},className:"justify-center"})]})}),!N&&0===u.length&&!f&&o.jsxs("div",{className:"text-center py-16",children:[o.jsx("div",{className:"text-gray-400 text-lg mb-4",children:"No videos found in this category"}),o.jsx("p",{className:"text-gray-500 text-sm mb-4",children:"Try browsing other categories or check back later!"}),o.jsx("button",{onClick:P,className:"px-6 py-2 bg-blue-700 text-white rounded-lg hover:bg-blue-600 transition-colors",children:"Back to Home"})]})]})})};export{m as default};
