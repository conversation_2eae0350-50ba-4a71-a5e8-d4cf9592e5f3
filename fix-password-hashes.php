<?php
/**
 * Fix Password Hashes for Existing Users
 * This script adds password hashes to users who don't have them
 */

require_once 'namecheap-api/config/database.php';

$database = new Database();
$db = $database->getConnection();

// Define default passwords for existing users
$userPasswords = [
    'username' => 'password123',
    'jayliinight' => 'jaylii123'
];

try {
    echo "Checking and fixing password hashes...\n";
    
    // Get all users without password hashes
    $query = "SELECT id, username, password_hash FROM profiles WHERE password_hash IS NULL OR password_hash = ''";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "All users already have password hashes.\n";
        exit(0);
    }
    
    echo "Found " . count($users) . " users without password hashes:\n";
    
    foreach ($users as $user) {
        echo "- {$user['username']} (ID: {$user['id']})\n";
        
        // Get password for this user
        $password = $userPasswords[$user['username']] ?? null;
        
        if (!$password) {
            echo "  WARNING: No default password defined for {$user['username']}, skipping...\n";
            continue;
        }
        
        // Generate password hash
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        
        // Update user with password hash
        $updateQuery = "UPDATE profiles SET password_hash = :password_hash WHERE id = :id";
        $updateStmt = $db->prepare($updateQuery);
        $updateStmt->bindParam(':password_hash', $passwordHash);
        $updateStmt->bindParam(':id', $user['id']);
        
        if ($updateStmt->execute()) {
            echo "  ✅ Updated password hash for {$user['username']}\n";
        } else {
            echo "  ❌ Failed to update password hash for {$user['username']}\n";
        }
    }
    
    echo "\nPassword hash fix completed!\n";
    echo "\nYou can now login with these credentials:\n";
    foreach ($userPasswords as $username => $password) {
        echo "- Username: {$username}, Password: {$password}\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>