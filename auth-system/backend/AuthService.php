<?php
/**
 * Enhanced Authentication Service
 * Provides secure user authentication with JWT tokens, email verification,
 * password reset, and comprehensive security features
 */

require_once __DIR__ . '/utils/JWTHelper.php';
require_once __DIR__ . '/utils/EmailService.php';
require_once __DIR__ . '/utils/SecurityHelper.php';

class AuthService {
    private $db;
    private $jwtHelper;
    private $emailService;
    private $securityHelper;
    
    // Security constants
    private const MAX_LOGIN_ATTEMPTS = 5;
    private const LOCKOUT_DURATION = 900; // 15 minutes
    private const SESSION_LIFETIME = 86400; // 24 hours
    private const TOKEN_EXPIRY = 3600; // 1 hour for verification/reset tokens
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->jwtHelper = new JWTHelper();
        $this->emailService = new EmailService();
        $this->securityHelper = new SecurityHelper();
    }
    
    /**
     * Register a new user
     */
    public function register(array $userData): array {
        try {
            // Validate input
            $validation = $this->validateRegistrationData($userData);
            if (!$validation['valid']) {
                return $this->errorResponse($validation['errors']);
            }
            
            // Check if user already exists
            if ($this->userExists($userData['email'], $userData['username'])) {
                return $this->errorResponse('User with this email or username already exists');
            }
            
            // Create user (let database auto-increment the ID)
            $passwordHash = password_hash($userData['password'], PASSWORD_ARGON2ID);
            
            $stmt = $this->db->prepare("
                INSERT INTO users (username, email, password_hash, first_name, last_name) 
                VALUES (?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $userData['username'],
                $userData['email'],
                $passwordHash,
                $userData['first_name'] ?? null,
                $userData['last_name'] ?? null
            ]);
            
            $userId = $this->db->lastInsertId();
            
            // Assign default user role
            $this->assignUserRole($userId, 'user');
            
            // Send email verification (temporarily disabled for now)
            // $verificationToken = $this->createEmailVerificationToken($userId);
            // $this->emailService->sendVerificationEmail($userData['email'], $userData['username'], $verificationToken);
            
            // Get user data
            $user = $this->getUserById($userId);
            
            return $this->successResponse([
                'user' => $this->sanitizeUserData($user),
                'message' => 'Registration successful. Please check your email to verify your account.'
            ]);
            
        } catch (Exception $e) {
            error_log('Registration error: ' . $e->getMessage());
            return $this->errorResponse('Registration failed. Please try again.');
        }
    }
    
    /**
     * Authenticate user login
     */
    public function login(string $identifier, string $password, array $deviceInfo = []): array {
        try {
            // Find user by email or username
            $user = $this->getUserByIdentifier($identifier);
            
            if (!$user) {
                return $this->errorResponse('Invalid credentials');
            }
            
            // Check if account is locked
            if ($this->isAccountLocked($user)) {
                return $this->errorResponse('Account is temporarily locked due to too many failed login attempts');
            }
            
            // Verify password
            if (!password_verify($password, $user['password_hash'])) {
                $this->recordFailedLogin($user['id']);
                return $this->errorResponse('Invalid credentials');
            }
            
            // Check if account is active
            if (!$user['is_active']) {
                return $this->errorResponse('Account is deactivated');
            }
            
            // Reset failed login attempts
            $this->resetFailedLoginAttempts($user['id']);
            
            // Update last login
            $this->updateLastLogin($user['id'], $deviceInfo['ip'] ?? null);
            
            // Create session
            $sessionId = $this->createUserSession($user['id'], $deviceInfo);
            
            // Generate JWT token
            $token = $this->jwtHelper->generateToken([
                'user_id' => $user['id'],
                'session_id' => $sessionId,
                'roles' => $this->getUserRoles($user['id'])
            ]);
            
            return $this->successResponse([
                'user' => $this->sanitizeUserData($user),
                'token' => $token,
                'session_id' => $sessionId,
                'expires_in' => self::SESSION_LIFETIME
            ]);
            
        } catch (Exception $e) {
            error_log('Login error: ' . $e->getMessage());
            return $this->errorResponse('Login failed. Please try again.');
        }
    }
    
    /**
     * Logout user
     */
    public function logout(string $sessionId): array {
        try {
            $stmt = $this->db->prepare("UPDATE user_sessions SET is_active = FALSE WHERE id = ?");
            $stmt->execute([$sessionId]);
            
            return $this->successResponse(['message' => 'Logged out successfully']);
            
        } catch (Exception $e) {
            error_log('Logout error: ' . $e->getMessage());
            return $this->errorResponse('Logout failed');
        }
    }
    
    /**
     * Verify email address
     */
    public function verifyEmail(string $token): array {
        try {
            $stmt = $this->db->prepare("
                SELECT user_id FROM email_verification_tokens 
                WHERE token = ? AND expires_at > NOW() AND used_at IS NULL
            ");
            $stmt->execute([$token]);
            $tokenData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$tokenData) {
                return $this->errorResponse('Invalid or expired verification token');
            }
            
            // Mark email as verified
            $this->db->beginTransaction();
            
            $stmt = $this->db->prepare("
                UPDATE users SET is_verified = TRUE, email_verified_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$tokenData['user_id']]);
            
            $stmt = $this->db->prepare("
                UPDATE email_verification_tokens SET used_at = NOW() 
                WHERE token = ?
            ");
            $stmt->execute([$token]);
            
            $this->db->commit();
            
            return $this->successResponse(['message' => 'Email verified successfully']);
            
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('Email verification error: ' . $e->getMessage());
            return $this->errorResponse('Email verification failed');
        }
    }
    
    /**
     * Request password reset
     */
    public function requestPasswordReset(string $email): array {
        try {
            $user = $this->getUserByEmail($email);
            
            if (!$user) {
                // Don't reveal if email exists
                return $this->successResponse(['message' => 'If the email exists, a reset link has been sent']);
            }
            
            $resetToken = $this->createPasswordResetToken($user['id']);
            $this->emailService->sendPasswordResetEmail($email, $user['username'], $resetToken);
            
            return $this->successResponse(['message' => 'If the email exists, a reset link has been sent']);
            
        } catch (Exception $e) {
            error_log('Password reset request error: ' . $e->getMessage());
            return $this->errorResponse('Password reset request failed');
        }
    }
    
    /**
     * Reset password with token
     */
    public function resetPassword(string $token, string $newPassword): array {
        try {
            $stmt = $this->db->prepare("
                SELECT user_id FROM password_reset_tokens 
                WHERE token = ? AND expires_at > NOW() AND used_at IS NULL
            ");
            $stmt->execute([$token]);
            $tokenData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$tokenData) {
                return $this->errorResponse('Invalid or expired reset token');
            }
            
            // Validate new password
            if (!$this->isValidPassword($newPassword)) {
                return $this->errorResponse('Password does not meet security requirements');
            }
            
            $passwordHash = password_hash($newPassword, PASSWORD_ARGON2ID);
            
            $this->db->beginTransaction();
            
            // Update password
            $stmt = $this->db->prepare("UPDATE users SET password_hash = ? WHERE id = ?");
            $stmt->execute([$passwordHash, $tokenData['user_id']]);
            
            // Mark token as used
            $stmt = $this->db->prepare("UPDATE password_reset_tokens SET used_at = NOW() WHERE token = ?");
            $stmt->execute([$token]);
            
            // Invalidate all user sessions
            $stmt = $this->db->prepare("UPDATE user_sessions SET is_active = FALSE WHERE user_id = ?");
            $stmt->execute([$tokenData['user_id']]);
            
            $this->db->commit();
            
            return $this->successResponse(['message' => 'Password reset successfully']);
            
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('Password reset error: ' . $e->getMessage());
            return $this->errorResponse('Password reset failed');
        }
    }
    
    /**
     * Get current user from session
     */
    public function getCurrentUser(string $sessionId): ?array {
        try {
            $stmt = $this->db->prepare("
                SELECT u.*, s.expires_at, s.last_activity 
                FROM users u 
                JOIN user_sessions s ON u.id = s.user_id 
                WHERE s.id = ? AND s.is_active = TRUE AND s.expires_at > NOW()
            ");
            $stmt->execute([$sessionId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                // Update last activity
                $stmt = $this->db->prepare("UPDATE user_sessions SET last_activity = NOW() WHERE id = ?");
                $stmt->execute([$sessionId]);
                
                return $this->sanitizeUserData($user);
            }
            
            return null;
            
        } catch (Exception $e) {
            error_log('Get current user error: ' . $e->getMessage());
            return null;
        }
    }
    
    // Private helper methods
    
    private function validateRegistrationData(array $data): array {
        $errors = [];
        
        if (empty($data['username']) || strlen($data['username']) < 3) {
            $errors[] = 'Username must be at least 3 characters long';
        }
        
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Invalid email address';
        }
        
        if (!$this->isValidPassword($data['password'])) {
            $errors[] = 'Password must be at least 8 characters with uppercase, lowercase, number, and special character';
        }
        
        return ['valid' => empty($errors), 'errors' => $errors];
    }
    
    private function isValidPassword(string $password): bool {
        return strlen($password) >= 8 &&
               preg_match('/[A-Z]/', $password) &&
               preg_match('/[a-z]/', $password) &&
               preg_match('/[0-9]/', $password) &&
               preg_match('/[^A-Za-z0-9]/', $password);
    }
    
    private function userExists(string $email, string $username): bool {
        $stmt = $this->db->prepare("SELECT id FROM users WHERE email = ? OR username = ?");
        $stmt->execute([$email, $username]);
        return $stmt->fetch() !== false;
    }
    
    private function generateUUID(): string {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
    
    private function sanitizeUserData(array $user): array {
        unset($user['password_hash']);
        return $user;
    }
    
    private function successResponse(array $data): array {
        return ['success' => true, 'data' => $data];
    }
    
    private function errorResponse(string $message): array {
        return ['success' => false, 'error' => $message];
    }
    
    private function getUserByIdentifier(string $identifier): ?array {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE email = ? OR username = ?");
        $stmt->execute([$identifier, $identifier]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }
    
    private function getUserByEmail(string $email): ?array {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$email]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }
    
    private function isAccountLocked(array $user): bool {
        // Check if account is locked due to failed login attempts
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as attempts 
            FROM login_attempts 
            WHERE user_id = ? AND created_at > datetime('now', '-15 minutes')
        ");
        $stmt->execute([$user['id']]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['attempts'] >= self::MAX_LOGIN_ATTEMPTS;
    }
    
    private function createUserSession(string $userId, array $deviceInfo = []): string {
        $sessionId = $this->generateUUID();
        $expiresAt = date('Y-m-d H:i:s', time() + self::SESSION_LIFETIME);
        $now = date('Y-m-d H:i:s');
        
        $stmt = $this->db->prepare("
            INSERT INTO user_sessions (id, user_id, expires_at, device_info, created_at, last_activity) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$sessionId, $userId, $expiresAt, json_encode($deviceInfo), $now, $now]);
        
        return $sessionId;
    }
    
    private function recordLoginAttempt(string $userId, bool $success, array $deviceInfo = []): void {
        $stmt = $this->db->prepare("
            INSERT INTO login_attempts (user_id, success, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $userId, 
            $success ? 1 : 0, 
            $deviceInfo['ip'] ?? '', 
            $deviceInfo['user_agent'] ?? '',
            date('Y-m-d H:i:s')
        ]);
    }
    
    private function resetFailedLoginAttempts($userId): void {
        // Reset failed login attempts for successful login
        $stmt = $this->db->prepare("UPDATE users SET failed_login_attempts = 0, locked_until = NULL WHERE id = ?");
        $stmt->execute([$userId]);
    }
    
    private function updateLastLogin($userId): void {
        // Update last login timestamp
        $stmt = $this->db->prepare("UPDATE users SET last_login_at = ? WHERE id = ?");
        $stmt->execute([date('Y-m-d H:i:s'), $userId]);
    }
    
    private function getUserRoles($userId): array {
        // Get user roles from user_roles and roles tables
        $stmt = $this->db->prepare("
            SELECT r.name, r.permissions 
            FROM user_roles ur 
            JOIN roles r ON ur.role_id = r.id 
            WHERE ur.user_id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function validateToken(string $token): array {
        try {
            $decoded = $this->jwtHelper->validateToken($token);
            if (!$decoded) {
                return $this->errorResponse('Invalid token');
            }
            
            $user = $this->getUserById($decoded['user_id']);
            if (!$user) {
                return $this->errorResponse('User not found');
            }
            
            return $this->successResponse(['user' => $this->sanitizeUserData($user)]);
            
        } catch (Exception $e) {
            error_log('Token validation error: ' . $e->getMessage());
            return $this->errorResponse('Token validation failed');
        }
    }
    
    private function getUserById($userId): ?array {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }
    
    private function assignUserRole($userId, string $roleName): void {
        // Get role ID by name
        $stmt = $this->db->prepare("SELECT id FROM roles WHERE name = ?");
        $stmt->execute([$roleName]);
        $role = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($role) {
            // Assign role to user using integer ID
            $stmt = $this->db->prepare("INSERT OR IGNORE INTO user_roles (user_id, role_id) VALUES (?, ?)");
            $stmt->execute([$userId, $role['id']]);
        }
    }
    
    private function createEmailVerificationToken(string $userId): string {
        $token = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', time() + 24 * 3600); // 24 hours
        
        $stmt = $this->db->prepare("
            INSERT INTO email_verifications (user_id, token, expires_at) 
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$userId, $token, $expiresAt]);
        
        return $token;
    }
}